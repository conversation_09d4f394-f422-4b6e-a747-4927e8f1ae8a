# NeonRush Game Documentation

## Overview
NeonRush is a Flutter-based mobile game featuring neon-themed visuals, token-based progression, and diverse gameplay mechanics. The game combines fast-paced action with strategic power-up usage and adaptive difficulty systems.

## Core Game Structure

### Level System
- **Total Levels**: 10 core levels + <PERSON> fights every 10th level
- **Level Progression**: Linear unlock system where completing a level unlocks the next
- **Boss Fights**: Special encounters every 10 levels (10, 20, 30, etc.) with unique mechanics
- **Difficulty Scaling**: Easy → Medium → Hard → Expert progression

### Game Modes (Core Levels)

#### 1. Tap (Level 1) - "Neon Touch"
- **Objective**: Tap glowing dots to score points
- **Goal**: Score 20 points in 30 seconds
- **Mechanics**: 
  - Targets fall from top of screen with gravity
  - Background tap penalty: -15 score with red particle effects
  - Red penalty targets (20% spawn chance): -15 score when tapped, explosion effects
  - Target timeout: 2 seconds
  - Spawn rate: 800ms intervals
- **Theme**: Cyber Blue
- **Rewards**: 100 XP, 25 tokens

#### 2. Swipe (Level 2) - "Neon Swipe"
- **Objective**: Swipe objects away from edges
- **Goal**: Prevent 15 objects from hitting edges in 45 seconds
- **Mechanics**: Object speed 100px/s, spawn rate 2.0s, swipe threshold 50px
- **Theme**: Neon Pink
- **Rewards**: 120 XP, 30 tokens

#### 3. Hold (Level 3) - "Neon Hold"
- **Objective**: Hold down on glowing area without releasing
- **Goal**: Hold for 45 seconds total in 60 seconds
- **Mechanics**: Hold threshold 0.8, glow pulse rate 1.5s, penalty time 2.0s
- **Theme**: Electric Green
- **Rewards**: 150 XP, 35 tokens

#### 4. Avoid (Level 4) - "Neon Dodge"
- **Objective**: Drag orb to avoid enemies
- **Goal**: Survive 50 seconds in 60 seconds
- **Mechanics**: Enemy speed 150px/s, spawn rate 1.0s, player size 40px, enemy size 30px
- **Theme**: Fire Orange
- **Rewards**: 180 XP, 40 tokens

#### 5. Memory (Level 5) - "Neon Memory"
- **Objective**: Repeat color sequences
- **Goal**: Complete 8 sequences in 90 seconds
- **Mechanics**: Sequence length 3, show time 1.0s, max sequence length 6
- **Theme**: Purple Haze
- **Rewards**: 200 XP, 45 tokens

#### 6. Reaction (Level 6) - "Neon Reaction"
- **Objective**: Tap only when signal is green
- **Goal**: 25 correct reactions in 60 seconds
- **Mechanics**: Wait time 1.0-4.0s, reaction window 0.5s
- **Theme**: Electric Green
- **Rewards**: 220 XP, 50 tokens

#### 7. Drag (Level 7) - "Neon Drag"
- **Objective**: Drag orbs to matching targets
- **Goal**: Complete 20 successful drags in 75 seconds
- **Mechanics**: Target tolerance 30px, 3 orbs, time limit 5.0s
- **Theme**: Neon Pink
- **Rewards**: 250 XP, 55 tokens

#### 8. Spin (Level 8) - "Neon Spin"
- **Objective**: Rotate rings to match gaps
- **Goal**: Complete 15 alignments in 90 seconds
- **Mechanics**: 3 rings, rotation speed 90°/s, gap size 60px
- **Theme**: Gold Rush
- **Rewards**: 280 XP, 60 tokens

#### 9. Deflect (Level 9) - "Neon Deflect"
- **Objective**: Drag shield to block lasers
- **Goal**: Deflect 30 laser beams in 75 seconds
- **Mechanics**: Laser speed 200px/s, shield size 80px, spawn rate 1.5s
- **Theme**: Ice Blue
- **Rewards**: 320 XP, 65 tokens

#### 10. Survive (Level 10) - "Neon Chaos"
- **Objective**: Survive pure chaos
- **Goal**: Survive 60 seconds
- **Mechanics**: Chaos intensity 1.0, 5 effects, survival bonus 10
- **Theme**: Rainbow
- **Rewards**: 500 XP, 100 tokens

## Boss Fight System

### Boss Fight Mechanics
- **Trigger**: Every 10th level completion
- **Duration**: 1.5 minutes (90 seconds)
- **Stages**: 3 progressive stages with increasing difficulty
  - Stage 1: 150 taps required, boss size 200px
  - Stage 2: 200 taps required, boss size 150px  
  - Stage 3: 250 taps required, boss size 100px
- **Penalty System**: Red areas spawn every 3 seconds
  - Tapping red areas: -5 seconds time penalty
  - Red areas last 2 seconds, 100px diameter
- **Scoring**: (Total taps × 10) + (Time remaining × 5)
- **Rewards**: Score converted to tokens via lucky wheel

### Boss Fight Progression
- Boss shrinks with each stage (harder to hit)
- Tap requirements increase per stage
- Red penalty areas provide strategic challenge
- Pulsing/scaling animations for visual feedback

## Power-Up System

### Available Power-Ups
1. **Slow Motion** (50 tokens)
   - Duration: 5 seconds
   - Effect: Slows all targets by 50%
   - Color: Cyan

2. **Neon Bomb** (75 tokens)
   - Duration: 500ms
   - Effect: Clears all targets on screen
   - Explosion radius: 200px
   - Color: Magenta

3. **Shield** (30 tokens)
   - Duration: 1 minute
   - Effect: Prevents one failure/hit
   - Color: Green

4. **Magnet Touch** (60 tokens)
   - Duration: 10 seconds
   - Effect: 2x tap hitbox, 50px magnet range
   - Color: Gold

5. **Time Boost** (40 tokens)
   - Duration: Instant
   - Effect: Adds 5 seconds to timer
   - Color: Orange

## Token Economy & Rewards

### Token Sources
- Level completion: 25-100 tokens (varies by level)
- Daily bonuses: Available through login streaks
- Box selection rewards: 1, 5, 10, or 20 tokens (replaces spin wheel)
- Boss fight completion: Score-based token conversion

### Token Costs
- Power-ups: 30-75 tokens each
- Bonus games: 100-300 tokens to unlock permanently

### Box Selection System
After level completion, players choose from 4 boxes containing:
- Box rewards: 1, 5, 10, or 20 tokens
- Replaces previous lucky spin wheel mechanic
- Visual feedback with neon styling

## Bonus Games System

### Available Bonus Games (Token-Purchasable)
1. **Neon Snake** - 100 tokens
2. **Neon Pong** - 150 tokens  
3. **Astro Blaster** - 200 tokens
4. **Crate Smash** - 250 tokens
5. **Glow Flow** - 300 tokens

### Unlock System
- Purchase with tokens for permanent access
- No longer unlocked by completing all standard levels
- Visual indicators show unlock status and affordability
- Success dialogs confirm purchases

## Visual Effects & Themes

### Level-Specific Themes (Cycling every 10 levels)
1. Electric Green (Levels 1-10)
2. Cyber Blue (Levels 11-20)
3. Neon Pink (Levels 21-30)
4. Fire Orange (Levels 31-40)
5. Purple Haze (Levels 41-50)
6. Gold Rush (Levels 51-60)
7. Ice Blue (Levels 61-70)
8. Crimson Red (Levels 71-80)
9. Silver Storm (Levels 81-90)
10. Rainbow (Levels 91-100)

### Particle Effects
- **Background Tap**: Red particles when tapping empty areas
- **Red Target Explosion**: Enhanced explosion effects for penalty targets
- **Target Hit**: Standard particle effects for successful taps
- **Power-up Activation**: Specialized effects per power-up type

### Animated Elements
- **Main Menu**: Animated glowing background with dynamic effects
- **Play Button**: Pulsing, scaling, and shimmer animations
- **Boss Fights**: Pulsing boss with scaling animations
- **Targets**: Falling motion with gravity simulation
- **UI Elements**: Neon glow effects throughout interface

## Progression Systems

### Player Progression
- **XP System**: 1000 XP per player level
- **Starting Tokens**: 100 tokens for new players
- **Level Unlocking**: Sequential unlock system

### Combo System
- **Combo Window**: 1 second between taps
- **Max Combo Level**: 10
- **Base Multiplier**: 1.0
- **Multiplier Increment**: 0.2 per combo level
- **Combo Threshold**: 5 taps minimum

### Adaptive Difficulty
- Adjusts based on player performance
- Modifies spawn rates and target behavior
- Maintains challenge without frustration

## Audio System
- **Menu Music**: 'Menu Theme.wav' for background
- **Game Music**: 'Game theme 1.wav' during gameplay
- **Sound Effects**: Haptic feedback integration
- **Theme-Specific**: Different sound sets per neon theme

## Technical Architecture
- **Framework**: Flutter with Dart
- **State Management**: Provider pattern
- **Performance**: Object pooling for targets
- **Persistence**: SharedPreferences for game data
- **Animations**: AnimationController with TickerProviderStateMixin
- **Timer Systems**: 60fps update loops for smooth gameplay
