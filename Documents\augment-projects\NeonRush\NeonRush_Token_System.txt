NEONRUSH TOKEN SYSTEM DOCUMENTATION
=====================================

OVERVIEW
--------
The NeonRush token system is the primary in-game currency that enables players to purchase power-ups, unlock bonus games, and enhance their gameplay experience. Tokens are earned through various gameplay activities and spent on strategic advantages and content unlocks.

STARTING TOKENS
---------------
- New players begin with: 100 tokens
- This provides enough currency to purchase 1-3 power-ups initially
- Allows players to experiment with different power-ups early in the game

TOKEN SOURCES (How to Earn Tokens)
----------------------------------

1. LEVEL COMPLETION REWARDS
   - Base reward range: 25-100 tokens per level
   - Specific level rewards:
     * Level 1 (Neon Touch): 25 tokens
     * Level 2 (Neon Swipe): 30 tokens  
     * Level 3 (Neon Hold): 35 tokens
     * Level 4 (Neon Avoid): 40 tokens
     * Level 5 (Neon Memory): 45 tokens
     * Level 6 (Neon Reaction): 50 tokens
     * Level 7 (Neon Drag): 55 tokens
     * Level 8 (Neon Spin): 60 tokens
     * Level 9 (Neon Deflect): 80 tokens
     * Level 10 (Neon Chaos): 100 tokens
   - Higher levels provide progressively more tokens
   - Rewards scale with difficulty and complexity

2. BOX SELECTION SYSTEM
   - Triggered after each level completion
   - Players choose from 4 mystery boxes
   - Box rewards: 1, 5, 10, or 20 tokens
   - Replaces the previous lucky spin wheel mechanic
   - Features neon-styled visual feedback
   - Adds element of chance and excitement to rewards

3. BOSS FIGHT COMPLETION
   - Triggered every 10th level completion
   - Score-based token conversion system
   - Scoring formula: (Total taps × 10) + (Time remaining × 5)
   - Higher scores = more tokens through conversion
   - Provides significant token bonuses for skilled players

4. DAILY BONUSES
   - Available through login streaks
   - Encourages regular gameplay
   - Specific amounts not detailed in current implementation

5. CRATE REWARDS (Future System)
   - Basic Crates: 10-50 tokens
   - Advanced Crates: 50-150 tokens  
   - Epic Crates: 100-300 tokens
   - Currently defined but not fully implemented

TOKEN SPENDING (How to Use Tokens)
----------------------------------

1. POWER-UP PURCHASES
   Power-ups provide temporary advantages during gameplay:

   a) Shield - 30 tokens
      - Duration: 1 minute
      - Effect: Prevents one failure or hit
      - Color: Green
      - Most affordable defensive option

   b) Time Boost - 40 tokens  
      - Duration: Instant effect
      - Effect: Adds 5 seconds to level timer
      - Color: Orange
      - Quick time extension for tight situations

   c) Slow Motion - 50 tokens
      - Duration: 5 seconds
      - Effect: Slows all targets by 50%
      - Color: Cyan
      - Excellent for precision-required levels

   d) Magnet Touch - 60 tokens
      - Duration: 10 seconds  
      - Effect: 2x tap hitbox + 50px magnet range
      - Color: Gold
      - Improves accuracy and hit detection

   e) Neon Bomb - 75 tokens
      - Duration: 500ms
      - Effect: Clears all targets on screen
      - Color: Magenta
      - Most expensive but powerful screen clear

2. BONUS GAME UNLOCKS
   Permanent purchases for additional game modes:

   a) Neon Snake - 100 tokens
      - Classic snake game with neon styling
      - Permanent unlock after purchase

   b) Neon Pong - 150 tokens
      - Neon-themed pong variant
      - Mid-tier bonus game

   c) Astro Blaster - 200 tokens
      - Space shooting game
      - Higher-tier entertainment

   d) Crate Smash - 250 tokens
      - Destruction-based mini-game
      - Premium bonus content

   e) Glow Flow - 300 tokens
      - Most expensive bonus game
      - Premium entertainment option

PLAYER PROFILE INTEGRATION
--------------------------
- Tokens are stored in the PlayerProfile model
- Automatic validation prevents spending more tokens than available
- Token transactions update the lastPlayedAt timestamp
- Supports adding and spending tokens with error handling
- Integrates with power-up inventory system

POWER-UP INVENTORY SYSTEM
-------------------------
- Players can own multiple quantities of each power-up
- Power-ups are consumed when used (quantity decreases)
- Inventory tracks acquisition dates
- Automatic cleanup when quantities reach zero
- Supports stacking multiple purchases of the same power-up

ECONOMIC BALANCE
----------------
- Total tokens from all 10 levels: ~525 tokens (base rewards only)
- Additional tokens from box selections: 40-200 tokens (estimated)
- Boss fight bonuses: Variable based on performance
- Power-up costs range: 30-75 tokens each
- Bonus game costs range: 100-300 tokens each
- System encourages strategic spending and replay value

PROGRESSION STRATEGY
-------------------
Early Game (Levels 1-3):
- Focus on learning mechanics with starting tokens
- Purchase affordable power-ups (Shield, Time Boost)
- Save tokens for more expensive power-ups

Mid Game (Levels 4-7):
- Invest in Slow Motion and Magnet Touch for difficult levels
- Begin saving for first bonus game unlock
- Use box selection rewards strategically

Late Game (Levels 8-10):
- Purchase Neon Bomb for challenging final levels
- Unlock bonus games for additional content
- Maximize boss fight scores for token bonuses

TECHNICAL IMPLEMENTATION
------------------------
- Constants defined in GameConstants class
- Token costs are centrally managed and easily adjustable
- Player profile handles all token transactions
- Error handling prevents invalid token operations
- Integration with game state management system

FUTURE ENHANCEMENTS
-------------------
- Daily bonus system implementation
- Crate opening mechanics
- Token multiplier events
- Achievement-based token rewards
- Seasonal token bonuses
- Premium token packages (if monetization added)

This token system creates a balanced economy that rewards skilled play, encourages progression, and provides meaningful choices for players in how they spend their earned currency.
