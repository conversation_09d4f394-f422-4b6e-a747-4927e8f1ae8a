TAPVERSE GAMES COMPREHENSIVE LIST
==================================

This document contains all games in TapVerse with their mechanics, objectives, controls, and implementation details.

=== BREAKFINITY GAME ===

BREAKFINITY
Mechanic: Idle/progressive tapping game with persistent save state
Objective: Break through layers by tapping, discover hidden rewards
Controls: Tap to break sections
Scoring: Progressive layer completion, hidden reward discovery
Special Features: Animated backgrounds, particle effects, next layer visibility, reward popups
Reward System: Common (20%) 1 token, Uncommon (10%) 10 tokens, Rare (5%) 20 tokens, Very Rare (1%) theme unlock or 3 keys
Layer Styles: 1000+ layers with specific visual themes and effects for each range
Code Location: Breakfinity game implementation

=== BONUS GAMES (15 Total) ===

1. NEON SNAKE
   Mechanic: Classic snake gameplay with neon theme
   Objective: Eat food to grow snake without hitting walls or self
   Controls: Swipe to change direction
   Scoring: 10 points per food eaten
   Special Features: Neon visual effects, grid-based movement
   Code Location: lib/games/neon_snake_game.dart

2. NEON PONG
   Mechanic: Classic pong with AI opponent
   Objective: Score points by getting ball past AI paddle
   Controls: Drag to move player paddle
   Scoring: First to reach target score wins
   Special Features: AI opponent, ball physics, neon effects
   Code Location: lib/games/neon_pong_game.dart

3. ASTRO BLASTER
   Mechanic: Space shooter with enemies and power-ups
   Objective: Destroy enemies while avoiding collisions
   Controls: Drag to move ship, auto-fire bullets
   Scoring: Points for destroyed enemies, wave progression
   Special Features: Multiple enemy types, power-ups, wave system
   Code Location: lib/screens/bonus_games/astro_blaster_screen.dart

4. CRATE SMASH
   Mechanic: Choose one crate to smash for rewards
   Objective: Pick the crate with the highest reward
   Controls: Tap to smash chosen crate
   Scoring: Token rewards based on crate contents
   Special Features: Risk/reward mechanics, one-time choice
   Code Location: lib/games/crate_smash_mini_game.dart

5. GLOW FLOW
   Mechanic: Connect flowing lights through grid paths
   Objective: Create path from start to end node
   Controls: Drag to draw connection paths
   Scoring: Points for successful connections, move efficiency
   Special Features: Grid-based puzzle, obstacles, limited moves
   Code Location: lib/screens/bonus_games/glow_flow_screen.dart

6. NEON STACK
   Mechanic: Stack falling blocks precisely
   Objective: Stack blocks as high as possible with precision
   Controls: Tap to drop moving block
   Scoring: Points per successfully stacked block
   Special Features: Block width decreases with imprecision
   Code Location: lib/games/bonus/neon_stack_game.dart

7. SWIPE SLICE
   Mechanic: Slice flying objects while avoiding bombs
   Objective: Slice fruits/objects, avoid bombs
   Controls: Swipe to slice objects
   Scoring: 10 points per sliced object
   Special Features: Bomb penalties, slice trail effects
   Code Location: lib/games/bonus/swipe_slice_game.dart

8. TAPRUNNER
   Mechanic: Endless runner with jumping
   Objective: Run and jump over obstacles as far as possible
   Controls: Tap to jump
   Scoring: Distance-based scoring, obstacle avoidance
   Special Features: Platform generation, physics-based jumping
   Code Location: lib/games/bonus/tap_runner_game.dart

9. NEON DIG
   Mechanic: Dig through blocks to find treasures
   Objective: Dig deep while avoiding traps, find treasures
   Controls: Tap blocks to destroy them
   Scoring: 10 points normal blocks, 50 treasure, 100 boss, -25 traps
   Special Features: Different block types, depth progression
   Code Location: lib/games/bonus/neon_dig_game.dart

10. SLIDEMATCH
    Mechanic: Match-3 puzzle with sliding tiles
    Objective: Match 3+ tiles by swapping adjacent tiles
    Controls: Tap to select, tap adjacent to swap
    Scoring: Points per match, combo bonuses
    Special Features: Gravity system, combo chains, limited moves
    Code Location: lib/games/bonus/slide_match_game.dart

11. TAPSHOT
    Mechanic: Lane-based shooting gallery
    Objective: Shoot falling targets in 4 lanes
    Controls: Tap lane to shoot bullet upward
    Scoring: Points per target hit, special target bonuses
    Special Features: 4-lane system, bullet-target collision
    Code Location: lib/games/bonus/tap_shot_game.dart

12. DRAG MAZE
    Mechanic: Navigate orb through generated mazes
    Objective: Drag orb from start to end without hitting walls
    Controls: Drag orb through maze paths
    Scoring: 100 points + time bonus for completion
    Special Features: Procedural maze generation, trail effects
    Code Location: lib/games/bonus/drag_maze_game.dart

13. COLORZAP
    Mechanic: Quick color matching reaction game
    Objective: Tap when colors match, avoid when they don't
    Controls: Tap when prompted
    Scoring: 10 points per correct match
    Special Features: Color comparison, reaction timing, lives system
    Code Location: lib/games/bonus/color_zap_game.dart

14. BOUNCETAP
    Mechanic: Keep ball bouncing on platforms
    Objective: Bounce ball on platforms, tap for extra boost
    Controls: Tap to give ball upward boost
    Scoring: Points per platform bounce
    Special Features: Physics-based ball movement, platform collision
    Code Location: lib/games/bonus/bounce_tap_game.dart

15. TAPCRAFT
    Mechanic: Tap to build/craft with progress bars
    Objective: Tap to fill progress bar and complete levels
    Controls: Tap anywhere to increase progress
    Scoring: Progress-based completion
    Special Features: Visual building blocks, tap effects
    Code Location: lib/games/bonus/tap_craft_game.dart





=== PROGRESSION SYSTEM ===

- Breakfinity: Idle/progressive tapping with persistent save state and 1000+ layers
- Bonus games unlocked with key system (3 keys required, 250 tokens per key)
- Token rewards and progression through Breakfinity layers
- Hidden rewards with popup reveals in Breakfinity
- Progressive visual themes and effects across layer ranges

All games feature neon-themed visual effects, particle systems, haptic feedback, and sound integration for an immersive gaming experience.
