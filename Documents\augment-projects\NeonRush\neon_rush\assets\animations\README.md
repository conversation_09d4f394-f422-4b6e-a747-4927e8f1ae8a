# Rive Animation Assets

This directory contains Rive animation files for the enhanced Breakfinity game.

## Required Files

- `breakfinity_layer.riv` - Layer-specific animations for sections

## Creating Rive Animations

To create the required Rive animations:

1. Download Rive Editor from https://rive.app/
2. Create animations with the following state machines:
   - `idle` - Looping glow/pulse animation
   - `tap` - Quick response animation on tap
   - `break` - Section destruction animation
   - `layer_transition` - Layer change animation

3. Export as `.riv` files and place in this directory

## Fallback Behavior

The enhanced game includes fallback behavior when Rive files are not available:
- Animations will be skipped gracefully
- Core gameplay functionality remains intact
- Visual effects will use Flutter animations instead
