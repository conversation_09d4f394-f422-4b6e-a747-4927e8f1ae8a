import 'dart:math';
import 'package:audioplayers/audioplayers.dart';
import 'package:shared_preferences/shared_preferences.dart';

enum SoundType {
  buttonClick,
  neonBomb,
  reward,
  shield,
  addTime,
  slowMo,
  gameOver,
  menuTheme,
  gameTheme1,
  gameTheme2,
  gameTheme3,
}

class SoundManager {
  static final SoundManager _instance = SoundManager._internal();
  factory SoundManager() => _instance;
  SoundManager._internal();

  final AudioPlayer _sfxPlayer = AudioPlayer();
  final AudioPlayer _musicPlayer = AudioPlayer();
  
  bool _sfxEnabled = true;
  bool _musicEnabled = true;
  double _sfxVolume = 0.7;
  double _musicVolume = 0.5;

  // Sound file mappings
  static const Map<SoundType, String> _soundFiles = {
    SoundType.buttonClick: 'assets/sounds/button click.wav',
    SoundType.neonBomb: 'assets/sounds/neon bomb.wav',
    SoundType.reward: 'assets/sounds/reward.wav',
    SoundType.shield: 'assets/sounds/Shield.wav',
    SoundType.addTime: 'assets/sounds/Add time.wav',
    SoundType.slowMo: 'assets/sounds/slow mo.wav',
    SoundType.gameOver: 'assets/sounds/neon bomb.wav', // Reuse existing sound
    SoundType.menuTheme: 'assets/sounds/Menu Theme.wav',
    SoundType.gameTheme1: 'assets/sounds/Game theme 1.wav',
    SoundType.gameTheme2: 'assets/sounds/Game theme 2.wav',
    SoundType.gameTheme3: 'assets/sounds/Game theme 3.mp3',
  };

  // Getters
  bool get sfxEnabled => _sfxEnabled;
  bool get musicEnabled => _musicEnabled;
  double get sfxVolume => _sfxVolume;
  double get musicVolume => _musicVolume;

  /// Initialize the sound manager and load settings
  Future<void> initialize() async {
    await _loadSettings();
    await _setupPlayers();
  }

  /// Load sound settings from SharedPreferences
  Future<void> _loadSettings() async {
    final prefs = await SharedPreferences.getInstance();
    _sfxEnabled = prefs.getBool('sfx_enabled') ?? true;
    _musicEnabled = prefs.getBool('music_enabled') ?? true;
    _sfxVolume = prefs.getDouble('sfx_volume') ?? 0.7;
    _musicVolume = prefs.getDouble('music_volume') ?? 0.5;
  }

  /// Setup audio players with initial settings
  Future<void> _setupPlayers() async {
    await _sfxPlayer.setVolume(_sfxVolume);
    await _musicPlayer.setVolume(_musicVolume);
    await _musicPlayer.setReleaseMode(ReleaseMode.loop);
  }

  /// Save sound settings to SharedPreferences
  Future<void> _saveSettings() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('sfx_enabled', _sfxEnabled);
    await prefs.setBool('music_enabled', _musicEnabled);
    await prefs.setDouble('sfx_volume', _sfxVolume);
    await prefs.setDouble('music_volume', _musicVolume);
  }

  /// Play a sound effect
  Future<void> playSfx(SoundType soundType) async {
    if (!_sfxEnabled) return;

    final soundFile = _soundFiles[soundType];
    if (soundFile != null) {
      try {
        await _sfxPlayer.play(AssetSource(soundFile.replaceFirst('assets/', '')));
      } catch (e) {
        print('Error playing sound effect $soundType: $e');
      }
    }
  }

  /// Play game complete sound
  Future<void> playGameComplete() async {
    await playSfx(SoundType.reward);
  }

  /// Play level complete sound
  Future<void> playLevelComplete() async {
    await playSfx(SoundType.reward);
  }

  /// Play reward sound
  Future<void> playRewardSound() async {
    await playSfx(SoundType.reward);
  }

  /// Play game music
  Future<void> playGameMusic() async {
    await playMusic(SoundType.gameTheme1);
  }

  /// Play background music
  Future<void> playMusic(SoundType musicType) async {
    if (!_musicEnabled) return;
    
    final musicFile = _soundFiles[musicType];
    if (musicFile != null) {
      try {
        await _musicPlayer.stop();
        await _musicPlayer.play(AssetSource(musicFile.replaceFirst('assets/', '')));
      } catch (e) {
        print('Error playing music $musicType: $e');
      }
    }
  }

  /// Stop current music
  Future<void> stopMusic() async {
    await _musicPlayer.stop();
  }

  /// Pause current music
  Future<void> pauseMusic() async {
    await _musicPlayer.pause();
  }

  /// Resume paused music
  Future<void> resumeMusic() async {
    await _musicPlayer.resume();
  }

  /// Set SFX enabled/disabled
  Future<void> setSfxEnabled(bool enabled) async {
    _sfxEnabled = enabled;
    await _saveSettings();
  }

  /// Set music enabled/disabled
  Future<void> setMusicEnabled(bool enabled) async {
    _musicEnabled = enabled;
    if (!enabled) {
      await stopMusic();
    }
    await _saveSettings();
  }

  /// Set SFX volume (0.0 to 1.0)
  Future<void> setSfxVolume(double volume) async {
    _sfxVolume = volume.clamp(0.0, 1.0);
    await _sfxPlayer.setVolume(_sfxVolume);
    await _saveSettings();
  }

  /// Set music volume (0.0 to 1.0)
  Future<void> setMusicVolume(double volume) async {
    _musicVolume = volume.clamp(0.0, 1.0);
    await _musicPlayer.setVolume(_musicVolume);
    await _saveSettings();
  }

  /// Dispose of audio players
  void dispose() {
    _sfxPlayer.dispose();
    _musicPlayer.dispose();
  }

  /// Quick access methods for common sounds
  Future<void> playButtonClick() => playSfx(SoundType.buttonClick);
  Future<void> playReward() => playSfx(SoundType.reward);
  Future<void> playNeonBomb() => playSfx(SoundType.neonBomb);
  Future<void> playShield() => playSfx(SoundType.shield);
  Future<void> playAddTime() => playSfx(SoundType.addTime);
  Future<void> playSlowMo() => playSfx(SoundType.slowMo);
  
  /// Quick access methods for music
  Future<void> playMenuMusic() => playMusic(SoundType.menuTheme);
  Future<void> playGameMusic1() => playMusic(SoundType.gameTheme1);
  Future<void> playGameMusic2() => playMusic(SoundType.gameTheme2);
  Future<void> playGameMusic3() => playMusic(SoundType.gameTheme3);

  /// Play a random game theme song
  Future<void> playRandomGameMusic() async {
    final gameThemes = [
      SoundType.gameTheme1,
      SoundType.gameTheme2,
      SoundType.gameTheme3,
    ];
    final randomIndex = Random().nextInt(gameThemes.length);
    await playMusic(gameThemes[randomIndex]);
  }
}
