import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'dart:math' as math;
import '../design_tokens.dart';
import '../../constants.dart';

/// Animated neon title with bobbing motion and cycling hue colors
class AnimatedNeonTitle extends StatefulWidget {
  final String text;
  final double? fontSize;
  final FontWeight? fontWeight;
  final Duration bobbingDuration;
  final Duration hueCycleDuration;
  final double bobbingDistance;
  final bool enableHueCycle;
  final bool enableBobbing;
  final TextAlign? textAlign;

  const AnimatedNeonTitle({
    super.key,
    required this.text,
    this.fontSize,
    this.fontWeight,
    this.bobbingDuration = const Duration(seconds: 3),
    this.hueCycleDuration = const Duration(seconds: 8),
    this.bobbingDistance = 8.0,
    this.enableHueCycle = true,
    this.enableBobbing = true,
    this.textAlign,
  });

  @override
  State<AnimatedNeonTitle> createState() => _AnimatedNeonTitleState();
}

class _AnimatedNeonTitleState extends State<AnimatedNeonTitle>
    with TickerProviderStateMixin {
  late AnimationController _bobbingController;
  late AnimationController _hueController;
  late Animation<double> _bobbingAnimation;
  late Animation<double> _hueAnimation;

  @override
  void initState() {
    super.initState();

    // Bobbing animation controller
    _bobbingController = AnimationController(
      duration: widget.bobbingDuration,
      vsync: this,
    );

    // Hue cycle animation controller
    _hueController = AnimationController(
      duration: widget.hueCycleDuration,
      vsync: this,
    );

    // Bobbing animation (smooth sine wave)
    _bobbingAnimation = Tween<double>(
      begin: 0.0,
      end: 2 * math.pi,
    ).animate(CurvedAnimation(
      parent: _bobbingController,
      curve: Curves.linear,
    ));

    // Hue cycle animation (0 to 360 degrees)
    _hueAnimation = Tween<double>(
      begin: 0.0,
      end: 360.0,
    ).animate(CurvedAnimation(
      parent: _hueController,
      curve: Curves.linear,
    ));

    // Start animations
    if (widget.enableBobbing) {
      _bobbingController.repeat();
    }
    if (widget.enableHueCycle) {
      _hueController.repeat();
    }
  }

  @override
  void dispose() {
    _bobbingController.dispose();
    _hueController.dispose();
    super.dispose();
  }

  Color _getAnimatedColor() {
    if (!widget.enableHueCycle) {
      return NeonColors.primaryAccent;
    }

    // Convert hue to HSV color
    final hue = _hueAnimation.value;
    return HSVColor.fromAHSV(1.0, hue, 0.8, 1.0).toColor();
  }

  double _getBobbingOffset() {
    if (!widget.enableBobbing) {
      return 0.0;
    }

    // Create smooth bobbing motion using sine wave
    return math.sin(_bobbingAnimation.value) * widget.bobbingDistance;
  }

  @override
  Widget build(BuildContext context) {
    final effectiveFontSize = widget.fontSize ?? DesignTokens.fontSizeDisplay;
    final effectiveFontWeight = widget.fontWeight ?? DesignTokens.fontWeightExtraBold;

    return AnimatedBuilder(
      animation: Listenable.merge([_bobbingController, _hueController]),
      builder: (context, child) {
        final animatedColor = _getAnimatedColor();
        final bobbingOffset = _getBobbingOffset();

        return Transform.translate(
          offset: Offset(0, bobbingOffset),
          child: Container(
            decoration: BoxDecoration(
              boxShadow: [
                // Outer glow
                BoxShadow(
                  color: animatedColor.withValues(alpha: 0.6),
                  blurRadius: 30,
                  spreadRadius: 8,
                ),
                // Inner glow
                BoxShadow(
                  color: animatedColor.withValues(alpha: 0.8),
                  blurRadius: 15,
                  spreadRadius: 3,
                ),
              ],
            ),
            child: ShaderMask(
              shaderCallback: (bounds) {
                return LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    animatedColor,
                    animatedColor.withValues(alpha: 0.8),
                    animatedColor,
                  ],
                  stops: const [0.0, 0.5, 1.0],
                ).createShader(bounds);
              },
              child: Text(
                widget.text,
                textAlign: widget.textAlign ?? TextAlign.center,
                style: GoogleFonts.orbitron(
                  fontSize: effectiveFontSize,
                  fontWeight: effectiveFontWeight,
                  color: Colors.white, // This will be masked by the shader
                  letterSpacing: 3.0,
                  shadows: [
                    // Text shadow for depth
                    Shadow(
                      color: animatedColor.withValues(alpha: 0.5),
                      blurRadius: 10,
                      offset: const Offset(2, 2),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}

/// Specialized title variants
class AnimatedNeonTitleVariants {
  // Main title with full effects
  static Widget mainTitle({
    required String text,
    double? fontSize,
    TextAlign? textAlign,
  }) {
    return AnimatedNeonTitle(
      text: text,
      fontSize: fontSize ?? DesignTokens.fontSizeDisplay,
      fontWeight: DesignTokens.fontWeightBlack,
      bobbingDuration: const Duration(seconds: 3),
      hueCycleDuration: const Duration(seconds: 8),
      bobbingDistance: 10.0,
      enableHueCycle: true,
      enableBobbing: true,
      textAlign: textAlign,
    );
  }

  // Subtitle with subtle effects
  static Widget subtitle({
    required String text,
    double? fontSize,
    TextAlign? textAlign,
  }) {
    return AnimatedNeonTitle(
      text: text,
      fontSize: fontSize ?? DesignTokens.fontSizeTitleLarge,
      fontWeight: DesignTokens.fontWeightBold,
      bobbingDuration: const Duration(seconds: 4),
      hueCycleDuration: const Duration(seconds: 12),
      bobbingDistance: 5.0,
      enableHueCycle: true,
      enableBobbing: true,
      textAlign: textAlign,
    );
  }

  // Static title with only hue cycling
  static Widget staticHue({
    required String text,
    double? fontSize,
    TextAlign? textAlign,
  }) {
    return AnimatedNeonTitle(
      text: text,
      fontSize: fontSize ?? DesignTokens.fontSizeHeadingLarge,
      fontWeight: DesignTokens.fontWeightExtraBold,
      hueCycleDuration: const Duration(seconds: 6),
      enableHueCycle: true,
      enableBobbing: false,
      textAlign: textAlign,
    );
  }

  // Bobbing only (no hue cycle)
  static Widget bobbingOnly({
    required String text,
    double? fontSize,
    TextAlign? textAlign,
  }) {
    return AnimatedNeonTitle(
      text: text,
      fontSize: fontSize ?? DesignTokens.fontSizeHeadingLarge,
      fontWeight: DesignTokens.fontWeightExtraBold,
      bobbingDuration: const Duration(seconds: 2),
      bobbingDistance: 6.0,
      enableHueCycle: false,
      enableBobbing: true,
      textAlign: textAlign,
    );
  }

  // Minimal animation for performance
  static Widget minimal({
    required String text,
    double? fontSize,
    TextAlign? textAlign,
  }) {
    return AnimatedNeonTitle(
      text: text,
      fontSize: fontSize ?? DesignTokens.fontSizeHeading,
      fontWeight: DesignTokens.fontWeightBold,
      bobbingDuration: const Duration(seconds: 5),
      hueCycleDuration: const Duration(seconds: 15),
      bobbingDistance: 3.0,
      enableHueCycle: true,
      enableBobbing: true,
      textAlign: textAlign,
    );
  }
}
