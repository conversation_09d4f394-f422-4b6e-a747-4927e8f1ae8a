import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../design_tokens.dart';
import '../../constants.dart';

/// Standardized button component with consistent neon styling
class NeonButton extends StatefulWidget {
  final String text;
  final VoidCallback? onPressed;
  final Color? backgroundColor;
  final Color? borderColor;
  final Color? textColor;
  final Color? glowColor;
  final double? width;
  final double? height;
  final double? fontSize;
  final FontWeight? fontWeight;
  final EdgeInsetsGeometry? padding;
  final double? borderRadius;
  final bool hasGlow;
  final bool hasBorder;
  final bool isLoading;
  final Widget? icon;
  final bool iconFirst;

  const NeonButton({
    super.key,
    required this.text,
    this.onPressed,
    this.backgroundColor,
    this.borderColor,
    this.textColor,
    this.glowColor,
    this.width,
    this.height,
    this.fontSize,
    this.fontWeight,
    this.padding,
    this.borderRadius,
    this.hasGlow = true,
    this.hasBorder = true,
    this.isLoading = false,
    this.icon,
    this.iconFirst = true,
  });

  @override
  State<NeonButton> createState() => _NeonButtonState();
}

class _NeonButtonState extends State<NeonButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  bool _isPressed = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: DesignTokens.animationFast,
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _onTapDown(TapDownDetails details) {
    setState(() => _isPressed = true);
    _animationController.forward();
  }

  void _onTapUp(TapUpDetails details) {
    setState(() => _isPressed = false);
    _animationController.reverse();
  }

  void _onTapCancel() {
    setState(() => _isPressed = false);
    _animationController.reverse();
  }

  @override
  Widget build(BuildContext context) {
    final isEnabled = widget.onPressed != null && !widget.isLoading;
    final effectiveBackgroundColor = widget.backgroundColor ?? 
        (isEnabled ? NeonColors.primaryAccent.withValues(alpha: 0.2) : NeonColors.surface.withValues(alpha: 0.1));
    final effectiveBorderColor = widget.borderColor ?? 
        (isEnabled ? NeonColors.primaryAccent : NeonColors.primaryAccent.withValues(alpha: 0.3));
    final effectiveTextColor = widget.textColor ?? 
        (isEnabled ? NeonColors.primaryAccent : NeonColors.primaryAccent.withValues(alpha: 0.5));
    final effectiveGlowColor = widget.glowColor ?? effectiveBorderColor;
    final effectiveFontSize = widget.fontSize ?? DesignTokens.fontSizeBodyLarge;
    final effectiveFontWeight = widget.fontWeight ?? DesignTokens.fontWeightSemiBold;
    final effectivePadding = widget.padding ?? 
        const EdgeInsets.symmetric(horizontal: DesignTokens.space20, vertical: DesignTokens.space12);
    final effectiveBorderRadius = widget.borderRadius ?? DesignTokens.radiusMedium;

    Widget buttonContent = Row(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        if (widget.icon != null && widget.iconFirst) ...[
          widget.icon!,
          const SizedBox(width: DesignTokens.space8),
        ],
        if (widget.isLoading)
          SizedBox(
            width: DesignTokens.iconMedium,
            height: DesignTokens.iconMedium,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(effectiveTextColor),
            ),
          )
        else
          Text(
            widget.text,
            style: GoogleFonts.orbitron(
              fontSize: effectiveFontSize,
              fontWeight: effectiveFontWeight,
              color: effectiveTextColor,
              letterSpacing: 1.2,
            ),
          ),
        if (widget.icon != null && !widget.iconFirst) ...[
          const SizedBox(width: DesignTokens.space8),
          widget.icon!,
        ],
      ],
    );

    return AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: GestureDetector(
            onTapDown: isEnabled ? _onTapDown : null,
            onTapUp: isEnabled ? _onTapUp : null,
            onTapCancel: isEnabled ? _onTapCancel : null,
            onTap: isEnabled ? widget.onPressed : null,
            child: Container(
              width: widget.width,
              height: widget.height,
              padding: effectivePadding,
              decoration: BoxDecoration(
                color: effectiveBackgroundColor,
                borderRadius: BorderRadius.circular(effectiveBorderRadius),
                border: widget.hasBorder
                    ? Border.all(
                        color: effectiveBorderColor,
                        width: 1.5,
                      )
                    : null,
                boxShadow: widget.hasGlow && isEnabled
                    ? [
                        BoxShadow(
                          color: effectiveGlowColor.withValues(alpha: _isPressed ? 0.8 : 0.5),
                          blurRadius: _isPressed ? 16 : 12,
                          spreadRadius: _isPressed ? 4 : 2,
                        ),
                      ]
                    : null,
              ),
              child: buttonContent,
            ),
          ),
        );
      },
    );
  }
}

/// Button variants for common use cases
class NeonButtonVariants {
  // Primary button with strong accent
  static Widget primary({
    required String text,
    VoidCallback? onPressed,
    double? width,
    double? height,
    Widget? icon,
    bool isLoading = false,
  }) {
    return NeonButton(
      text: text,
      onPressed: onPressed,
      backgroundColor: NeonColors.primaryAccent.withValues(alpha: 0.2),
      borderColor: NeonColors.primaryAccent,
      textColor: NeonColors.primaryAccent,
      glowColor: NeonColors.primaryAccent,
      width: width,
      height: height,
      icon: icon,
      isLoading: isLoading,
      hasGlow: true,
      hasBorder: true,
    );
  }

  // Secondary button with subtle styling
  static Widget secondary({
    required String text,
    VoidCallback? onPressed,
    double? width,
    double? height,
    Widget? icon,
    bool isLoading = false,
  }) {
    return NeonButton(
      text: text,
      onPressed: onPressed,
      backgroundColor: NeonColors.secondaryAccent.withValues(alpha: 0.1),
      borderColor: NeonColors.secondaryAccent,
      textColor: NeonColors.secondaryAccent,
      glowColor: NeonColors.secondaryAccent,
      width: width,
      height: height,
      icon: icon,
      isLoading: isLoading,
      hasGlow: false,
      hasBorder: true,
    );
  }

  // Ghost button with transparent background
  static Widget ghost({
    required String text,
    VoidCallback? onPressed,
    double? width,
    double? height,
    Widget? icon,
    bool isLoading = false,
  }) {
    return NeonButton(
      text: text,
      onPressed: onPressed,
      backgroundColor: Colors.transparent,
      borderColor: NeonColors.primaryAccent.withValues(alpha: 0.5),
      textColor: NeonColors.primaryAccent,
      glowColor: NeonColors.primaryAccent,
      width: width,
      height: height,
      icon: icon,
      isLoading: isLoading,
      hasGlow: false,
      hasBorder: true,
    );
  }

  // Success button with green accent
  static Widget success({
    required String text,
    VoidCallback? onPressed,
    double? width,
    double? height,
    Widget? icon,
    bool isLoading = false,
  }) {
    return NeonButton(
      text: text,
      onPressed: onPressed,
      backgroundColor: NeonColors.success.withValues(alpha: 0.2),
      borderColor: NeonColors.success,
      textColor: NeonColors.success,
      glowColor: NeonColors.success,
      width: width,
      height: height,
      icon: icon,
      isLoading: isLoading,
      hasGlow: true,
      hasBorder: true,
    );
  }

  // Error button with red accent
  static Widget error({
    required String text,
    VoidCallback? onPressed,
    double? width,
    double? height,
    Widget? icon,
    bool isLoading = false,
  }) {
    return NeonButton(
      text: text,
      onPressed: onPressed,
      backgroundColor: NeonColors.error.withValues(alpha: 0.2),
      borderColor: NeonColors.error,
      textColor: NeonColors.error,
      glowColor: NeonColors.error,
      width: width,
      height: height,
      icon: icon,
      isLoading: isLoading,
      hasGlow: true,
      hasBorder: true,
    );
  }
}
