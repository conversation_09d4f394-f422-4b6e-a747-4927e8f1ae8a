import 'package:flutter/material.dart';
import '../design_tokens.dart';
import '../../constants.dart';

/// Standardized card component with consistent neon styling
class NeonCard extends StatelessWidget {
  final Widget child;
  final EdgeInsetsGeometry? padding;
  final Color? backgroundColor;
  final Color? borderColor;
  final Color? glowColor;
  final double? borderRadius;
  final double borderWidth;
  final bool hasGlow;
  final bool hasBorder;
  final VoidCallback? onTap;
  final double? width;
  final double? height;

  const NeonCard({
    super.key,
    required this.child,
    this.padding,
    this.backgroundColor,
    this.borderColor,
    this.glowColor,
    this.borderRadius,
    this.borderWidth = 1.0,
    this.hasGlow = true,
    this.hasBorder = true,
    this.onTap,
    this.width,
    this.height,
  });

  @override
  Widget build(BuildContext context) {
    final effectiveBorderColor = borderColor ?? NeonColors.primaryAccent;
    final effectiveGlowColor = glowColor ?? effectiveBorderColor;
    final effectiveBackgroundColor = backgroundColor ?? NeonColors.surface;
    final effectiveBorderRadius = borderRadius ?? DesignTokens.radiusMedium;
    final effectivePadding = padding ?? const EdgeInsets.all(DesignTokens.space16);

    Widget cardWidget = Container(
      width: width,
      height: height,
      padding: effectivePadding,
      decoration: BoxDecoration(
        color: effectiveBackgroundColor,
        borderRadius: BorderRadius.circular(effectiveBorderRadius),
        border: hasBorder
            ? Border.all(
                color: effectiveBorderColor,
                width: borderWidth,
              )
            : null,
        boxShadow: hasGlow ? DesignGlows.medium(effectiveGlowColor) : null,
      ),
      child: child,
    );

    if (onTap != null) {
      return GestureDetector(
        onTap: onTap,
        child: cardWidget,
      );
    }

    return cardWidget;
  }
}

/// Specialized card variants
class NeonCardVariants {
  // Primary card with strong glow
  static Widget primary({
    required Widget child,
    EdgeInsetsGeometry? padding,
    VoidCallback? onTap,
    double? width,
    double? height,
  }) {
    return NeonCard(
      backgroundColor: NeonColors.surface,
      borderColor: NeonColors.primaryAccent,
      glowColor: NeonColors.primaryAccent,
      hasGlow: true,
      hasBorder: true,
      padding: padding,
      onTap: onTap,
      width: width,
      height: height,
      child: child,
    );
  }

  // Secondary card with subtle glow
  static Widget secondary({
    required Widget child,
    EdgeInsetsGeometry? padding,
    VoidCallback? onTap,
    double? width,
    double? height,
  }) {
    return NeonCard(
      backgroundColor: NeonColors.surface,
      borderColor: NeonColors.secondaryAccent,
      glowColor: NeonColors.secondaryAccent,
      hasGlow: true,
      hasBorder: true,
      padding: padding,
      onTap: onTap,
      width: width,
      height: height,
      child: child,
    );
  }

  // Ghost card with no background
  static Widget ghost({
    required Widget child,
    EdgeInsetsGeometry? padding,
    VoidCallback? onTap,
    double? width,
    double? height,
  }) {
    return NeonCard(
      backgroundColor: Colors.transparent,
      borderColor: NeonColors.primaryAccent.withValues(alpha: 0.3),
      glowColor: NeonColors.primaryAccent,
      hasGlow: false,
      hasBorder: true,
      padding: padding,
      onTap: onTap,
      width: width,
      height: height,
      child: child,
    );
  }

  // Success card with green accent
  static Widget success({
    required Widget child,
    EdgeInsetsGeometry? padding,
    VoidCallback? onTap,
    double? width,
    double? height,
  }) {
    return NeonCard(
      backgroundColor: NeonColors.surface,
      borderColor: NeonColors.success,
      glowColor: NeonColors.success,
      hasGlow: true,
      hasBorder: true,
      padding: padding,
      onTap: onTap,
      width: width,
      height: height,
      child: child,
    );
  }

  // Error card with red accent
  static Widget error({
    required Widget child,
    EdgeInsetsGeometry? padding,
    VoidCallback? onTap,
    double? width,
    double? height,
  }) {
    return NeonCard(
      backgroundColor: NeonColors.surface,
      borderColor: NeonColors.error,
      glowColor: NeonColors.error,
      hasGlow: true,
      hasBorder: true,
      padding: padding,
      onTap: onTap,
      width: width,
      height: height,
      child: child,
    );
  }

  // Warning card with orange accent
  static Widget warning({
    required Widget child,
    EdgeInsetsGeometry? padding,
    VoidCallback? onTap,
    double? width,
    double? height,
  }) {
    return NeonCard(
      backgroundColor: NeonColors.surface,
      borderColor: NeonColors.warning,
      glowColor: NeonColors.warning,
      hasGlow: true,
      hasBorder: true,
      padding: padding,
      onTap: onTap,
      width: width,
      height: height,
      child: child,
    );
  }
}
