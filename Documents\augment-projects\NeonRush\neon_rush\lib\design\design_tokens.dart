import 'package:flutter/material.dart';

/// Design tokens for consistent spacing, typography, and visual elements
class DesignTokens {
  // Spacing system (4px base unit)
  static const double space4 = 4.0;
  static const double space8 = 8.0;
  static const double space12 = 12.0;
  static const double space16 = 16.0;
  static const double space20 = 20.0;
  static const double space24 = 24.0;
  static const double space32 = 32.0;
  static const double space40 = 40.0;
  static const double space48 = 48.0;
  static const double space64 = 64.0;

  // Border radius system
  static const double radiusSmall = 8.0;
  static const double radiusMedium = 12.0;
  static const double radiusLarge = 16.0;
  static const double radiusXLarge = 20.0;
  static const double radiusRound = 50.0;

  // Typography scale
  static const double fontSizeCaption = 12.0;
  static const double fontSizeBody = 14.0;
  static const double fontSizeBodyLarge = 16.0;
  static const double fontSizeSubtitle = 18.0;
  static const double fontSizeTitle = 20.0;
  static const double fontSizeTitleLarge = 24.0;
  static const double fontSizeHeading = 28.0;
  static const double fontSizeHeadingLarge = 32.0;
  static const double fontSizeDisplay = 40.0;

  // Font weights
  static const FontWeight fontWeightLight = FontWeight.w300;
  static const FontWeight fontWeightRegular = FontWeight.w400;
  static const FontWeight fontWeightMedium = FontWeight.w500;
  static const FontWeight fontWeightSemiBold = FontWeight.w600;
  static const FontWeight fontWeightBold = FontWeight.w700;
  static const FontWeight fontWeightExtraBold = FontWeight.w800;
  static const FontWeight fontWeightBlack = FontWeight.w900;

  // Elevation/Shadow system
  static const double elevationLow = 2.0;
  static const double elevationMedium = 4.0;
  static const double elevationHigh = 8.0;
  static const double elevationVeryHigh = 16.0;

  // Glow intensity levels
  static const double glowSubtle = 0.3;
  static const double glowMedium = 0.5;
  static const double glowStrong = 0.8;
  static const double glowIntense = 1.0;

  // Animation durations
  static const Duration animationFast = Duration(milliseconds: 150);
  static const Duration animationMedium = Duration(milliseconds: 300);
  static const Duration animationSlow = Duration(milliseconds: 500);
  static const Duration animationVerySlow = Duration(milliseconds: 1000);

  // Icon sizes
  static const double iconSmall = 16.0;
  static const double iconMedium = 20.0;
  static const double iconLarge = 24.0;
  static const double iconXLarge = 32.0;
  static const double iconXXLarge = 48.0;

  // Button heights
  static const double buttonHeightSmall = 32.0;
  static const double buttonHeightMedium = 40.0;
  static const double buttonHeightLarge = 48.0;
  static const double buttonHeightXLarge = 56.0;

  // Container constraints
  static const double containerMaxWidth = 400.0;
  static const double containerMaxWidthLarge = 600.0;
  static const double containerMaxWidthXLarge = 800.0;

  // Grid system
  static const int gridColumnsSmall = 1;
  static const int gridColumnsMedium = 2;
  static const int gridColumnsLarge = 3;
  static const int gridColumnsXLarge = 4;

  // Breakpoints
  static const double breakpointSmall = 400.0;
  static const double breakpointMedium = 600.0;
  static const double breakpointLarge = 900.0;
  static const double breakpointXLarge = 1200.0;

  // Helper methods for responsive design
  static bool isSmallScreen(double width) => width < breakpointSmall;
  static bool isMediumScreen(double width) => width >= breakpointSmall && width < breakpointMedium;
  static bool isLargeScreen(double width) => width >= breakpointMedium && width < breakpointLarge;
  static bool isXLargeScreen(double width) => width >= breakpointLarge;

  // Get responsive spacing
  static double getResponsiveSpacing(double width, {
    double small = space16,
    double medium = space20,
    double large = space24,
    double xLarge = space32,
  }) {
    if (isSmallScreen(width)) return small;
    if (isMediumScreen(width)) return medium;
    if (isLargeScreen(width)) return large;
    return xLarge;
  }

  // Get responsive font size
  static double getResponsiveFontSize(double width, {
    double small = fontSizeBody,
    double medium = fontSizeBodyLarge,
    double large = fontSizeSubtitle,
    double xLarge = fontSizeTitle,
  }) {
    if (isSmallScreen(width)) return small;
    if (isMediumScreen(width)) return medium;
    if (isLargeScreen(width)) return large;
    return xLarge;
  }

  // Get responsive grid columns
  static int getResponsiveColumns(double width, {
    int small = gridColumnsSmall,
    int medium = gridColumnsMedium,
    int large = gridColumnsLarge,
    int xLarge = gridColumnsXLarge,
  }) {
    if (isSmallScreen(width)) return small;
    if (isMediumScreen(width)) return medium;
    if (isLargeScreen(width)) return large;
    return xLarge;
  }
}

/// Standard shadow configurations
class DesignShadows {
  static List<BoxShadow> low(Color color) => [
    BoxShadow(
      color: color.withValues(alpha: 0.1),
      blurRadius: DesignTokens.elevationLow,
      spreadRadius: 0,
      offset: const Offset(0, 1),
    ),
  ];

  static List<BoxShadow> medium(Color color) => [
    BoxShadow(
      color: color.withValues(alpha: 0.15),
      blurRadius: DesignTokens.elevationMedium,
      spreadRadius: 0,
      offset: const Offset(0, 2),
    ),
  ];

  static List<BoxShadow> high(Color color) => [
    BoxShadow(
      color: color.withValues(alpha: 0.2),
      blurRadius: DesignTokens.elevationHigh,
      spreadRadius: 0,
      offset: const Offset(0, 4),
    ),
  ];

  static List<BoxShadow> veryHigh(Color color) => [
    BoxShadow(
      color: color.withValues(alpha: 0.25),
      blurRadius: DesignTokens.elevationVeryHigh,
      spreadRadius: 0,
      offset: const Offset(0, 8),
    ),
  ];
}

/// Standard glow configurations for neon effects
class DesignGlows {
  static List<BoxShadow> subtle(Color color) => [
    BoxShadow(
      color: color.withValues(alpha: DesignTokens.glowSubtle),
      blurRadius: 8,
      spreadRadius: 2,
    ),
  ];

  static List<BoxShadow> medium(Color color) => [
    BoxShadow(
      color: color.withValues(alpha: DesignTokens.glowMedium),
      blurRadius: 12,
      spreadRadius: 3,
    ),
  ];

  static List<BoxShadow> strong(Color color) => [
    BoxShadow(
      color: color.withValues(alpha: DesignTokens.glowStrong),
      blurRadius: 16,
      spreadRadius: 4,
    ),
  ];

  static List<BoxShadow> intense(Color color) => [
    BoxShadow(
      color: color.withValues(alpha: DesignTokens.glowIntense),
      blurRadius: 20,
      spreadRadius: 5,
    ),
  ];
}
