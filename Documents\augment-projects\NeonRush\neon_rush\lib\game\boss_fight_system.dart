import 'dart:async';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '../core/constants.dart';
import '../models/boss.dart';

/// Manages boss fight mechanics and state
class BossFightSystem {
  Boss? _currentBoss;
  bool _isActive = false;
  int _playerTaps = 0;
  int _consecutiveHits = 0;
  DateTime _fightStartTime = DateTime.now();
  
  // Callbacks
  Function(int damage)? onBossDamaged;
  Function(Boss boss)? onBossDefeated;
  Function(String message)? onFightMessage;
  Function(int taps, int timeRemaining)? onScoreUpdate;
  Function()? onPlayerHit;
  
  // Fight configuration
  static const int playerDamage = 25;
  static const int comboThreshold = 3;
  static const int comboDamageBonus = 10;
  static const Duration fightDuration = Duration(minutes: 2);
  
  Timer? _updateTimer;
  Timer? _fightTimer;

  /// Initialize boss fight for given level
  bool startBossFight(int level, Size screenSize) {
    if (!GameCalculations.isBossLevel(level)) {
      return false;
    }
    
    try {
      final spawnPosition = Offset(screenSize.width / 2, screenSize.height / 3);
      _currentBoss = BossConfigs.createBossForLevel(level, spawnPosition);
      _isActive = true;
      _playerTaps = 0;
      _consecutiveHits = 0;
      _fightStartTime = DateTime.now();
      
      _startUpdateLoop();
      _startFightTimer();
      
      onFightMessage?.call('Boss Fight: ${_currentBoss!.name}');
      return true;
    } catch (e) {
      return false;
    }
  }

  /// Handle player tap on boss
  bool handleTap(Offset tapPosition) {
    if (!_isActive || _currentBoss == null) return false;
    
    final boss = _currentBoss!;
    final distance = (tapPosition - boss.position).distance;
    final hitRadius = boss.getCurrentSize() / 2;
    
    if (distance <= hitRadius) {
      _playerTaps++;
      
      // Check if boss can take damage
      if (boss.takeDamage(playerDamage + _getComboBonus())) {
        _consecutiveHits++;
        onBossDamaged?.call(playerDamage + _getComboBonus());
        
        // Provide feedback
        HapticFeedback.mediumImpact();
        
        if (_consecutiveHits >= comboThreshold) {
          onFightMessage?.call('Combo x${_consecutiveHits}!');
        }
        
        // Check if boss is defeated
        if (boss.isDefeated) {
          _defeatBoss();
        }
        
        return true;
      } else {
        // Boss blocked the attack
        _consecutiveHits = 0;
        onFightMessage?.call('Boss blocked!');
        HapticFeedback.lightImpact();
        return false;
      }
    }
    
    // Missed the boss
    _consecutiveHits = 0;
    return false;
  }

  /// Handle boss attack hitting player
  void handleBossAttack() {
    if (!_isActive) return;
    
    onPlayerHit?.call();
    onFightMessage?.call('Boss attack hit!');
    HapticFeedback.heavyImpact();
  }

  /// Get current boss
  Boss? get currentBoss => _currentBoss;

  /// Check if fight is active
  bool get isActive => _isActive;

  /// Get fight progress (0.0 to 1.0)
  double get fightProgress {
    if (_currentBoss == null) return 0.0;
    return 1.0 - _currentBoss!.healthPercentage;
  }

  /// Get time remaining in fight
  Duration get timeRemaining {
    if (!_isActive) return Duration.zero;
    
    final elapsed = DateTime.now().difference(_fightStartTime);
    final remaining = fightDuration - elapsed;
    return remaining.isNegative ? Duration.zero : remaining;
  }

  /// Get player tap count
  int get playerTaps => _playerTaps;

  /// Stop the boss fight
  void stopFight() {
    _isActive = false;
    _updateTimer?.cancel();
    _fightTimer?.cancel();
    _currentBoss = null;
  }

  /// Dispose resources
  void dispose() {
    stopFight();
  }

  // Private methods
  void _startUpdateLoop() {
    _updateTimer = Timer.periodic(const Duration(milliseconds: 16), (timer) {
      if (!_isActive || _currentBoss == null) {
        timer.cancel();
        return;
      }
      
      _currentBoss!.update(const Duration(milliseconds: 16));
      
      // Check for boss attacks
      _checkBossAttacks();
    });
  }

  void _startFightTimer() {
    _fightTimer = Timer(fightDuration, () {
      if (_isActive) {
        _timeOut();
      }
    });
  }

  void _checkBossAttacks() {
    if (_currentBoss == null) return;
    
    final boss = _currentBoss!;
    
    // Boss attacks based on state and pattern
    if (boss.state == BossState.attacking) {
      switch (boss.currentAttackPattern) {
        case BossAttackType.basicTap:
          // Basic attack - player just needs to tap when vulnerable
          break;
          
        case BossAttackType.rapidFire:
          // Rapid fire - requires quick successive taps
          if (_consecutiveHits < 3) {
            // Boss is still dangerous
          }
          break;
          
        case BossAttackType.chargedAttack:
          // Charged attack - boss charges up, then becomes vulnerable
          break;
          
        case BossAttackType.shieldBreaker:
          // Shield breaker - must hit specific weak points
          break;
          
        case BossAttackType.ultimateAttack:
          // Ultimate attack - complex pattern
          break;
      }
    }
  }

  int _getComboBonus() {
    if (_consecutiveHits >= comboThreshold) {
      return comboDamageBonus * (_consecutiveHits - comboThreshold + 1);
    }
    return 0;
  }

  void _defeatBoss() {
    if (_currentBoss == null) return;
    
    _isActive = false;
    _updateTimer?.cancel();
    _fightTimer?.cancel();
    
    // Calculate final score
    final timeRemainingSeconds = timeRemaining.inSeconds;
    final scoreBonus = GameCalculations.calculateBossScoreBonus(
      _playerTaps, 
      timeRemainingSeconds
    );
    
    onScoreUpdate?.call(_playerTaps, timeRemainingSeconds);
    onBossDefeated?.call(_currentBoss!);
    onFightMessage?.call('Boss Defeated! Bonus: $scoreBonus');
    
    // Victory effects
    HapticFeedback.heavyImpact();
  }

  void _timeOut() {
    _isActive = false;
    _updateTimer?.cancel();
    _fightTimer?.cancel();
    
    onFightMessage?.call('Time\'s up! Boss escaped!');
    HapticFeedback.heavyImpact();
  }
}

/// Boss fight result data
class BossFightResult {
  final bool victory;
  final int playerTaps;
  final int timeRemainingSeconds;
  final int scoreBonus;
  final Boss boss;
  final Duration fightDuration;

  const BossFightResult({
    required this.victory,
    required this.playerTaps,
    required this.timeRemainingSeconds,
    required this.scoreBonus,
    required this.boss,
    required this.fightDuration,
  });

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'victory': victory,
      'playerTaps': playerTaps,
      'timeRemainingSeconds': timeRemainingSeconds,
      'scoreBonus': scoreBonus,
      'boss': boss.toJson(),
      'fightDurationMs': fightDuration.inMilliseconds,
    };
  }

  /// Create from JSON
  factory BossFightResult.fromJson(Map<String, dynamic> json) {
    return BossFightResult(
      victory: json['victory'],
      playerTaps: json['playerTaps'],
      timeRemainingSeconds: json['timeRemainingSeconds'],
      scoreBonus: json['scoreBonus'],
      boss: Boss.fromJson(json['boss']),
      fightDuration: Duration(milliseconds: json['fightDurationMs']),
    );
  }
}

/// Boss fight statistics
class BossFightStats {
  final Map<int, BossFightResult> completedFights;
  final int totalBossesDefeated;
  final int totalTapsOnBosses;
  final Duration totalBossFightTime;

  const BossFightStats({
    required this.completedFights,
    required this.totalBossesDefeated,
    required this.totalTapsOnBosses,
    required this.totalBossFightTime,
  });

  /// Add a completed fight
  BossFightStats addFight(int level, BossFightResult result) {
    final newFights = Map<int, BossFightResult>.from(completedFights);
    newFights[level] = result;
    
    return BossFightStats(
      completedFights: newFights,
      totalBossesDefeated: totalBossesDefeated + (result.victory ? 1 : 0),
      totalTapsOnBosses: totalTapsOnBosses + result.playerTaps,
      totalBossFightTime: totalBossFightTime + result.fightDuration,
    );
  }

  /// Check if boss level is completed
  bool isBossLevelCompleted(int level) {
    final result = completedFights[level];
    return result != null && result.victory;
  }

  /// Get best time for boss level
  Duration? getBestTimeForLevel(int level) {
    final result = completedFights[level];
    if (result != null && result.victory) {
      return result.fightDuration;
    }
    return null;
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'completedFights': completedFights.map(
        (level, result) => MapEntry(level.toString(), result.toJson())
      ),
      'totalBossesDefeated': totalBossesDefeated,
      'totalTapsOnBosses': totalTapsOnBosses,
      'totalBossFightTimeMs': totalBossFightTime.inMilliseconds,
    };
  }

  /// Create from JSON
  factory BossFightStats.fromJson(Map<String, dynamic> json) {
    final fightsMap = <int, BossFightResult>{};
    final fightsJson = json['completedFights'] as Map<String, dynamic>? ?? {};
    
    for (final entry in fightsJson.entries) {
      final level = int.parse(entry.key);
      final result = BossFightResult.fromJson(entry.value);
      fightsMap[level] = result;
    }
    
    return BossFightStats(
      completedFights: fightsMap,
      totalBossesDefeated: json['totalBossesDefeated'] ?? 0,
      totalTapsOnBosses: json['totalTapsOnBosses'] ?? 0,
      totalBossFightTime: Duration(
        milliseconds: json['totalBossFightTimeMs'] ?? 0
      ),
    );
  }

  /// Create empty stats
  factory BossFightStats.empty() {
    return const BossFightStats(
      completedFights: {},
      totalBossesDefeated: 0,
      totalTapsOnBosses: 0,
      totalBossFightTime: Duration.zero,
    );
  }
}
