import 'dart:async';
import 'package:flutter/material.dart';

/// Combo system for tracking consecutive successful actions
class ComboSystem extends ChangeNotifier {
  int _currentCombo = 0;
  int _maxCombo = 0;
  double _comboMultiplier = 1.0;
  Timer? _comboTimer;
  DateTime? _lastActionTime;
  
  // Configuration
  final Duration comboWindow;
  final int maxComboLevel;
  final double baseMultiplier;
  final double multiplierIncrement;
  
  ComboSystem({
    this.comboWindow = const Duration(seconds: 2),
    this.maxComboLevel = 10,
    this.baseMultiplier = 1.0,
    this.multiplierIncrement = 0.2,
  });

  // Getters
  int get currentCombo => _currentCombo;
  int get maxCombo => _maxCombo;
  double get comboMultiplier => _comboMultiplier;
  bool get hasActiveCombo => _currentCombo > 1;
  
  /// Register a successful action (hit, dodge, etc.)
  void registerSuccess() {
    final now = DateTime.now();
    
    // Check if this action is within the combo window
    if (_lastActionTime != null && 
        now.difference(_lastActionTime!).compareTo(comboWindow) <= 0) {
      // Continue combo
      _currentCombo++;
    } else {
      // Start new combo
      _currentCombo = 1;
    }
    
    _lastActionTime = now;
    _updateComboMultiplier();
    _updateMaxCombo();
    _resetComboTimer();
    
    notifyListeners();
  }
  
  /// Register a failed action (miss, hit by obstacle, etc.)
  void registerFailure() {
    if (_currentCombo > 0) {
      _currentCombo = 0;
      _comboMultiplier = baseMultiplier;
      _comboTimer?.cancel();
      notifyListeners();
    }
  }
  
  /// Force reset the combo
  void resetCombo() {
    _currentCombo = 0;
    _comboMultiplier = baseMultiplier;
    _comboTimer?.cancel();
    notifyListeners();
  }
  
  void _updateComboMultiplier() {
    if (_currentCombo <= 1) {
      _comboMultiplier = baseMultiplier;
    } else {
      final comboLevel = (_currentCombo - 1).clamp(0, maxComboLevel);
      _comboMultiplier = baseMultiplier + (comboLevel * multiplierIncrement);
    }
  }
  
  void _updateMaxCombo() {
    if (_currentCombo > _maxCombo) {
      _maxCombo = _currentCombo;
    }
  }
  
  void _resetComboTimer() {
    _comboTimer?.cancel();
    _comboTimer = Timer(comboWindow, () {
      if (_currentCombo > 0) {
        resetCombo();
      }
    });
  }
  
  /// Get combo tier for visual effects
  ComboTier get comboTier {
    if (_currentCombo < 3) return ComboTier.none;
    if (_currentCombo < 5) return ComboTier.bronze;
    if (_currentCombo < 8) return ComboTier.silver;
    if (_currentCombo < 12) return ComboTier.gold;
    return ComboTier.legendary;
  }
  
  /// Get points with combo multiplier applied
  int getComboPoints(int basePoints) {
    return (basePoints * _comboMultiplier).round();
  }
  
  @override
  void dispose() {
    _comboTimer?.cancel();
    super.dispose();
  }
}

enum ComboTier {
  none,
  bronze,
  silver, 
  gold,
  legendary,
}

extension ComboTierExtension on ComboTier {
  Color get color {
    switch (this) {
      case ComboTier.none:
        return Colors.white;
      case ComboTier.bronze:
        return const Color(0xFFCD7F32);
      case ComboTier.silver:
        return const Color(0xFFC0C0C0);
      case ComboTier.gold:
        return const Color(0xFFFFD700);
      case ComboTier.legendary:
        return const Color(0xFFFF00FF);
    }
  }
  
  String get name {
    switch (this) {
      case ComboTier.none:
        return '';
      case ComboTier.bronze:
        return 'BRONZE';
      case ComboTier.silver:
        return 'SILVER';
      case ComboTier.gold:
        return 'GOLD';
      case ComboTier.legendary:
        return 'LEGENDARY';
    }
  }
}

/// Adaptive difficulty system that adjusts game parameters based on performance
class AdaptiveDifficulty extends ChangeNotifier {
  final List<double> _recentPerformances = [];
  final int _performanceHistorySize = 5;
  double _currentDifficultyMultiplier = 1.0;
  
  // Configuration
  final double minDifficulty;
  final double maxDifficulty;
  final double adjustmentRate;
  final double targetPerformance;
  
  AdaptiveDifficulty({
    this.minDifficulty = 0.7,
    this.maxDifficulty = 1.5,
    this.adjustmentRate = 0.1,
    this.targetPerformance = 0.75, // 75% success rate target
  });
  
  double get difficultyMultiplier => _currentDifficultyMultiplier;
  double get averagePerformance => _recentPerformances.isEmpty 
      ? 0.0 
      : _recentPerformances.reduce((a, b) => a + b) / _recentPerformances.length;
  
  /// Record performance for a completed level (0.0 to 1.0)
  void recordPerformance(double performance) {
    _recentPerformances.add(performance.clamp(0.0, 1.0));
    
    // Keep only recent performances
    if (_recentPerformances.length > _performanceHistorySize) {
      _recentPerformances.removeAt(0);
    }
    
    _adjustDifficulty();
    notifyListeners();
  }
  
  void _adjustDifficulty() {
    if (_recentPerformances.length < 2) return;
    
    final avgPerformance = averagePerformance;
    
    if (avgPerformance > targetPerformance + 0.1) {
      // Player is performing too well, increase difficulty
      _currentDifficultyMultiplier += adjustmentRate;
    } else if (avgPerformance < targetPerformance - 0.1) {
      // Player is struggling, decrease difficulty
      _currentDifficultyMultiplier -= adjustmentRate;
    }
    
    // Clamp to min/max bounds
    _currentDifficultyMultiplier = _currentDifficultyMultiplier.clamp(
      minDifficulty, 
      maxDifficulty,
    );
  }
  
  /// Apply difficulty to spawn rate
  double adjustSpawnRate(double baseRate) {
    return baseRate * _currentDifficultyMultiplier;
  }
  
  /// Apply difficulty to speed
  double adjustSpeed(double baseSpeed) {
    return baseSpeed * _currentDifficultyMultiplier;
  }
  
  /// Apply difficulty to goal requirements
  int adjustGoal(int baseGoal) {
    return (baseGoal / _currentDifficultyMultiplier).round().clamp(1, baseGoal * 2);
  }
  
  /// Reset difficulty to baseline
  void resetDifficulty() {
    _currentDifficultyMultiplier = 1.0;
    _recentPerformances.clear();
    notifyListeners();
  }
  
  /// Get difficulty description for UI
  String get difficultyDescription {
    if (_currentDifficultyMultiplier < 0.8) return 'EASY';
    if (_currentDifficultyMultiplier < 1.2) return 'NORMAL';
    if (_currentDifficultyMultiplier < 1.4) return 'HARD';
    return 'EXTREME';
  }
  
  Color get difficultyColor {
    if (_currentDifficultyMultiplier < 0.8) return Colors.green;
    if (_currentDifficultyMultiplier < 1.2) return Colors.blue;
    if (_currentDifficultyMultiplier < 1.4) return Colors.orange;
    return Colors.red;
  }
}

/// Streak system for tracking consecutive achievements
class StreakSystem extends ChangeNotifier {
  int _currentStreak = 0;
  int _maxStreak = 0;
  DateTime? _lastStreakTime;
  Timer? _streakTimer;
  
  final Duration streakWindow;
  final Map<int, StreakReward> streakRewards;
  
  StreakSystem({
    this.streakWindow = const Duration(seconds: 10),
    this.streakRewards = const {
      5: StreakReward(xpBonus: 50, description: 'Hot Streak!'),
      10: StreakReward(xpBonus: 100, tokenBonus: 5, description: 'On Fire!'),
      15: StreakReward(xpBonus: 200, tokenBonus: 10, description: 'Unstoppable!'),
      20: StreakReward(xpBonus: 300, tokenBonus: 15, description: 'Legendary!'),
    },
  });
  
  int get currentStreak => _currentStreak;
  int get maxStreak => _maxStreak;
  bool get hasActiveStreak => _currentStreak > 0;
  
  void registerStreakAction() {
    _currentStreak++;
    _lastStreakTime = DateTime.now();
    
    if (_currentStreak > _maxStreak) {
      _maxStreak = _currentStreak;
    }
    
    _resetStreakTimer();
    notifyListeners();
  }
  
  void breakStreak() {
    if (_currentStreak > 0) {
      _currentStreak = 0;
      _streakTimer?.cancel();
      notifyListeners();
    }
  }
  
  void _resetStreakTimer() {
    _streakTimer?.cancel();
    _streakTimer = Timer(streakWindow, () {
      breakStreak();
    });
  }
  
  StreakReward? checkForReward() {
    return streakRewards[_currentStreak];
  }
  
  @override
  void dispose() {
    _streakTimer?.cancel();
    super.dispose();
  }
}

class StreakReward {
  final int xpBonus;
  final int tokenBonus;
  final String description;
  
  const StreakReward({
    this.xpBonus = 0,
    this.tokenBonus = 0,
    required this.description,
  });
}
