import 'dart:async';
import 'dart:math';
import 'package:flutter/material.dart';
import '../models/neon_theme.dart';
import '../ui/neon_effects.dart';

/// Types of enemies in the game
enum EnemyType {
  basic,      // Simple moving enemy
  spike,      // Spiky enemy that rotates
  chaser,     // Enemy that follows the player
  bouncer,    // Enemy that bounces off walls
  exploder,   // Enemy that explodes when hit
  boss,       // Boss enemy
}

/// Enemy behavior patterns
enum EnemyBehavior {
  linear,     // Moves in straight line
  circular,   // Moves in circular pattern
  zigzag,     // Moves in zigzag pattern
  homing,     // Homes in on player
  random,     // Random movement
  stationary, // Doesn't move
}

/// Represents an enemy in the game
class Enemy {
  final String id;
  final EnemyType type;
  final EnemyBehavior behavior;
  Offset position;
  Offset velocity;
  final double size;
  final Color color;
  final int health;
  final int damage;
  final double speed;
  double rotation;
  final Map<String, dynamic> properties;
  bool isActive;
  DateTime spawnTime;

  Enemy({
    required this.id,
    required this.type,
    required this.behavior,
    required this.position,
    required this.velocity,
    required this.size,
    required this.color,
    required this.health,
    required this.damage,
    required this.speed,
    this.rotation = 0.0,
    this.properties = const {},
    this.isActive = true,
    DateTime? spawnTime,
  }) : spawnTime = spawnTime ?? DateTime.now();

  /// Create a copy of this enemy with updated properties
  Enemy copyWith({
    String? id,
    EnemyType? type,
    EnemyBehavior? behavior,
    Offset? position,
    Offset? velocity,
    double? size,
    Color? color,
    int? health,
    int? damage,
    double? speed,
    double? rotation,
    Map<String, dynamic>? properties,
    bool? isActive,
    DateTime? spawnTime,
  }) {
    return Enemy(
      id: id ?? this.id,
      type: type ?? this.type,
      behavior: behavior ?? this.behavior,
      position: position ?? this.position,
      velocity: velocity ?? this.velocity,
      size: size ?? this.size,
      color: color ?? this.color,
      health: health ?? this.health,
      damage: damage ?? this.damage,
      speed: speed ?? this.speed,
      rotation: rotation ?? this.rotation,
      properties: properties ?? this.properties,
      isActive: isActive ?? this.isActive,
      spawnTime: spawnTime ?? this.spawnTime,
    );
  }

  /// Check if enemy is colliding with a circular area
  bool isCollidingWith(Offset otherPosition, double otherRadius) {
    final distance = (position - otherPosition).distance;
    return distance < (size / 2 + otherRadius);
  }

  /// Get the age of this enemy in seconds
  double get age => DateTime.now().difference(spawnTime).inMilliseconds / 1000.0;
}

/// Enemy factory for creating different types of enemies
class EnemyFactory {
  static final Random _random = Random();

  /// Create a basic enemy
  static Enemy createBasic({
    required Offset position,
    required Offset velocity,
    required Color color,
    double size = 30.0,
    double speed = 100.0,
  }) {
    return Enemy(
      id: _generateId(),
      type: EnemyType.basic,
      behavior: EnemyBehavior.linear,
      position: position,
      velocity: velocity,
      size: size,
      color: color,
      health: 1,
      damage: 1,
      speed: speed,
    );
  }

  /// Create a spike enemy
  static Enemy createSpike({
    required Offset position,
    required Offset velocity,
    required Color color,
    double size = 40.0,
    double speed = 80.0,
  }) {
    return Enemy(
      id: _generateId(),
      type: EnemyType.spike,
      behavior: EnemyBehavior.linear,
      position: position,
      velocity: velocity,
      size: size,
      color: color,
      health: 1,
      damage: 2,
      speed: speed,
      rotation: _random.nextDouble() * 2 * pi,
    );
  }

  /// Create a chaser enemy
  static Enemy createChaser({
    required Offset position,
    required Color color,
    double size = 35.0,
    double speed = 60.0,
  }) {
    return Enemy(
      id: _generateId(),
      type: EnemyType.chaser,
      behavior: EnemyBehavior.homing,
      position: position,
      velocity: Offset.zero,
      size: size,
      color: color,
      health: 2,
      damage: 1,
      speed: speed,
    );
  }

  /// Create a bouncer enemy
  static Enemy createBouncer({
    required Offset position,
    required Offset velocity,
    required Color color,
    double size = 25.0,
    double speed = 120.0,
  }) {
    return Enemy(
      id: _generateId(),
      type: EnemyType.bouncer,
      behavior: EnemyBehavior.linear,
      position: position,
      velocity: velocity,
      size: size,
      color: color,
      health: 1,
      damage: 1,
      speed: speed,
      properties: {'bounceCount': 0},
    );
  }

  /// Create a boss enemy
  static Enemy createBoss({
    required Offset position,
    required Color color,
    double size = 100.0,
    int health = 10,
  }) {
    return Enemy(
      id: _generateId(),
      type: EnemyType.boss,
      behavior: EnemyBehavior.circular,
      position: position,
      velocity: Offset.zero,
      size: size,
      color: color,
      health: health,
      damage: 3,
      speed: 30.0,
      properties: {'phase': 1, 'attackCooldown': 0.0},
    );
  }

  /// Generate a random enemy ID
  static String _generateId() {
    return 'enemy_${DateTime.now().millisecondsSinceEpoch}_${_random.nextInt(1000)}';
  }
}

/// Enemy spawner configuration
class EnemySpawnConfig {
  final EnemyType type;
  final double spawnRate; // enemies per second
  final int maxEnemies;
  final List<Offset> spawnPoints;
  final Color color;
  final double sizeMin;
  final double sizeMax;
  final double speedMin;
  final double speedMax;

  const EnemySpawnConfig({
    required this.type,
    required this.spawnRate,
    required this.maxEnemies,
    required this.spawnPoints,
    required this.color,
    this.sizeMin = 20.0,
    this.sizeMax = 40.0,
    this.speedMin = 50.0,
    this.speedMax = 150.0,
  });
}

/// Enemy manager handles spawning, updating, and rendering enemies
class EnemyManager {
  final List<Enemy> _enemies = [];
  final List<EnemySpawnConfig> _spawnConfigs = [];
  final Random _random = Random();
  Timer? _spawnTimer;
  Timer? _updateTimer;

  List<Enemy> get enemies => List.unmodifiable(_enemies);

  /// Add a spawn configuration
  void addSpawnConfig(EnemySpawnConfig config) {
    _spawnConfigs.add(config);
  }

  /// Start the enemy system
  void start() {
    // Spawn timer
    _spawnTimer = Timer.periodic(const Duration(milliseconds: 500), (timer) {
      _spawnEnemies();
    });

    // Update timer
    _updateTimer = Timer.periodic(const Duration(milliseconds: 16), (timer) {
      _updateEnemies();
    });
  }

  /// Stop the enemy system
  void stop() {
    _spawnTimer?.cancel();
    _updateTimer?.cancel();
    _enemies.clear();
  }

  /// Spawn enemies based on configurations
  void _spawnEnemies() {
    for (final config in _spawnConfigs) {
      final currentCount = _enemies.where((e) => e.type == config.type).length;
      if (currentCount < config.maxEnemies && _random.nextDouble() < config.spawnRate / 2) {
        _spawnEnemy(config);
      }
    }
  }

  /// Spawn a single enemy
  void _spawnEnemy(EnemySpawnConfig config) {
    final spawnPoint = config.spawnPoints[_random.nextInt(config.spawnPoints.length)];
    final size = config.sizeMin + _random.nextDouble() * (config.sizeMax - config.sizeMin);
    final speed = config.speedMin + _random.nextDouble() * (config.speedMax - config.speedMin);

    Enemy enemy;
    switch (config.type) {
      case EnemyType.basic:
        enemy = EnemyFactory.createBasic(
          position: spawnPoint,
          velocity: _getRandomVelocity(speed),
          color: config.color,
          size: size,
          speed: speed,
        );
        break;
      case EnemyType.spike:
        enemy = EnemyFactory.createSpike(
          position: spawnPoint,
          velocity: _getRandomVelocity(speed),
          color: config.color,
          size: size,
          speed: speed,
        );
        break;
      case EnemyType.chaser:
        enemy = EnemyFactory.createChaser(
          position: spawnPoint,
          color: config.color,
          size: size,
          speed: speed,
        );
        break;
      case EnemyType.bouncer:
        enemy = EnemyFactory.createBouncer(
          position: spawnPoint,
          velocity: _getRandomVelocity(speed),
          color: config.color,
          size: size,
          speed: speed,
        );
        break;
      default:
        return;
    }

    _enemies.add(enemy);
  }

  /// Get random velocity for enemy movement
  Offset _getRandomVelocity(double speed) {
    final angle = _random.nextDouble() * 2 * pi;
    return Offset(cos(angle) * speed, sin(angle) * speed);
  }

  /// Update all enemies
  void _updateEnemies() {
    final deltaTime = 16 / 1000.0; // 16ms in seconds

    for (int i = _enemies.length - 1; i >= 0; i--) {
      final enemy = _enemies[i];
      
      if (!enemy.isActive) {
        _enemies.removeAt(i);
        continue;
      }

      // Update position based on behavior
      _updateEnemyBehavior(enemy, deltaTime);

      // Remove enemies that are off screen
      if (_isOffScreen(enemy.position)) {
        _enemies.removeAt(i);
      }
    }
  }

  /// Update enemy behavior
  void _updateEnemyBehavior(Enemy enemy, double deltaTime) {
    switch (enemy.behavior) {
      case EnemyBehavior.linear:
        enemy.position = Offset(
          enemy.position.dx + enemy.velocity.dx * deltaTime,
          enemy.position.dy + enemy.velocity.dy * deltaTime,
        );
        break;
      case EnemyBehavior.circular:
        enemy.rotation += enemy.speed * deltaTime * 0.01;
        final radius = 100.0;
        enemy.position = Offset(
          0.5 + cos(enemy.rotation) * radius / 400,
          0.5 + sin(enemy.rotation) * radius / 400,
        );
        break;
      case EnemyBehavior.homing:
        // Homing behavior requires player position - will be implemented in game-specific code
        break;
      default:
        break;
    }

    // Update rotation for spike enemies
    if (enemy.type == EnemyType.spike) {
      enemy.rotation += 2.0 * deltaTime;
    }
  }

  /// Check if position is off screen
  bool _isOffScreen(Offset position) {
    return position.dx < -0.2 || position.dx > 1.2 || 
           position.dy < -0.2 || position.dy > 1.2;
  }

  /// Remove an enemy
  void removeEnemy(String enemyId) {
    _enemies.removeWhere((enemy) => enemy.id == enemyId);
  }

  /// Get enemies colliding with a position
  List<Enemy> getCollidingEnemies(Offset position, double radius) {
    return _enemies.where((enemy) => enemy.isCollidingWith(position, radius)).toList();
  }

  /// Clear all enemies
  void clear() {
    _enemies.clear();
  }
}
