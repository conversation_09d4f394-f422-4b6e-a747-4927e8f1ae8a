import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../core/constants.dart';
import '../models/level_config.dart';
import '../models/player_profile.dart';
import '../models/power_up.dart';
import '../services/challenges_service.dart';
import '../models/challenge.dart';

/// Manages the overall game state
class GameStateManager extends ChangeNotifier {
  GameState _currentState = GameState.menu;
  LevelConfig? _currentLevel;
  PlayerProfile? _playerProfile;
  int _currentScore = 0;
  Duration _timeRemaining = Duration.zero;
  Duration _timeElapsed = Duration.zero;
  List<String> _activePowerUps = [];
  Map<String, dynamic> _gameData = {};
  String? _errorMessage;

  // Getters
  GameState get currentState => _currentState;
  LevelConfig? get currentLevel => _currentLevel;
  PlayerProfile? get playerProfile => _playerProfile;
  int get currentScore => _currentScore;
  Duration get timeRemaining => _timeRemaining;
  Duration get timeElapsed => _timeElapsed;
  List<String> get activePowerUps => List.unmodifiable(_activePowerUps);
  Map<String, dynamic> get gameData => Map.unmodifiable(_gameData);
  String? get errorMessage => _errorMessage;

  /// Initialize the game state manager
  void initialize(PlayerProfile profile) {
    _playerProfile = profile;
    _currentState = GameState.menu;
    notifyListeners();
  }

  /// Start a new level
  void startLevel(LevelConfig level) {
    _currentLevel = level;
    _currentScore = 0;
    _timeRemaining = level.duration;
    _timeElapsed = Duration.zero;
    _activePowerUps.clear();
    _gameData.clear();
    _errorMessage = null;
    _currentState = GameState.playing;
    
    // Initialize level-specific data
    _initializeLevelData(level);
    
    notifyListeners();
  }

  /// Pause the current game
  void pauseGame() {
    if (_currentState == GameState.playing) {
      _currentState = GameState.paused;
      notifyListeners();
    }
  }

  /// Resume the paused game
  void resumeGame() {
    if (_currentState == GameState.paused) {
      _currentState = GameState.playing;
      notifyListeners();
    }
  }

  /// End the current game
  void endGame({bool completed = false}) {
    if (completed) {
      // Calculate completion time and call completeLevel
      final completionTime = _currentLevel?.duration != null
          ? _currentLevel!.duration - _timeRemaining
          : _timeElapsed;

      final timeBonus = _timeRemaining.inSeconds > 0 ? _timeRemaining.inSeconds * 10 : 0;

      completeLevel(
        finalScore: _currentScore,
        completionTime: completionTime,
        timeBonus: timeBonus,
      );
    } else {
      _currentState = GameState.gameOver;
      notifyListeners();
    }
  }

  /// Return to main menu
  void returnToMenu() {
    _currentState = GameState.menu;
    _currentLevel = null;
    _currentScore = 0;
    _timeRemaining = Duration.zero;
    _timeElapsed = Duration.zero;
    _activePowerUps.clear();
    _gameData.clear();
    _errorMessage = null;
    notifyListeners();
  }

  /// Open the shop
  void openShop() {
    _currentState = GameState.shop;
    notifyListeners();
  }

  /// Open crate opening screen
  void openCrateOpening() {
    _currentState = GameState.crateOpening;
    notifyListeners();
  }

  /// Update the current score
  void updateScore(int newScore) {
    _currentScore = newScore;
    notifyListeners();
  }

  /// Add to the current score
  void addScore(int points) {
    _currentScore += points;
    notifyListeners();
  }

  /// Update time remaining
  void updateTimeRemaining(Duration time) {
    _timeRemaining = time;
    notifyListeners();
  }

  /// Update time elapsed
  void updateTimeElapsed(Duration time) {
    _timeElapsed = time;
    notifyListeners();
  }

  /// Activate a power-up
  void activatePowerUp(String powerUpId) {
    if (!_activePowerUps.contains(powerUpId)) {
      _activePowerUps.add(powerUpId);
      notifyListeners();
    }
  }

  /// Deactivate a power-up
  void deactivatePowerUp(String powerUpId) {
    _activePowerUps.remove(powerUpId);
    notifyListeners();
  }

  /// Check if a power-up is active
  bool isPowerUpActive(String powerUpId) {
    return _activePowerUps.contains(powerUpId);
  }

  /// Update game data
  void updateGameData(String key, dynamic value) {
    _gameData[key] = value;
    notifyListeners();
  }

  /// Get game data value
  T? getGameData<T>(String key) {
    return _gameData[key] as T?;
  }

  /// Set error message
  void setError(String message) {
    _errorMessage = message;
    notifyListeners();
  }

  /// Clear error message
  void clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  /// Update player profile
  void updatePlayerProfile(PlayerProfile profile) {
    _playerProfile = profile;
    notifyListeners();
  }

  /// Complete the current level with score and rewards
  void completeLevel({
    required int finalScore,
    required Duration completionTime,
    int? timeBonus,
  }) {
    if (_currentLevel == null || _playerProfile == null) return;

    // Calculate rewards
    final baseTokens = (_currentLevel!.tokenReward * (finalScore / 1000)).round();
    final timeBonusTokens = timeBonus != null ? (timeBonus * 0.05).round() : 0;
    final totalTokens = baseTokens + timeBonusTokens;

    // Update player profile
    _playerProfile = _playerProfile!.completeLevel(
      _currentLevel!.mode,
      _currentLevel!.levelNumber,
      finalScore,
      completionTime,
      totalTokens,
    );

    _currentScore = finalScore;
    _currentState = GameState.levelComplete;

    // Update challenge progress
    _updateChallengeProgress();

    // Save progress
    savePlayerProfile();

    notifyListeners();
  }

  /// Update challenge progress based on level completion
  void _updateChallengeProgress() {
    if (_currentLevel == null || _playerProfile == null) return;

    // Update level completion challenges
    ChallengesService.updateProgress(
      gameId: GameIdentifiers.neonrush,
      type: ChallengeType.levelCompletion,
      amount: 1,
      metadata: {
        'gameMode': _currentLevel!.mode.name,
        'levelNumber': _currentLevel!.levelNumber,
        'score': _currentScore,
      },
    );

    // Update token earning challenges
    final tokensEarned = (_currentLevel!.tokenReward * (_currentScore / 1000)).round();
    if (tokensEarned > 0) {
      ChallengesService.updateProgress(
        gameId: GameIdentifiers.neonrush,
        type: ChallengeType.tokenEarning,
        amount: tokensEarned,
      );
    }

    // Update accuracy challenges if applicable
    if (_gameData.containsKey('accuracy')) {
      final accuracy = _gameData['accuracy'] as double;
      ChallengesService.updateProgress(
        gameId: GameIdentifiers.neonrush,
        type: ChallengeType.accuracy,
        amount: (accuracy * 100).round(),
        metadata: {
          'gameMode': _currentLevel!.mode.name,
        },
      );
    }

    // Update speed challenges based on completion time
    final completionTime = _currentLevel!.duration - _timeRemaining;
    ChallengesService.updateProgress(
      gameId: GameIdentifiers.neonrush,
      type: ChallengeType.speed,
      amount: completionTime.inSeconds,
      metadata: {
        'gameMode': _currentLevel!.mode.name,
        'levelNumber': _currentLevel!.levelNumber,
      },
    );
  }

  /// Get completion data for the level completion dialog
  Map<String, dynamic> getLevelCompletionData() {
    if (_currentLevel == null || _playerProfile == null) {
      return {};
    }

    final timeBonus = _timeRemaining.inSeconds > 0 ? _timeRemaining.inSeconds * 10 : 0;

    return {
      'level': _currentLevel!,
      'score': _currentScore,
      'timeBonus': timeBonus,
      'tokensEarned': _currentLevel!.tokenReward,
      'hasNextLevel': _currentLevel!.levelNumber < 10,
      'nextLevelUnlocked': _playerProfile!.isLevelUnlocked(_currentLevel!.mode, _currentLevel!.levelNumber + 1),
    };
  }

  /// Initialize level-specific data based on game mode
  void _initializeLevelData(LevelConfig level) {
    switch (level.mode) {
      case GameMode.tap:
        _gameData['dotsHit'] = 0;
        _gameData['dotsSpawned'] = 0;
        _gameData['dotsMissed'] = 0;
        _gameData['activeDots'] = <Map<String, dynamic>>[];
        break;
      case GameMode.swipe:
        _gameData['objectsSaved'] = 0;
        _gameData['objectsLost'] = 0;
        _gameData['activeObjects'] = <Map<String, dynamic>>[];
        break;
      case GameMode.hold:
        _gameData['totalHoldTime'] = Duration.zero;
        _gameData['isHolding'] = false;
        _gameData['holdStartTime'] = null;
        break;
      case GameMode.avoid:
        _gameData['timesSurvived'] = Duration.zero;
        _gameData['enemiesAvoided'] = 0;
        _gameData['playerPosition'] = {'x': 0.5, 'y': 0.5};
        _gameData['activeEnemies'] = <Map<String, dynamic>>[];
        break;
      case GameMode.memory:
        _gameData['sequencesCompleted'] = 0;
        _gameData['currentSequence'] = <int>[];
        _gameData['playerSequence'] = <int>[];
        _gameData['showingSequence'] = false;
        break;
      case GameMode.reaction:
        _gameData['correctReactions'] = 0;
        _gameData['incorrectReactions'] = 0;
        _gameData['currentSignal'] = 'waiting';
        _gameData['signalStartTime'] = null;
        break;
      case GameMode.drag:
        _gameData['successfulDrags'] = 0;
        _gameData['failedDrags'] = 0;
        _gameData['activeOrbs'] = <Map<String, dynamic>>[];
        _gameData['activeTargets'] = <Map<String, dynamic>>[];
        break;
      case GameMode.spin:
        _gameData['successfulAlignments'] = 0;
        _gameData['ringPositions'] = <double>[0.0, 0.0, 0.0];
        _gameData['targetAlignment'] = 0.0;
        break;
      case GameMode.deflect:
        _gameData['lasersDeflected'] = 0;
        _gameData['lasersHit'] = 0;
        _gameData['activeLasers'] = <Map<String, dynamic>>[];
        _gameData['shieldPosition'] = {'x': 0.5, 'y': 0.5};
        break;
      case GameMode.survive:
        _gameData['survivalTime'] = Duration.zero;
        _gameData['playerPosition'] = {'x': 0.5, 'y': 0.5};
        _gameData['chaosElements'] = <Map<String, dynamic>>[];
        break;
    }
  }

  /// Check if level goal is achieved
  bool isLevelGoalAchieved() {
    if (_currentLevel == null) return false;

    switch (_currentLevel!.mode) {
      case GameMode.tap:
        return (_gameData['dotsHit'] ?? 0) >= _currentLevel!.goal;
      case GameMode.swipe:
        return (_gameData['objectsSaved'] ?? 0) >= _currentLevel!.goal;
      case GameMode.hold:
        final totalHoldTime = _gameData['totalHoldTime'] as Duration? ?? Duration.zero;
        return totalHoldTime.inSeconds >= _currentLevel!.goal;
      case GameMode.avoid:
        final survivalTime = _gameData['timesSurvived'] as Duration? ?? Duration.zero;
        return survivalTime.inSeconds >= _currentLevel!.goal;
      case GameMode.memory:
        return (_gameData['sequencesCompleted'] ?? 0) >= _currentLevel!.goal;
      case GameMode.reaction:
        return (_gameData['correctReactions'] ?? 0) >= _currentLevel!.goal;
      case GameMode.drag:
        return (_gameData['successfulDrags'] ?? 0) >= _currentLevel!.goal;
      case GameMode.spin:
        return (_gameData['successfulAlignments'] ?? 0) >= _currentLevel!.goal;
      case GameMode.deflect:
        return (_gameData['lasersDeflected'] ?? 0) >= _currentLevel!.goal;
      case GameMode.survive:
        return _timeElapsed.inSeconds >= _currentLevel!.goal;
    }
  }

  /// Get progress towards level goal (0.0 to 1.0)
  double getLevelProgress() {
    if (_currentLevel == null) return 0.0;

    switch (_currentLevel!.mode) {
      case GameMode.tap:
        return ((_gameData['dotsHit'] ?? 0) / _currentLevel!.goal).clamp(0.0, 1.0);
      case GameMode.swipe:
        return ((_gameData['objectsSaved'] ?? 0) / _currentLevel!.goal).clamp(0.0, 1.0);
      case GameMode.hold:
        final totalHoldTime = _gameData['totalHoldTime'] as Duration? ?? Duration.zero;
        return (totalHoldTime.inSeconds / _currentLevel!.goal).clamp(0.0, 1.0);
      case GameMode.avoid:
        final survivalTime = _gameData['timesSurvived'] as Duration? ?? Duration.zero;
        return (survivalTime.inSeconds / _currentLevel!.goal).clamp(0.0, 1.0);
      case GameMode.memory:
        return ((_gameData['sequencesCompleted'] ?? 0) / _currentLevel!.goal).clamp(0.0, 1.0);
      case GameMode.reaction:
        return ((_gameData['correctReactions'] ?? 0) / _currentLevel!.goal).clamp(0.0, 1.0);
      case GameMode.drag:
        return ((_gameData['successfulDrags'] ?? 0) / _currentLevel!.goal).clamp(0.0, 1.0);
      case GameMode.spin:
        return ((_gameData['successfulAlignments'] ?? 0) / _currentLevel!.goal).clamp(0.0, 1.0);
      case GameMode.deflect:
        return ((_gameData['lasersDeflected'] ?? 0) / _currentLevel!.goal).clamp(0.0, 1.0);
      case GameMode.survive:
        return (_timeElapsed.inSeconds / _currentLevel!.goal).clamp(0.0, 1.0);
    }
  }

  /// Save player profile to persistent storage
  Future<void> savePlayerProfile() async {
    if (_playerProfile == null) return;

    try {
      final prefs = await SharedPreferences.getInstance();
      final profileJson = _playerProfile!.toJson();
      await prefs.setString('player_profile', jsonEncode(profileJson));
    } catch (e) {
      print('Error saving player profile: $e');
    }
  }

  /// Load player profile from persistent storage
  Future<void> loadPlayerProfile() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final profileString = prefs.getString('player_profile');

      if (profileString != null) {
        final profileJson = jsonDecode(profileString);
        _playerProfile = PlayerProfile.fromJson(profileJson);
      } else {
        // Create new player profile
        _playerProfile = PlayerProfile.newPlayer(
          playerId: 'player_${DateTime.now().millisecondsSinceEpoch}',
          playerName: 'Player',
        );
        await savePlayerProfile();
      }

      notifyListeners();
    } catch (e) {
      print('Error loading player profile: $e');
      // Create default profile on error
      _playerProfile = PlayerProfile.newPlayer(
        playerId: 'player_${DateTime.now().millisecondsSinceEpoch}',
        playerName: 'Player',
      );
      notifyListeners();
    }
  }

  /// Purchase a power-up with tokens
  void purchasePowerUp(String powerUpId) {
    if (_playerProfile == null) return;

    final powerUp = PowerUps.getPowerUpById(powerUpId);
    if (powerUp == null) return;

    if (_playerProfile!.tokens < powerUp.cost) {
      throw Exception('Insufficient tokens');
    }

    // Deduct tokens and add power-up
    _playerProfile = _playerProfile!
        .copyWith(tokens: _playerProfile!.tokens - powerUp.cost)
        .addPowerUp(powerUpId, 1);

    // Save the updated profile
    savePlayerProfile();
    notifyListeners();
  }

  /// Use a power-up during gameplay
  void usePowerUp(String powerUpId) {
    if (_playerProfile == null) return;

    try {
      _playerProfile = _playerProfile!.usePowerUp(powerUpId);
      savePlayerProfile();
      notifyListeners();
    } catch (e) {
      print('Error using power-up: $e');
    }
  }

  /// Get available power-ups for the current player
  List<OwnedPowerUp> getAvailablePowerUps() {
    return _playerProfile?.ownedPowerUps ?? [];
  }

  /// Check if player has a specific power-up
  bool hasPowerUp(String powerUpId) {
    return _playerProfile?.ownedPowerUps
        .any((owned) => owned.powerUpId == powerUpId && owned.quantity > 0) ?? false;
  }

  /// Get quantity of a specific power-up
  int getPowerUpQuantity(String powerUpId) {
    final ownedPowerUp = _playerProfile?.ownedPowerUps
        .where((owned) => owned.powerUpId == powerUpId)
        .firstOrNull;
    return ownedPowerUp?.quantity ?? 0;
  }
}
