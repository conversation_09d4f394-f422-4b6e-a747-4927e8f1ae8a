import 'dart:async';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/gestures.dart';

import '../constants.dart';
import '../core/constants.dart' as core_constants;

/// Types of input gestures supported by the game
enum InputType {
  tap,
  swipe,
  hold,
  drag,
  pinch,
  rotate,
  multiTouch,
}

/// Direction of swipe gestures
enum SwipeDirection {
  up,
  down,
  left,
  right,
  upLeft,
  upRight,
  downLeft,
  downRight,
}

/// Input event data
class InputEvent {
  final InputType type;
  final Offset position;
  final Offset? startPosition;
  final Offset? endPosition;
  final SwipeDirection? swipeDirection;
  final double? velocity;
  final double? distance;
  final Duration? duration;
  final double? angle;
  final double? scale;
  final int touchCount;
  final DateTime timestamp;

  InputEvent({
    required this.type,
    required this.position,
    this.startPosition,
    this.endPosition,
    this.swipeDirection,
    this.velocity,
    this.distance,
    this.duration,
    this.angle,
    this.scale,
    this.touchCount = 1,
  }) : timestamp = DateTime.now();
}

/// Callback function for input events
typedef InputCallback = void Function(InputEvent event);

/// Input system that handles all game input mechanics
class InputSystem {
  // Input callbacks
  final Map<InputType, List<InputCallback>> _callbacks = {};
  
  // Touch tracking
  final Map<int, Offset> _activeTouches = {};
  final Map<int, DateTime> _touchStartTimes = {};
  final Map<int, Offset> _touchStartPositions = {};
  
  // Gesture state
  bool _isHolding = false;
  bool _isDragging = false;
  Timer? _holdTimer;
  DateTime? _holdStartTime;
  Offset? _holdPosition;
  
  // Swipe detection
  static const double _minSwipeDistance = 50.0;
  static const double _maxSwipeTime = 500.0; // milliseconds
  static const double _minSwipeVelocity = 100.0; // pixels per second
  
  // Hold detection
  static const Duration _holdThreshold = Duration(milliseconds: 500);
  static const double _holdTolerance = 20.0; // pixels
  
  // Drag detection
  static const double _dragThreshold = 10.0; // pixels
  
  // Multi-touch
  int get activeTouchCount => _activeTouches.length;
  bool get isMultiTouch => _activeTouches.length > 1;
  
  /// Register a callback for specific input type
  void registerCallback(InputType type, InputCallback callback) {
    _callbacks.putIfAbsent(type, () => []).add(callback);
  }
  
  /// Unregister a callback
  void unregisterCallback(InputType type, InputCallback callback) {
    _callbacks[type]?.remove(callback);
  }
  
  /// Clear all callbacks
  void clearCallbacks() {
    _callbacks.clear();
  }
  
  /// Handle tap down event
  void handleTapDown(TapDownDetails details) {
    final pointerId = 0; // Primary pointer
    final position = details.localPosition;
    
    _activeTouches[pointerId] = position;
    _touchStartTimes[pointerId] = DateTime.now();
    _touchStartPositions[pointerId] = position;
    
    // Start hold detection
    _startHoldDetection(position);
    
    // Trigger immediate tap event for responsive gameplay
    _triggerEvent(InputEvent(
      type: InputType.tap,
      position: position,
      startPosition: position,
    ));
  }
  
  /// Handle tap up event
  void handleTapUp(TapUpDetails details) {
    final pointerId = 0;
    final position = details.localPosition;
    final startPosition = _touchStartPositions[pointerId];
    final startTime = _touchStartTimes[pointerId];
    
    if (startPosition != null && startTime != null) {
      final duration = DateTime.now().difference(startTime);
      final distance = (position - startPosition).distance;
      
      // Check if this was a swipe
      if (distance >= _minSwipeDistance && 
          duration.inMilliseconds <= _maxSwipeTime) {
        _handleSwipe(startPosition, position, duration);
      }
    }
    
    // Clean up touch tracking
    _activeTouches.remove(pointerId);
    _touchStartTimes.remove(pointerId);
    _touchStartPositions.remove(pointerId);
    
    // Stop hold detection
    _stopHoldDetection();
  }
  
  /// Handle pan start event
  void handlePanStart(DragStartDetails details) {
    final position = details.localPosition;
    
    _isDragging = true;
    _triggerEvent(InputEvent(
      type: InputType.drag,
      position: position,
      startPosition: position,
    ));
  }
  
  /// Handle pan update event
  void handlePanUpdate(DragUpdateDetails details) {
    if (!_isDragging) return;
    
    final position = details.localPosition;
    final delta = details.delta;
    
    _triggerEvent(InputEvent(
      type: InputType.drag,
      position: position,
      velocity: delta.distance,
    ));
  }
  
  /// Handle pan end event
  void handlePanEnd(DragEndDetails details) {
    _isDragging = false;
    
    final velocity = details.velocity.pixelsPerSecond.distance;
    _triggerEvent(InputEvent(
      type: InputType.drag,
      position: Offset.zero, // End position
      velocity: velocity,
    ));
  }
  
  /// Handle scale start event (pinch/rotate)
  void handleScaleStart(ScaleStartDetails details) {
    // Multi-touch gesture started
    _triggerEvent(InputEvent(
      type: InputType.multiTouch,
      position: details.localFocalPoint,
      touchCount: details.pointerCount,
    ));
  }
  
  /// Handle scale update event
  void handleScaleUpdate(ScaleUpdateDetails details) {
    final scale = details.scale;
    final rotation = details.rotation;
    
    if (scale != 1.0) {
      _triggerEvent(InputEvent(
        type: InputType.pinch,
        position: details.localFocalPoint,
        scale: scale,
        touchCount: details.pointerCount,
      ));
    }
    
    if (rotation != 0.0) {
      _triggerEvent(InputEvent(
        type: InputType.rotate,
        position: details.localFocalPoint,
        angle: rotation,
        touchCount: details.pointerCount,
      ));
    }
  }
  
  /// Start hold detection
  void _startHoldDetection(Offset position) {
    _holdPosition = position;
    _holdStartTime = DateTime.now();
    
    _holdTimer?.cancel();
    _holdTimer = Timer(_holdThreshold, () {
      if (_holdPosition != null && _holdStartTime != null) {
        _isHolding = true;
        _triggerEvent(InputEvent(
          type: InputType.hold,
          position: _holdPosition!,
          startPosition: _holdPosition!,
          duration: DateTime.now().difference(_holdStartTime!),
        ));
      }
    });
  }
  
  /// Stop hold detection
  void _stopHoldDetection() {
    _holdTimer?.cancel();
    _holdTimer = null;
    _isHolding = false;
    _holdPosition = null;
    _holdStartTime = null;
  }
  
  /// Handle swipe gesture
  void _handleSwipe(Offset start, Offset end, Duration duration) {
    final delta = end - start;
    final distance = delta.distance;
    final velocity = distance / (duration.inMilliseconds / 1000.0);
    
    if (velocity < _minSwipeVelocity) return;
    
    final direction = _getSwipeDirection(delta);
    
    _triggerEvent(InputEvent(
      type: InputType.swipe,
      position: end,
      startPosition: start,
      endPosition: end,
      swipeDirection: direction,
      velocity: velocity,
      distance: distance,
      duration: duration,
    ));
    
    // Provide haptic feedback for swipes
    HapticFeedback.lightImpact();
  }
  
  /// Determine swipe direction from delta
  SwipeDirection _getSwipeDirection(Offset delta) {
    final angle = atan2(delta.dy, delta.dx);
    final degrees = angle * 180 / pi;
    
    // Normalize to 0-360 degrees
    final normalizedDegrees = (degrees + 360) % 360;
    
    if (normalizedDegrees >= 337.5 || normalizedDegrees < 22.5) {
      return SwipeDirection.right;
    } else if (normalizedDegrees >= 22.5 && normalizedDegrees < 67.5) {
      return SwipeDirection.downRight;
    } else if (normalizedDegrees >= 67.5 && normalizedDegrees < 112.5) {
      return SwipeDirection.down;
    } else if (normalizedDegrees >= 112.5 && normalizedDegrees < 157.5) {
      return SwipeDirection.downLeft;
    } else if (normalizedDegrees >= 157.5 && normalizedDegrees < 202.5) {
      return SwipeDirection.left;
    } else if (normalizedDegrees >= 202.5 && normalizedDegrees < 247.5) {
      return SwipeDirection.upLeft;
    } else if (normalizedDegrees >= 247.5 && normalizedDegrees < 292.5) {
      return SwipeDirection.up;
    } else {
      return SwipeDirection.upRight;
    }
  }
  
  /// Trigger input event to all registered callbacks
  void _triggerEvent(InputEvent event) {
    final callbacks = _callbacks[event.type];
    if (callbacks != null) {
      for (final callback in callbacks) {
        callback(event);
      }
    }
  }
  
  /// Check if position is within tolerance of hold position
  bool _isWithinHoldTolerance(Offset position) {
    if (_holdPosition == null) return false;
    return (position - _holdPosition!).distance <= _holdTolerance;
  }
  
  /// Update hold progress (for visual feedback)
  double getHoldProgress() {
    if (_holdStartTime == null) return 0.0;
    
    final elapsed = DateTime.now().difference(_holdStartTime!);
    final progress = elapsed.inMilliseconds / _holdThreshold.inMilliseconds;
    return progress.clamp(0.0, 1.0);
  }
  
  /// Check if currently holding
  bool get isHolding => _isHolding;
  
  /// Check if currently dragging
  bool get isDragging => _isDragging;
  
  /// Get current hold position
  Offset? get holdPosition => _holdPosition;
  
  /// Dispose resources
  void dispose() {
    _holdTimer?.cancel();
    _callbacks.clear();
    _activeTouches.clear();
    _touchStartTimes.clear();
    _touchStartPositions.clear();
  }
}

/// Input handler widget that wraps game content and handles input
class InputHandler extends StatefulWidget {
  final Widget child;
  final InputSystem inputSystem;
  final core_constants.GameMode gameMode;

  const InputHandler({
    super.key,
    required this.child,
    required this.inputSystem,
    required this.gameMode,
  });

  @override
  State<InputHandler> createState() => _InputHandlerState();
}

class _InputHandlerState extends State<InputHandler> {
  @override
  Widget build(BuildContext context) {
    return RawGestureDetector(
      gestures: _buildGestureRecognizers(),
      behavior: _getHitTestBehavior(),
      child: widget.child,
    );
  }

  Map<Type, GestureRecognizerFactory> _buildGestureRecognizers() {
    final gestures = <Type, GestureRecognizerFactory>{};

    // Add tap recognizer
    gestures[TapGestureRecognizer] = GestureRecognizerFactoryWithHandlers<TapGestureRecognizer>(
      () => TapGestureRecognizer(),
      (TapGestureRecognizer instance) {
        instance
          ..onTapDown = widget.inputSystem.handleTapDown
          ..onTapUp = widget.inputSystem.handleTapUp;
      },
    );

    // Add pan recognizer for drag gestures
    gestures[PanGestureRecognizer] = GestureRecognizerFactoryWithHandlers<PanGestureRecognizer>(
      () => PanGestureRecognizer(),
      (PanGestureRecognizer instance) {
        instance
          ..onStart = widget.inputSystem.handlePanStart
          ..onUpdate = widget.inputSystem.handlePanUpdate
          ..onEnd = widget.inputSystem.handlePanEnd;
      },
    );

    // Add scale recognizer for advanced modes
    gestures[ScaleGestureRecognizer] = GestureRecognizerFactoryWithHandlers<ScaleGestureRecognizer>(
      () => ScaleGestureRecognizer(),
      (ScaleGestureRecognizer instance) {
        instance
          ..onStart = widget.inputSystem.handleScaleStart
          ..onUpdate = widget.inputSystem.handleScaleUpdate;
      },
    );

    return gestures;
  }

  HitTestBehavior _getHitTestBehavior() {
    switch (widget.gameMode) {
      case core_constants.GameMode.avoid:
        // For avoid mode, we want to capture all touches
        return HitTestBehavior.opaque;
      default:
        // For other modes, allow touches to pass through to targets
        return HitTestBehavior.translucent;
    }
  }
}
