import 'dart:async';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '../constants.dart';
import '../models/level_config.dart';
import '../providers/game_provider.dart';

/// Level completion criteria and scoring
class LevelObjective {
  final String id;
  final String name;
  final String description;
  final int targetValue;
  final ObjectiveType type;
  final bool isRequired;
  final int bonusPoints;

  LevelObjective({
    required this.id,
    required this.name,
    required this.description,
    required this.targetValue,
    required this.type,
    this.isRequired = true,
    this.bonusPoints = 0,
  });
}

/// Types of level objectives
enum ObjectiveType {
  score,           // Reach target score
  targets,         // Hit specific number of targets
  accuracy,        // Maintain accuracy percentage
  time,            // Complete within time limit
  combo,           // Achieve combo count
  noMisses,        // Complete without missing targets
  perfectHolds,    // Complete all hold targets perfectly
  sequenceLength,  // Memory sequence length
  reactionTime,    // Average reaction time
  deflections,     // Number of successful deflections
}

/// Level completion and progression manager
class LevelProgression {
  final GameProvider gameProvider;
  
  // Current level state
  LevelConfig? _currentLevel;
  List<LevelObjective> _objectives = [];
  Map<String, int> _progress = {};
  Map<String, bool> _completed = {};
  
  // Scoring and statistics
  int _baseScore = 0;
  int _bonusScore = 0;
  int _totalTargetsHit = 0;
  int _totalTargetsMissed = 0;
  int _maxCombo = 0;
  int _perfectActions = 0;
  List<int> _reactionTimes = [];
  Duration _completionTime = Duration.zero;
  DateTime? _levelStartTime;
  
  // Callbacks
  Function(LevelObjective objective)? onObjectiveCompleted;
  Function(int score, Map<String, dynamic> stats)? onLevelCompleted;
  Function(String reason)? onLevelFailed;
  Function(Map<String, int> progress)? onProgressUpdate;

  LevelProgression({required this.gameProvider});

  /// Initialize level progression for a specific level
  void initializeLevel(LevelConfig levelConfig) {
    _currentLevel = levelConfig;
    _resetProgress();
    _generateObjectives();
    _levelStartTime = DateTime.now();
  }

  /// Reset all progress tracking
  void _resetProgress() {
    _progress.clear();
    _completed.clear();
    _baseScore = 0;
    _bonusScore = 0;
    _totalTargetsHit = 0;
    _totalTargetsMissed = 0;
    _maxCombo = 0;
    _perfectActions = 0;
    _reactionTimes.clear();
    _completionTime = Duration.zero;
  }

  /// Generate objectives based on level configuration
  void _generateObjectives() {
    if (_currentLevel == null) return;
    
    _objectives.clear();
    final level = _currentLevel!;
    
    // Primary objective - always score-based
    _objectives.add(LevelObjective(
      id: 'primary_score',
      name: 'Target Score',
      description: 'Reach ${level.goal} points',
      targetValue: level.goal,
      type: ObjectiveType.score,
      isRequired: true,
      bonusPoints: 100,
    ));
    
    // Secondary objectives based on game mode and level
    _addSecondaryObjectives(level);
    
    // Initialize progress tracking
    for (final objective in _objectives) {
      _progress[objective.id] = 0;
      _completed[objective.id] = false;
    }
  }

  /// Add secondary objectives based on level characteristics
  void _addSecondaryObjectives(LevelConfig level) {
    final levelNumber = gameProvider.currentLevel;
    final mechanic = _getMechanicForLevel(levelNumber);
    
    // Time-based objective (optional)
    if (level.duration.inSeconds > 30) {
      final timeLimit = (level.duration.inSeconds * 0.75).round();
      _objectives.add(LevelObjective(
        id: 'time_bonus',
        name: 'Speed Bonus',
        description: 'Complete within $timeLimit seconds',
        targetValue: timeLimit,
        type: ObjectiveType.time,
        isRequired: false,
        bonusPoints: 200,
      ));
    }
    
    // Accuracy objective
    _objectives.add(LevelObjective(
      id: 'accuracy',
      name: 'Accuracy Master',
      description: 'Maintain 80% accuracy',
      targetValue: 80,
      type: ObjectiveType.accuracy,
      isRequired: false,
      bonusPoints: 150,
    ));
    
    // Mechanic-specific objectives
    switch (mechanic) {
      case 'tap':
        _objectives.add(LevelObjective(
          id: 'combo_master',
          name: 'Combo Master',
          description: 'Achieve 10+ combo',
          targetValue: 10,
          type: ObjectiveType.combo,
          isRequired: false,
          bonusPoints: 100,
        ));
        break;
        
      case 'hold':
        _objectives.add(LevelObjective(
          id: 'perfect_holds',
          name: 'Perfect Timing',
          description: 'Complete all holds perfectly',
          targetValue: level.goal ~/ 50, // Estimate number of hold targets
          type: ObjectiveType.perfectHolds,
          isRequired: false,
          bonusPoints: 250,
        ));
        break;
        
      case 'memory':
        final sequenceLength = 3 + ((levelNumber - 41) % 10);
        _objectives.add(LevelObjective(
          id: 'memory_master',
          name: 'Memory Master',
          description: 'Complete sequence of $sequenceLength',
          targetValue: sequenceLength,
          type: ObjectiveType.sequenceLength,
          isRequired: false,
          bonusPoints: 300,
        ));
        break;
        
      case 'reaction':
        _objectives.add(LevelObjective(
          id: 'quick_reflexes',
          name: 'Lightning Reflexes',
          description: 'Average reaction under 500ms',
          targetValue: 500,
          type: ObjectiveType.reactionTime,
          isRequired: false,
          bonusPoints: 200,
        ));
        break;
        
      case 'avoid':
        _objectives.add(LevelObjective(
          id: 'no_mistakes',
          name: 'Perfect Avoidance',
          description: 'No avoid targets touched',
          targetValue: 0,
          type: ObjectiveType.noMisses,
          isRequired: false,
          bonusPoints: 300,
        ));
        break;
        
      case 'deflect':
        _objectives.add(LevelObjective(
          id: 'deflection_master',
          name: 'Deflection Master',
          description: 'Deflect 15+ projectiles',
          targetValue: 15,
          type: ObjectiveType.deflections,
          isRequired: false,
          bonusPoints: 250,
        ));
        break;
    }
  }

  /// Get mechanic name for level number
  String _getMechanicForLevel(int level) {
    final mechanicIndex = ((level - 1) ~/ 10) % 10;
    const mechanics = [
      'tap', 'swipe', 'hold', 'avoid', 'memory',
      'reaction', 'drag', 'rotate', 'deflect', 'chaos'
    ];
    return mechanics[mechanicIndex];
  }

  /// Update score and check objectives
  void updateScore(int points, {String? source}) {
    _baseScore += points;
    _progress['primary_score'] = _baseScore;
    
    // Check score objective
    _checkObjectiveCompletion('primary_score');
    
    // Update progress callback
    onProgressUpdate?.call(Map.from(_progress));
  }

  /// Record target hit
  void recordTargetHit({
    bool isPerfect = false,
    int comboCount = 0,
    int? reactionTime,
    String? targetType,
  }) {
    _totalTargetsHit++;
    
    if (isPerfect) {
      _perfectActions++;
      _progress['perfect_holds'] = _perfectActions;
    }
    
    if (comboCount > _maxCombo) {
      _maxCombo = comboCount;
      _progress['combo_master'] = _maxCombo;
    }
    
    if (reactionTime != null) {
      _reactionTimes.add(reactionTime);
      if (_reactionTimes.isNotEmpty) {
        final avgReaction = _reactionTimes.reduce((a, b) => a + b) / _reactionTimes.length;
        _progress['quick_reflexes'] = avgReaction.round();
      }
    }
    
    // Update accuracy
    _updateAccuracy();
    
    // Check relevant objectives
    _checkObjectiveCompletion('combo_master');
    _checkObjectiveCompletion('perfect_holds');
    _checkObjectiveCompletion('quick_reflexes');
    _checkObjectiveCompletion('accuracy');
    
    onProgressUpdate?.call(Map.from(_progress));
  }

  /// Record target miss
  void recordTargetMiss() {
    _totalTargetsMissed++;
    _updateAccuracy();
    onProgressUpdate?.call(Map.from(_progress));
  }

  /// Record special action (deflection, sequence completion, etc.)
  void recordSpecialAction(String actionType, {int value = 1}) {
    switch (actionType) {
      case 'deflection':
        _progress['deflection_master'] = (_progress['deflection_master'] ?? 0) + value;
        _checkObjectiveCompletion('deflection_master');
        break;
      case 'sequence':
        _progress['memory_master'] = value;
        _checkObjectiveCompletion('memory_master');
        break;
      case 'avoid_hit':
        _progress['no_mistakes'] = (_progress['no_mistakes'] ?? 0) + 1;
        break;
    }
    onProgressUpdate?.call(Map.from(_progress));
  }

  /// Update accuracy calculation
  void _updateAccuracy() {
    final totalAttempts = _totalTargetsHit + _totalTargetsMissed;
    if (totalAttempts > 0) {
      final accuracy = (_totalTargetsHit / totalAttempts * 100).round();
      _progress['accuracy'] = accuracy;
    }
  }

  /// Check if specific objective is completed
  void _checkObjectiveCompletion(String objectiveId) {
    if (_completed[objectiveId] == true) return;
    
    final objective = _objectives.firstWhere(
      (obj) => obj.id == objectiveId,
      orElse: () => throw StateError('Objective not found: $objectiveId'),
    );
    
    final currentProgress = _progress[objectiveId] ?? 0;
    bool isCompleted = false;
    
    switch (objective.type) {
      case ObjectiveType.score:
      case ObjectiveType.targets:
      case ObjectiveType.combo:
      case ObjectiveType.perfectHolds:
      case ObjectiveType.sequenceLength:
      case ObjectiveType.deflections:
        isCompleted = currentProgress >= objective.targetValue;
        break;
        
      case ObjectiveType.accuracy:
        isCompleted = currentProgress >= objective.targetValue;
        break;
        
      case ObjectiveType.time:
        final elapsed = _getElapsedTime().inSeconds;
        isCompleted = elapsed <= objective.targetValue;
        break;
        
      case ObjectiveType.reactionTime:
        isCompleted = currentProgress <= objective.targetValue && currentProgress > 0;
        break;
        
      case ObjectiveType.noMisses:
        isCompleted = currentProgress == 0;
        break;
    }
    
    if (isCompleted) {
      _completed[objectiveId] = true;
      _bonusScore += objective.bonusPoints;
      onObjectiveCompleted?.call(objective);
      HapticFeedback.mediumImpact();
    }
  }

  /// Check if level is completed
  bool checkLevelCompletion() {
    // Check if primary objective is completed
    final primaryCompleted = _completed['primary_score'] == true;
    
    if (primaryCompleted) {
      _completionTime = _getElapsedTime();
      
      // Calculate final score and statistics
      final finalScore = _baseScore + _bonusScore;
      final stats = _generateCompletionStats();
      
      onLevelCompleted?.call(finalScore, stats);
      return true;
    }
    
    return false;
  }

  /// Check if level has failed
  bool checkLevelFailure() {
    if (_currentLevel == null) return false;
    
    // Time limit exceeded
    final elapsed = _getElapsedTime();
    if (elapsed >= _currentLevel!.duration) {
      onLevelFailed?.call('Time limit exceeded');
      return true;
    }
    
    // Other failure conditions can be added here
    return false;
  }

  /// Generate completion statistics
  Map<String, dynamic> _generateCompletionStats() {
    final totalAttempts = _totalTargetsHit + _totalTargetsMissed;
    final accuracy = totalAttempts > 0 ? (_totalTargetsHit / totalAttempts * 100) : 0.0;
    final avgReactionTime = _reactionTimes.isNotEmpty 
        ? _reactionTimes.reduce((a, b) => a + b) / _reactionTimes.length 
        : 0.0;
    
    return {
      'finalScore': _baseScore + _bonusScore,
      'baseScore': _baseScore,
      'bonusScore': _bonusScore,
      'completionTime': _completionTime.inSeconds,
      'accuracy': accuracy.round(),
      'targetsHit': _totalTargetsHit,
      'targetsMissed': _totalTargetsMissed,
      'maxCombo': _maxCombo,
      'perfectActions': _perfectActions,
      'averageReactionTime': avgReactionTime.round(),
      'objectivesCompleted': _completed.values.where((c) => c).length,
      'totalObjectives': _objectives.length,
      'completedObjectives': _completed.entries
          .where((entry) => entry.value)
          .map((entry) => entry.key)
          .toList(),
    };
  }

  /// Get elapsed time since level start
  Duration _getElapsedTime() {
    if (_levelStartTime == null) return Duration.zero;
    return DateTime.now().difference(_levelStartTime!);
  }

  /// Get current objectives
  List<LevelObjective> get objectives => List.unmodifiable(_objectives);
  
  /// Get current progress
  Map<String, int> get progress => Map.unmodifiable(_progress);
  
  /// Get completion status
  Map<String, bool> get completed => Map.unmodifiable(_completed);
  
  /// Get current scores
  int get baseScore => _baseScore;
  int get bonusScore => _bonusScore;
  int get totalScore => _baseScore + _bonusScore;
  
  /// Get statistics
  Map<String, dynamic> get currentStats => {
    'targetsHit': _totalTargetsHit,
    'targetsMissed': _totalTargetsMissed,
    'accuracy': _totalTargetsHit + _totalTargetsMissed > 0 
        ? (_totalTargetsHit / (_totalTargetsHit + _totalTargetsMissed) * 100).round() 
        : 0,
    'maxCombo': _maxCombo,
    'perfectActions': _perfectActions,
    'elapsedTime': _getElapsedTime().inSeconds,
  };
}
