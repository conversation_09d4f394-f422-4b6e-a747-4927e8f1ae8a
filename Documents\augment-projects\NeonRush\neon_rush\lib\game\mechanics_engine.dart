import 'dart:async';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '../constants.dart';
import '../core/constants.dart' as core_constants;
import '../models/target.dart';
import 'target_system.dart';
import 'input_system.dart';

/// Game mechanics that change every 10 levels
enum GameMechanic {
  tap,        // Levels 1-10: Basic tap targets
  swipe,      // Levels 11-20: Swipe in specific directions
  hold,       // Levels 21-30: Hold targets for duration
  avoid,      // Levels 31-40: Avoid red targets, tap others
  memory,     // Levels 41-50: Remember and repeat sequences
  reaction,   // Levels 51-60: Quick reaction to visual cues
  drag,       // Levels 61-70: Drag targets to specific areas
  rotate,     // Levels 71-80: Rotate device or targets
  deflect,    // Levels 81-90: Deflect moving targets
  chaos,      // Levels 91-100: Multiple mechanics combined
}

/// Mechanic-specific configuration
class MechanicConfig {
  final GameMechanic mechanic;
  final Map<String, dynamic> parameters;
  final Duration timeLimit;
  final int targetCount;
  final double difficulty;

  MechanicConfig({
    required this.mechanic,
    required this.parameters,
    required this.timeLimit,
    required this.targetCount,
    required this.difficulty,
  });
}

/// Game mechanics engine that manages level-specific gameplay
class MechanicsEngine {
  final TargetSystem targetSystem;
  final InputSystem inputSystem;
  
  // Current mechanic state
  GameMechanic? _currentMechanic;
  MechanicConfig? _currentConfig;
  int _currentLevel = 1;
  
  // Mechanic-specific state
  final List<Offset> _memorySequence = [];
  final List<Offset> _playerSequence = [];
  int _memoryStep = 0;
  bool _showingSequence = false;
  
  Timer? _reactionTimer;
  DateTime? _reactionStartTime;
  bool _waitingForReaction = false;
  
  final List<Offset> _dragTargets = [];
  final List<Offset> _dragDestinations = [];
  
  double _rotationTarget = 0.0;
  double _currentRotation = 0.0;
  
  final List<MovingTarget> _deflectTargets = [];
  
  // Callbacks
  Function(int points)? onScoreUpdate;
  Function(String message)? onMechanicFeedback;
  Function(int seconds)? onTimerPenalty;
  Function()? onMechanicComplete;
  Function()? onMechanicFailed;

  MechanicsEngine({
    required this.targetSystem,
    required this.inputSystem,
  });

  /// Initialize mechanics for a specific level
  void initializeLevel(int level) {
    _currentLevel = level;
    _currentMechanic = _getMechanicForLevel(level);
    _currentConfig = _getConfigForLevel(level);
    
    _resetMechanicState();
    _setupMechanicCallbacks();
    _startMechanic();
  }

  /// Get the mechanic type for a given level
  GameMechanic _getMechanicForLevel(int level) {
    final mechanicIndex = ((level - 1) ~/ 10) % GameMechanic.values.length;
    return GameMechanic.values[mechanicIndex];
  }

  /// Get configuration for a specific level
  MechanicConfig _getConfigForLevel(int level) {
    final mechanic = _getMechanicForLevel(level);
    final levelInMechanic = ((level - 1) % 10) + 1;
    final difficulty = levelInMechanic / 10.0; // 0.1 to 1.0
    
    switch (mechanic) {
      case GameMechanic.tap:
        return MechanicConfig(
          mechanic: mechanic,
          parameters: {
            'spawnRate': 1.0 + (difficulty * 2.0), // 1.0 to 3.0 targets/sec
            'targetSize': 80.0 - (difficulty * 20.0), // 80 to 60 pixels
          },
          timeLimit: Duration(seconds: 30 - (levelInMechanic * 2)),
          targetCount: 10 + (levelInMechanic * 2),
          difficulty: difficulty,
        );
        
      case GameMechanic.swipe:
        return MechanicConfig(
          mechanic: mechanic,
          parameters: {
            'directions': _getSwipeDirections(levelInMechanic),
            'minDistance': 100.0 + (difficulty * 50.0),
            'maxTime': 1000 - (difficulty * 200), // milliseconds
          },
          timeLimit: Duration(seconds: 45 - (levelInMechanic * 3)),
          targetCount: 8 + levelInMechanic,
          difficulty: difficulty,
        );
        
      case GameMechanic.hold:
        return MechanicConfig(
          mechanic: mechanic,
          parameters: {
            'holdDuration': 1000 + (difficulty * 1000), // 1-2 seconds
            'tolerance': 30.0 - (difficulty * 10.0), // pixel tolerance
          },
          timeLimit: Duration(seconds: 60 - (levelInMechanic * 4)),
          targetCount: 6 + levelInMechanic,
          difficulty: difficulty,
        );
        
      case GameMechanic.avoid:
        return MechanicConfig(
          mechanic: mechanic,
          parameters: {
            'avoidRatio': 0.2 + (difficulty * 0.3), // 20% to 50% avoid targets
            'speed': 50.0 + (difficulty * 100.0), // movement speed
          },
          timeLimit: Duration(seconds: 45 - (levelInMechanic * 2)),
          targetCount: 15 + (levelInMechanic * 2),
          difficulty: difficulty,
        );
        
      case GameMechanic.memory:
        return MechanicConfig(
          mechanic: mechanic,
          parameters: {
            'sequenceLength': 3 + levelInMechanic, // 4 to 13 steps
            'showTime': 800 - (difficulty * 200), // milliseconds per step
            'maxErrors': max(1, 3 - (levelInMechanic ~/ 3)),
          },
          timeLimit: Duration(seconds: 90 - (levelInMechanic * 5)),
          targetCount: 1, // One sequence
          difficulty: difficulty,
        );
        
      case GameMechanic.reaction:
        return MechanicConfig(
          mechanic: mechanic,
          parameters: {
            'reactionWindow': 1000 - (difficulty * 300), // 1000ms to 700ms
            'falsePositives': difficulty > 0.5, // Add fake cues
            'cueTypes': _getReactionCues(levelInMechanic),
          },
          timeLimit: Duration(seconds: 60 - (levelInMechanic * 3)),
          targetCount: 10 + levelInMechanic,
          difficulty: difficulty,
        );
        
      case GameMechanic.drag:
        return MechanicConfig(
          mechanic: mechanic,
          parameters: {
            'dragDistance': 150.0 + (difficulty * 100.0),
            'accuracy': 50.0 - (difficulty * 20.0), // pixel accuracy required
            'multiDrag': levelInMechanic > 5, // Multiple simultaneous drags
          },
          timeLimit: Duration(seconds: 75 - (levelInMechanic * 5)),
          targetCount: 8 + levelInMechanic,
          difficulty: difficulty,
        );
        
      case GameMechanic.rotate:
        return MechanicConfig(
          mechanic: mechanic,
          parameters: {
            'rotationAngle': 90.0 + (difficulty * 180.0), // 90° to 270°
            'rotationSpeed': 2.0 + (difficulty * 3.0), // degrees per frame
            'precision': 15.0 - (difficulty * 10.0), // degree tolerance
          },
          timeLimit: Duration(seconds: 60 - (levelInMechanic * 4)),
          targetCount: 6 + levelInMechanic,
          difficulty: difficulty,
        );
        
      case GameMechanic.deflect:
        return MechanicConfig(
          mechanic: mechanic,
          parameters: {
            'projectileSpeed': 100.0 + (difficulty * 150.0),
            'projectileCount': 1 + (levelInMechanic ~/ 2),
            'deflectionAngle': 45.0 + (difficulty * 45.0),
          },
          timeLimit: Duration(seconds: 90 - (levelInMechanic * 6)),
          targetCount: 5 + levelInMechanic,
          difficulty: difficulty,
        );
        
      case GameMechanic.chaos:
        return MechanicConfig(
          mechanic: mechanic,
          parameters: {
            'activeMechanics': _getChaosMechanics(levelInMechanic),
            'switchInterval': 5000 - (difficulty * 1000), // milliseconds
            'complexity': difficulty,
          },
          timeLimit: Duration(seconds: 120 - (levelInMechanic * 8)),
          targetCount: 20 + (levelInMechanic * 3),
          difficulty: difficulty,
        );
    }
  }

  /// Get swipe directions for swipe mechanic levels
  List<SwipeDirection> _getSwipeDirections(int levelInMechanic) {
    switch (levelInMechanic) {
      case 1:
      case 2:
        return [SwipeDirection.up, SwipeDirection.down];
      case 3:
      case 4:
        return [SwipeDirection.left, SwipeDirection.right];
      case 5:
      case 6:
        return [SwipeDirection.up, SwipeDirection.down, SwipeDirection.left, SwipeDirection.right];
      default:
        return SwipeDirection.values; // All 8 directions
    }
  }

  /// Get reaction cue types for reaction mechanic levels
  List<String> _getReactionCues(int levelInMechanic) {
    final cues = <String>['color', 'size'];
    if (levelInMechanic > 3) cues.add('shape');
    if (levelInMechanic > 6) cues.add('sound');
    if (levelInMechanic > 8) cues.add('vibration');
    return cues;
  }

  /// Get active mechanics for chaos mode
  List<GameMechanic> _getChaosMechanics(int levelInMechanic) {
    final mechanics = <GameMechanic>[GameMechanic.tap, GameMechanic.swipe];
    if (levelInMechanic > 2) mechanics.add(GameMechanic.hold);
    if (levelInMechanic > 4) mechanics.add(GameMechanic.avoid);
    if (levelInMechanic > 6) mechanics.add(GameMechanic.drag);
    if (levelInMechanic > 8) mechanics.add(GameMechanic.reaction);
    return mechanics;
  }

  /// Reset all mechanic-specific state
  void _resetMechanicState() {
    _memorySequence.clear();
    _playerSequence.clear();
    _memoryStep = 0;
    _showingSequence = false;
    
    _reactionTimer?.cancel();
    _reactionStartTime = null;
    _waitingForReaction = false;
    
    _dragTargets.clear();
    _dragDestinations.clear();
    
    _rotationTarget = 0.0;
    _currentRotation = 0.0;
    
    _deflectTargets.clear();
  }

  /// Setup input callbacks for current mechanic
  void _setupMechanicCallbacks() {
    inputSystem.clearCallbacks();
    
    switch (_currentMechanic!) {
      case GameMechanic.tap:
        inputSystem.registerCallback(InputType.tap, _handleTapMechanic);
        break;
      case GameMechanic.swipe:
        inputSystem.registerCallback(InputType.swipe, _handleSwipeMechanic);
        break;
      case GameMechanic.hold:
        inputSystem.registerCallback(InputType.hold, _handleHoldMechanic);
        break;
      case GameMechanic.avoid:
        inputSystem.registerCallback(InputType.tap, _handleAvoidMechanic);
        break;
      case GameMechanic.memory:
        inputSystem.registerCallback(InputType.tap, _handleMemoryMechanic);
        break;
      case GameMechanic.reaction:
        inputSystem.registerCallback(InputType.tap, _handleReactionMechanic);
        break;
      case GameMechanic.drag:
        inputSystem.registerCallback(InputType.drag, _handleDragMechanic);
        break;
      case GameMechanic.rotate:
        inputSystem.registerCallback(InputType.rotate, _handleRotateMechanic);
        break;
      case GameMechanic.deflect:
        inputSystem.registerCallback(InputType.tap, _handleDeflectMechanic);
        break;
      case GameMechanic.chaos:
        _setupChaosCallbacks();
        break;
    }
  }

  /// Start the current mechanic
  void _startMechanic() {
    switch (_currentMechanic!) {
      case GameMechanic.memory:
        _startMemorySequence();
        break;
      case GameMechanic.reaction:
        _startReactionTest();
        break;
      case GameMechanic.deflect:
        _startDeflectMode();
        break;
      case GameMechanic.chaos:
        _startChaosMode();
        break;
      default:
        // Other mechanics start automatically through target system
        break;
    }
  }

  // Mechanic handlers
  void _handleTapMechanic(InputEvent event) {
    final target = targetSystem.getTargetAt(event.position);
    if (target != null) {
      // Handle v2.0 target types
      switch (target.type) {
        case TargetType.easyBall:
        case TargetType.mediumBall:
        case TargetType.hardBall:
          targetSystem.hitTarget(event.position);
          final score = target.getScore();
          onScoreUpdate?.call(score);
          onMechanicFeedback?.call('Target hit! +$score');
          break;
        case TargetType.redEnemy:
          // Red enemy explodes and causes penalty
          targetSystem.hitTarget(event.position);
          onScoreUpdate?.call(target.penalty);
          onMechanicFeedback?.call('Red enemy hit! ${target.penalty}');
          HapticFeedback.heavyImpact();
          break;
        case TargetType.yellowEnemy:
          // Yellow enemy reduces timer
          targetSystem.hitTarget(event.position);
          onTimerPenalty?.call(target.penalty.abs()); // Convert to positive for timer reduction
          onMechanicFeedback?.call('Yellow enemy hit! -${target.penalty.abs()}s');
          HapticFeedback.mediumImpact();
          break;
      }
    }
  }

  void _handleSwipeMechanic(InputEvent event) {
    if (event.swipeDirection == null) return;

    final target = targetSystem.getTargetAt(event.position);
    if (target != null) {
      // In swipe mode, any target can be hit with swipe for bonus
      switch (target.type) {
        case TargetType.easyBall:
        case TargetType.mediumBall:
        case TargetType.hardBall:
          targetSystem.hitTarget(event.position);
          final score = target.getScore() * 2; // Bonus for swipe
          onScoreUpdate?.call(score);
          onMechanicFeedback?.call('Perfect swipe! +$score');
          break;
        case TargetType.redEnemy:
          targetSystem.hitTarget(event.position);
          onScoreUpdate?.call(target.penalty);
          onMechanicFeedback?.call('Red enemy hit! ${target.penalty}');
          HapticFeedback.heavyImpact();
          break;
        case TargetType.yellowEnemy:
          targetSystem.hitTarget(event.position);
          onTimerPenalty?.call(target.penalty.abs());
          onMechanicFeedback?.call('Yellow enemy hit! -${target.penalty.abs()}s');
          HapticFeedback.mediumImpact();
          break;
      }
    }
  }

  void _handleHoldMechanic(InputEvent event) {
    final target = targetSystem.getTargetAt(event.position);
    if (target != null) {
      final holdDuration = _currentConfig!.parameters['holdDuration'] as int;
      if (event.duration != null && event.duration!.inMilliseconds >= holdDuration) {
        // In hold mode, any target can be held for bonus
        switch (target.type) {
          case TargetType.easyBall:
          case TargetType.mediumBall:
          case TargetType.hardBall:
            targetSystem.hitTarget(event.position);
            final score = target.getScore() * 3; // Bonus for hold
            onScoreUpdate?.call(score);
            onMechanicFeedback?.call('Perfect hold! +$score');
            HapticFeedback.mediumImpact();
            break;
          case TargetType.redEnemy:
            targetSystem.hitTarget(event.position);
            onScoreUpdate?.call(target.penalty);
            onMechanicFeedback?.call('Red enemy held! ${target.penalty}');
            HapticFeedback.heavyImpact();
            break;
          case TargetType.yellowEnemy:
            targetSystem.hitTarget(event.position);
            onTimerPenalty?.call(target.penalty.abs());
            onMechanicFeedback?.call('Yellow enemy held! -${target.penalty.abs()}s');
            HapticFeedback.mediumImpact();
            break;
        }
      }
    }
  }

  void _handleAvoidMechanic(InputEvent event) {
    final target = targetSystem.getTargetAt(event.position);
    if (target != null) {
      // In avoid mode, enemies should be avoided, balls can be hit
      switch (target.type) {
        case TargetType.easyBall:
        case TargetType.mediumBall:
        case TargetType.hardBall:
          // Safe targets - can be hit
          targetSystem.hitTarget(event.position);
          final score = target.getScore();
          onScoreUpdate?.call(score);
          onMechanicFeedback?.call('Safe target! +$score');
          break;
        case TargetType.redEnemy:
        case TargetType.yellowEnemy:
          // Enemies should be avoided - penalty for touching
          targetSystem.hitTarget(event.position);
          onScoreUpdate?.call(-20); // Penalty for touching enemy
          onMechanicFeedback?.call('Enemy touched! -20');
          HapticFeedback.heavyImpact();
          break;
      }
    }
  }

  void _handleMemoryMechanic(InputEvent event) {
    if (_showingSequence) return; // Don't accept input during sequence display

    _playerSequence.add(event.position);

    // Check if the position matches the expected sequence step
    final expectedPosition = _memorySequence[_playerSequence.length - 1];
    final distance = (event.position - expectedPosition).distance;

    if (distance <= 50.0) { // Within tolerance
      onMechanicFeedback?.call('Correct! ${_playerSequence.length}/${_memorySequence.length}');

      if (_playerSequence.length == _memorySequence.length) {
        // Sequence completed successfully
        onScoreUpdate?.call(100 * _memorySequence.length);
        onMechanicFeedback?.call('Sequence complete! +${100 * _memorySequence.length}');
        onMechanicComplete?.call();
      }
    } else {
      // Wrong position
      onMechanicFeedback?.call('Wrong position! Try again.');
      _playerSequence.clear(); // Reset player sequence
    }
  }

  void _handleReactionMechanic(InputEvent event) {
    if (!_waitingForReaction || _reactionStartTime == null) return;

    final reactionTime = DateTime.now().difference(_reactionStartTime!);
    final reactionWindow = _currentConfig!.parameters['reactionWindow'] as int;

    if (reactionTime.inMilliseconds <= reactionWindow) {
      final points = max(10, 100 - reactionTime.inMilliseconds ~/ 10);
      onScoreUpdate?.call(points);
      onMechanicFeedback?.call('Quick reaction! +$points (${reactionTime.inMilliseconds}ms)');
      _waitingForReaction = false;
      _startReactionTest(); // Start next reaction test
    } else {
      onMechanicFeedback?.call('Too slow!');
      _waitingForReaction = false;
      _startReactionTest();
    }
  }

  void _handleDragMechanic(InputEvent event) {
    final target = targetSystem.getTargetAt(event.position);
    if (target != null) {
      // In drag mode, any target can be dragged for bonus
      switch (target.type) {
        case TargetType.easyBall:
        case TargetType.mediumBall:
        case TargetType.hardBall:
          // Check if dragged to correct destination
          final destination = _findNearestDestination(event.position);
          if (destination != null) {
            final accuracy = _currentConfig!.parameters['accuracy'] as double;
            final distance = (event.position - destination).distance;

            if (distance <= accuracy) {
              targetSystem.hitTarget(event.position);
              final score = target.getScore() * 2; // Bonus for drag
              onScoreUpdate?.call(score);
              onMechanicFeedback?.call('Perfect drag! +$score');
              _dragDestinations.remove(destination);
            }
          }
          break;
        case TargetType.redEnemy:
        case TargetType.yellowEnemy:
          // Enemies can't be dragged effectively
          targetSystem.hitTarget(event.position);
          onScoreUpdate?.call(-5);
          onMechanicFeedback?.call('Can\'t drag enemies! -5');
          break;
      }
    }
  }

  void _handleRotateMechanic(InputEvent event) {
    if (event.angle == null) return;

    _currentRotation += event.angle!;
    final precision = _currentConfig!.parameters['precision'] as double;
    final angleDiff = (_currentRotation - _rotationTarget).abs();

    if (angleDiff <= precision * (pi / 180)) { // Convert to radians
      onScoreUpdate?.call(50);
      onMechanicFeedback?.call('Perfect rotation! +50');
      _generateNewRotationTarget();
    }
  }

  void _handleDeflectMechanic(InputEvent event) {
    // Find the nearest moving target to deflect
    MovingTarget? nearestTarget;
    double nearestDistance = double.infinity;

    for (final target in _deflectTargets) {
      if (!target.isActive) continue;
      final distance = (event.position - target.position).distance;
      if (distance < nearestDistance && distance <= 50.0) {
        nearestDistance = distance;
        nearestTarget = target;
      }
    }

    if (nearestTarget != null) {
      // Deflect the target
      final deflectionAngle = _currentConfig!.parameters['deflectionAngle'] as double;
      final newDirection = _calculateDeflectionDirection(nearestTarget.velocity, deflectionAngle);
      nearestTarget.velocity = newDirection;

      onScoreUpdate?.call(25);
      onMechanicFeedback?.call('Deflected! +25');
      HapticFeedback.lightImpact();
    }
  }

  void _setupChaosCallbacks() {
    // Register callbacks for all active mechanics in chaos mode
    final activeMechanics = _currentConfig!.parameters['activeMechanics'] as List<GameMechanic>;

    for (final mechanic in activeMechanics) {
      switch (mechanic) {
        case GameMechanic.tap:
          inputSystem.registerCallback(InputType.tap, _handleTapMechanic);
          break;
        case GameMechanic.swipe:
          inputSystem.registerCallback(InputType.swipe, _handleSwipeMechanic);
          break;
        case GameMechanic.hold:
          inputSystem.registerCallback(InputType.hold, _handleHoldMechanic);
          break;
        case GameMechanic.drag:
          inputSystem.registerCallback(InputType.drag, _handleDragMechanic);
          break;
        default:
          break;
      }
    }
  }

  void _startMemorySequence() {
    _memorySequence.clear();
    _playerSequence.clear();

    final sequenceLength = _currentConfig!.parameters['sequenceLength'] as int;
    final showTime = _currentConfig!.parameters['showTime'] as int;

    // Generate random sequence positions
    final random = Random();
    for (int i = 0; i < sequenceLength; i++) {
      _memorySequence.add(Offset(
        random.nextDouble() * 300 + 50, // Screen bounds
        random.nextDouble() * 400 + 100,
      ));
    }

    // Show sequence to player
    _showingSequence = true;
    _showMemoryStep(0, showTime);
  }

  void _showMemoryStep(int step, int showTime) {
    if (step >= _memorySequence.length) {
      _showingSequence = false;
      onMechanicFeedback?.call('Now repeat the sequence!');
      return;
    }

    final position = _memorySequence[step];
    onMechanicFeedback?.call('Step ${step + 1}/${_memorySequence.length}');

    // Create a temporary target at this position
    // This would integrate with the target system to show a highlighted target

    Timer(Duration(milliseconds: showTime), () {
      _showMemoryStep(step + 1, showTime);
    });
  }

  void _startReactionTest() {
    if (_waitingForReaction) return;

    final random = Random();
    final delay = 1000 + random.nextInt(3000); // 1-4 second delay

    Timer(Duration(milliseconds: delay), () {
      _waitingForReaction = true;
      _reactionStartTime = DateTime.now();
      onMechanicFeedback?.call('REACT NOW!');
      HapticFeedback.heavyImpact();

      // Auto-timeout after reaction window
      final reactionWindow = _currentConfig!.parameters['reactionWindow'] as int;
      Timer(Duration(milliseconds: reactionWindow), () {
        if (_waitingForReaction) {
          _waitingForReaction = false;
          onMechanicFeedback?.call('Too slow!');
          _startReactionTest();
        }
      });
    });
  }

  void _startDeflectMode() {
    _deflectTargets.clear();
    _spawnDeflectTarget();
  }

  void _spawnDeflectTarget() {
    final random = Random();
    final speed = _currentConfig!.parameters['projectileSpeed'] as double;

    final target = MovingTarget(
      position: Offset(
        random.nextDouble() * 400,
        random.nextDouble() * 200 + 100,
      ),
      velocity: Offset(
        (random.nextDouble() - 0.5) * speed,
        (random.nextDouble() - 0.5) * speed,
      ),
      color: NeonColors.error,
    );

    _deflectTargets.add(target);
  }

  void _startChaosMode() {
    final switchInterval = _currentConfig!.parameters['switchInterval'] as int;

    // Switch between different mechanics periodically
    Timer.periodic(Duration(milliseconds: switchInterval), (timer) {
      final activeMechanics = _currentConfig!.parameters['activeMechanics'] as List<GameMechanic>;
      final random = Random();
      final newMechanic = activeMechanics[random.nextInt(activeMechanics.length)];

      onMechanicFeedback?.call('Switching to ${_getMechanicName(newMechanic)}!');
    });
  }

  String _getMechanicName(GameMechanic mechanic) {
    switch (mechanic) {
      case GameMechanic.tap:
        return 'Tap';
      case GameMechanic.swipe:
        return 'Swipe';
      case GameMechanic.hold:
        return 'Hold';
      case GameMechanic.avoid:
        return 'Avoid';
      case GameMechanic.drag:
        return 'Drag';
      default:
        return 'Unknown';
    }
  }

  /// Update the mechanics engine
  void update(double deltaTime) {
    switch (_currentMechanic!) {
      case GameMechanic.deflect:
        _updateDeflectTargets(deltaTime);
        break;
      case GameMechanic.rotate:
        _updateRotation(deltaTime);
        break;
      default:
        break;
    }
  }

  void _updateDeflectTargets(double deltaTime) {
    for (final target in _deflectTargets) {
      target.update(deltaTime);

      // Remove targets that go off screen
      if (target.position.dx < -50 || target.position.dx > 450 ||
          target.position.dy < -50 || target.position.dy > 850) {
        target.isActive = false;
      }
    }

    // Remove inactive targets
    _deflectTargets.removeWhere((target) => !target.isActive);

    // Spawn new targets periodically
    final random = Random();
    if (random.nextDouble() < 0.02) { // 2% chance per frame
      _spawnDeflectTarget();
    }
  }

  void _updateRotation(double deltaTime) {
    // Smooth rotation interpolation
    final rotationSpeed = _currentConfig!.parameters['rotationSpeed'] as double;
    final diff = _rotationTarget - _currentRotation;

    if (diff.abs() > 0.1) {
      _currentRotation += diff.sign * rotationSpeed * deltaTime;
    }
  }

  // Helper methods

  Offset? _findNearestDestination(Offset position) {
    if (_dragDestinations.isEmpty) return null;

    Offset? nearest;
    double nearestDistance = double.infinity;

    for (final destination in _dragDestinations) {
      final distance = (position - destination).distance;
      if (distance < nearestDistance) {
        nearestDistance = distance;
        nearest = destination;
      }
    }

    return nearest;
  }

  void _generateNewRotationTarget() {
    final random = Random();
    _rotationTarget = random.nextDouble() * 2 * pi; // 0 to 360 degrees
  }

  Offset _calculateDeflectionDirection(Offset velocity, double deflectionAngle) {
    final currentAngle = atan2(velocity.dy, velocity.dx);
    final newAngle = currentAngle + (deflectionAngle * pi / 180);
    final speed = velocity.distance;

    return Offset(
      cos(newAngle) * speed,
      sin(newAngle) * speed,
    );
  }

  /// Get current mechanic info for UI display
  String getMechanicName() {
    switch (_currentMechanic!) {
      case GameMechanic.tap:
        return 'Tap Targets';
      case GameMechanic.swipe:
        return 'Swipe Directions';
      case GameMechanic.hold:
        return 'Hold Targets';
      case GameMechanic.avoid:
        return 'Avoid Red Targets';
      case GameMechanic.memory:
        return 'Memory Sequence';
      case GameMechanic.reaction:
        return 'Quick Reaction';
      case GameMechanic.drag:
        return 'Drag Targets';
      case GameMechanic.rotate:
        return 'Rotate Targets';
      case GameMechanic.deflect:
        return 'Deflect Projectiles';
      case GameMechanic.chaos:
        return 'Chaos Mode';
    }
  }

  String getMechanicDescription() {
    switch (_currentMechanic!) {
      case GameMechanic.tap:
        return 'Tap the glowing targets as they appear';
      case GameMechanic.swipe:
        return 'Swipe in the direction shown on targets';
      case GameMechanic.hold:
        return 'Hold targets until the timer completes';
      case GameMechanic.avoid:
        return 'Tap blue targets, avoid red ones';
      case GameMechanic.memory:
        return 'Remember and repeat the sequence';
      case GameMechanic.reaction:
        return 'React quickly when targets change';
      case GameMechanic.drag:
        return 'Drag targets to their destinations';
      case GameMechanic.rotate:
        return 'Rotate targets to match the pattern';
      case GameMechanic.deflect:
        return 'Deflect projectiles away from targets';
      case GameMechanic.chaos:
        return 'Multiple mechanics combined!';
    }
  }

  /// Dispose resources
  void dispose() {
    _reactionTimer?.cancel();
  }
}

/// Moving target for deflect mechanic
class MovingTarget {
  Offset position;
  Offset velocity;
  double size;
  Color color;
  bool isActive;

  MovingTarget({
    required this.position,
    required this.velocity,
    this.size = 20.0,
    this.color = Colors.red,
    this.isActive = true,
  });

  void update(double deltaTime) {
    if (isActive) {
      position += velocity * deltaTime;
    }
  }
}
