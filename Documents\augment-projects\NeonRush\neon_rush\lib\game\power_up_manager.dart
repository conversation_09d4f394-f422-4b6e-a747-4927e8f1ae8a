import 'dart:async';
import 'package:flutter/foundation.dart';
import '../models/power_up.dart';
import '../core/constants.dart';

/// Manages active power-ups during gameplay
class PowerUpManager extends ChangeNotifier {
  final Map<String, ActivePowerUp> _activePowerUps = {};
  final Map<String, Timer> _powerUpTimers = {};

  /// Get all currently active power-ups
  Map<String, ActivePowerUp> get activePowerUps => Map.unmodifiable(_activePowerUps);

  /// Check if a specific power-up is currently active
  bool isPowerUpActive(String powerUpId) {
    return _activePowerUps.containsKey(powerUpId);
  }

  /// Get the active power-up instance
  ActivePowerUp? getActivePowerUp(String powerUpId) {
    return _activePowerUps[powerUpId];
  }

  /// Activate a power-up
  void activatePowerUp(PowerUp powerUp) {
    // Cancel existing timer if power-up is already active
    if (_powerUpTimers.containsKey(powerUp.id)) {
      _powerUpTimers[powerUp.id]?.cancel();
    }

    // Create active power-up instance
    final activePowerUp = ActivePowerUp(
      powerUp: powerUp,
      activatedAt: DateTime.now(),
      remainingDuration: powerUp.duration,
    );

    _activePowerUps[powerUp.id] = activePowerUp;

    // Handle instant effects
    if (powerUp.effects['instantEffect'] == true) {
      _handleInstantEffect(powerUp);
      // Remove instant effect power-ups immediately
      _activePowerUps.remove(powerUp.id);
    } else {
      // Set up timer for duration-based power-ups
      _powerUpTimers[powerUp.id] = Timer(powerUp.duration, () {
        deactivatePowerUp(powerUp.id);
      });
    }

    notifyListeners();
  }

  /// Deactivate a power-up
  void deactivatePowerUp(String powerUpId) {
    _activePowerUps.remove(powerUpId);
    _powerUpTimers[powerUpId]?.cancel();
    _powerUpTimers.remove(powerUpId);
    notifyListeners();
  }

  /// Clear all active power-ups
  void clearAllPowerUps() {
    for (final timer in _powerUpTimers.values) {
      timer.cancel();
    }
    _activePowerUps.clear();
    _powerUpTimers.clear();
    notifyListeners();
  }

  /// Handle instant effect power-ups
  void _handleInstantEffect(PowerUp powerUp) {
    switch (powerUp.type) {
      case PowerUpType.timeBoost:
        // Time boost effect will be handled by the game logic
        break;
      case PowerUpType.neonBomb:
        // Neon bomb effect will be handled by the game logic
        break;
      default:
        break;
    }
  }

  /// Get slow motion factor (for slow motion power-up)
  double getSlowMotionFactor() {
    final slowMotionPowerUp = _activePowerUps[PowerUps.slowMotion.id];
    if (slowMotionPowerUp != null) {
      return slowMotionPowerUp.powerUp.effects['slowFactor'] ?? 1.0;
    }
    return 1.0;
  }

  /// Check if shield is active
  bool isShieldActive() {
    return isPowerUpActive(PowerUps.shield.id);
  }

  /// Use shield (when player would fail)
  bool useShield() {
    final shieldPowerUp = _activePowerUps[PowerUps.shield.id];
    if (shieldPowerUp != null) {
      final blockCount = shieldPowerUp.powerUp.effects['blockCount'] ?? 1;
      if (blockCount > 1) {
        // Reduce block count
        final updatedEffects = Map<String, dynamic>.from(shieldPowerUp.powerUp.effects);
        updatedEffects['blockCount'] = blockCount - 1;
        final updatedPowerUp = shieldPowerUp.powerUp.copyWith(effects: updatedEffects);
        _activePowerUps[PowerUps.shield.id] = shieldPowerUp.copyWith(powerUp: updatedPowerUp);
      } else {
        // Shield exhausted, remove it
        deactivatePowerUp(PowerUps.shield.id);
      }
      notifyListeners();
      return true;
    }
    return false;
  }

  /// Get magnet touch multiplier
  double getMagnetTouchMultiplier() {
    final magnetPowerUp = _activePowerUps[PowerUps.magnetTouch.id];
    if (magnetPowerUp != null) {
      return magnetPowerUp.powerUp.effects['hitboxMultiplier'] ?? 1.0;
    }
    return 1.0;
  }

  /// Get magnet range
  double getMagnetRange() {
    final magnetPowerUp = _activePowerUps[PowerUps.magnetTouch.id];
    if (magnetPowerUp != null) {
      return magnetPowerUp.powerUp.effects['magnetRange'] ?? 0.0;
    }
    return 0.0;
  }

  /// Check if neon bomb should clear all targets
  bool shouldClearAllTargets() {
    return isPowerUpActive(PowerUps.neonBomb.id);
  }

  /// Get time boost amount
  double getTimeBoostAmount() {
    // This is for instant effect, so we check if it was just activated
    return 5.0; // 5 seconds as defined in the power-up
  }

  /// Check if multiball is active
  bool isMultiballActive() {
    return isPowerUpActive(PowerUps.multiballT1.id);
  }

  /// Get multiball effects
  Map<String, dynamic>? getMultiballEffects() {
    final multiballPowerUp = _activePowerUps[PowerUps.multiballT1.id];
    return multiballPowerUp?.powerUp.effects;
  }

  /// Update remaining durations (call this periodically)
  void updateDurations() {
    final now = DateTime.now();
    final toRemove = <String>[];

    for (final entry in _activePowerUps.entries) {
      final activePowerUp = entry.value;
      final elapsed = now.difference(activePowerUp.activatedAt);
      final remaining = activePowerUp.powerUp.duration - elapsed;

      if (remaining.isNegative) {
        toRemove.add(entry.key);
      } else {
        _activePowerUps[entry.key] = activePowerUp.copyWith(
          remainingDuration: remaining,
        );
      }
    }

    for (final id in toRemove) {
      deactivatePowerUp(id);
    }

    if (toRemove.isNotEmpty) {
      notifyListeners();
    }
  }

  @override
  void dispose() {
    clearAllPowerUps();
    super.dispose();
  }
}

/// Represents an active power-up instance
class ActivePowerUp {
  final PowerUp powerUp;
  final DateTime activatedAt;
  final Duration remainingDuration;

  const ActivePowerUp({
    required this.powerUp,
    required this.activatedAt,
    required this.remainingDuration,
  });

  /// Creates a copy with modified properties
  ActivePowerUp copyWith({
    PowerUp? powerUp,
    DateTime? activatedAt,
    Duration? remainingDuration,
  }) {
    return ActivePowerUp(
      powerUp: powerUp ?? this.powerUp,
      activatedAt: activatedAt ?? this.activatedAt,
      remainingDuration: remainingDuration ?? this.remainingDuration,
    );
  }

  /// Get progress as a value between 0.0 and 1.0
  double get progress {
    final totalDuration = powerUp.duration.inMilliseconds;
    final remainingMs = remainingDuration.inMilliseconds;
    return 1.0 - (remainingMs / totalDuration).clamp(0.0, 1.0);
  }

  /// Check if power-up is about to expire (less than 20% remaining)
  bool get isAboutToExpire {
    return progress > 0.8;
  }
}
