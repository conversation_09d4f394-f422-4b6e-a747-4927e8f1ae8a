import 'dart:math';
import 'package:flutter/material.dart';
import '../core/constants.dart';
import '../models/target.dart';

/// V2.0 Target System Manager
/// Manages spawning and updating of v2.0 target types
class TargetSystem {
  final List<GameTarget> _targets = [];
  final Random _random = Random();
  
  // Spawn configuration
  late Size _screenSize;
  late Rect _spawnBounds;
  late Rect _gameBounds;
  
  // Target generation settings
  double _spawnRate = 1.0; // targets per second
  int _maxTargets = 10;
  DateTime _lastSpawn = DateTime.now();
  
  // Level configuration
  int _currentLevel = 1;
  GameMode _gameMode = GameMode.tap;
  
  List<GameTarget> get targets => List.unmodifiable(_targets);
  int get activeTargetCount => _targets.where((t) => t.isActive).length;

  /// Initialize the target system
  void initialize({
    required Size screenSize,
    required int level,
    required GameMode gameMode,
  }) {
    _screenSize = screenSize;
    _currentLevel = level;
    _gameMode = gameMode;
    
    // Set up bounds (leave space for UI)
    _spawnBounds = Rect.fromLTWH(
      50,
      150,
      screenSize.width - 100,
      screenSize.height - 300,
    );
    
    _gameBounds = Rect.fromLTWH(
      0,
      100,
      screenSize.width,
      screenSize.height - 200,
    );
    
    // Configure spawn rate based on level
    _spawnRate = _calculateSpawnRate(level);
    _maxTargets = _calculateMaxTargets(level);
  }

  /// Update all targets
  void update(double deltaTime) {
    // Update existing targets
    for (final target in _targets) {
      target.update(deltaTime);
      
      // Handle split targets (Hard Ball fragments)
      if (target.splitTargets != null) {
        for (final fragment in target.splitTargets!) {
          fragment.update(deltaTime);
        }
        // Remove inactive fragments
        target.splitTargets!.removeWhere((fragment) => !fragment.isActive);
      }
    }
    
    // Remove inactive targets
    _targets.removeWhere((target) => !target.isActive);
    
    // Spawn new targets if needed
    _trySpawnTarget();
  }

  /// Try to spawn a new target based on spawn rate
  void _trySpawnTarget() {
    final now = DateTime.now();
    final timeSinceLastSpawn = now.difference(_lastSpawn).inMilliseconds / 1000.0;
    final spawnInterval = 1.0 / _spawnRate;
    
    if (timeSinceLastSpawn >= spawnInterval && activeTargetCount < _maxTargets) {
      spawnTarget();
      _lastSpawn = now;
    }
  }

  /// Spawn a new target based on level and game mode
  void spawnTarget() {
    final targetType = _selectTargetType();
    final position = _generateSpawnPosition(targetType);
    final movement = _getMovementForType(targetType);
    final velocity = _generateVelocity(targetType, movement);
    
    final target = GameTarget(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      type: targetType,
      position: position,
      movement: movement,
      velocity: velocity,
    );
    
    _targets.add(target);
  }

  TargetType _selectTargetType() {
    // Select target type based on game mode and level progression
    switch (_gameMode) {
      case GameMode.tap:
        return _selectTapModeTarget();
      case GameMode.avoid:
        return _selectAvoidModeTarget();
      default:
        return _selectTapModeTarget();
    }
  }

  TargetType _selectTapModeTarget() {
    // Distribution based on level difficulty
    final random = _random.nextDouble();
    
    if (_currentLevel <= 20) {
      // Early levels: mostly easy and medium balls
      if (random < 0.6) return TargetType.easyBall;
      if (random < 0.9) return TargetType.mediumBall;
      return TargetType.hardBall;
    } else if (_currentLevel <= 50) {
      // Mid levels: introduce enemies
      if (random < 0.4) return TargetType.easyBall;
      if (random < 0.7) return TargetType.mediumBall;
      if (random < 0.85) return TargetType.hardBall;
      if (random < 0.95) return TargetType.redEnemy;
      return TargetType.yellowEnemy;
    } else {
      // Late levels: more enemies and hard balls
      if (random < 0.3) return TargetType.easyBall;
      if (random < 0.5) return TargetType.mediumBall;
      if (random < 0.7) return TargetType.hardBall;
      if (random < 0.85) return TargetType.redEnemy;
      return TargetType.yellowEnemy;
    }
  }

  TargetType _selectAvoidModeTarget() {
    // In avoid mode, spawn mostly enemies to avoid
    final random = _random.nextDouble();
    if (random < 0.7) return TargetType.redEnemy;
    return TargetType.yellowEnemy;
  }

  Offset _generateSpawnPosition(TargetType type) {
    switch (type) {
      case TargetType.mediumBall:
        // Medium balls fall from top
        return Offset(
          _spawnBounds.left + _random.nextDouble() * _spawnBounds.width,
          _spawnBounds.top - 50, // Start above visible area
        );
      default:
        // Other targets spawn randomly in bounds
        return Offset(
          _spawnBounds.left + _random.nextDouble() * _spawnBounds.width,
          _spawnBounds.top + _random.nextDouble() * _spawnBounds.height,
        );
    }
  }

  TargetMovement _getMovementForType(TargetType type) {
    switch (type) {
      case TargetType.mediumBall:
        return TargetMovement.falling;
      case TargetType.redEnemy:
        return TargetMovement.random;
      default:
        return TargetMovement.stationary;
    }
  }

  Offset _generateVelocity(TargetType type, TargetMovement movement) {
    switch (movement) {
      case TargetMovement.falling:
        return Offset(0, 100.0 + _random.nextDouble() * 50.0); // Fall speed
      case TargetMovement.random:
        final speed = 50.0 + _random.nextDouble() * 100.0;
        final angle = _random.nextDouble() * 2 * pi;
        return Offset(cos(angle) * speed, sin(angle) * speed);
      default:
        return const Offset(0, 0);
    }
  }

  double _calculateSpawnRate(int level) {
    // Increase spawn rate with level, but cap it - made more aggressive for better gameplay
    return (1.0 + (level * 0.1)).clamp(1.0, 4.0);
  }

  int _calculateMaxTargets(int level) {
    // Increase max targets with level - more targets on screen
    return (5 + (level / 5).floor()).clamp(5, 15);
  }

  /// Handle target hit
  bool hitTarget(Offset position) {
    // Check main targets
    for (final target in _targets) {
      if (target.isActive && target.containsPoint(position)) {
        target.hit();
        return true;
      }
      
      // Check split targets (Hard Ball fragments)
      if (target.splitTargets != null) {
        for (final fragment in target.splitTargets!) {
          if (fragment.isActive && fragment.containsPoint(position)) {
            fragment.hit();
            return true;
          }
        }
      }
    }
    return false;
  }

  /// Get target at position
  GameTarget? getTargetAt(Offset position) {
    // Check main targets first
    for (final target in _targets) {
      if (target.isActive && target.containsPoint(position)) {
        return target;
      }
    }
    
    // Check split targets
    for (final target in _targets) {
      if (target.splitTargets != null) {
        for (final fragment in target.splitTargets!) {
          if (fragment.isActive && fragment.containsPoint(position)) {
            return fragment;
          }
        }
      }
    }
    
    return null;
  }

  /// Get all active targets including fragments
  List<GameTarget> getAllActiveTargets() {
    final allTargets = <GameTarget>[];
    
    for (final target in _targets) {
      if (target.isActive) {
        allTargets.add(target);
      }
      
      if (target.splitTargets != null) {
        allTargets.addAll(target.splitTargets!.where((f) => f.isActive));
      }
    }
    
    return allTargets;
  }

  /// Clear all targets
  void clear() {
    _targets.clear();
  }

  /// Dispose resources
  void dispose() {
    clear();
  }
}
