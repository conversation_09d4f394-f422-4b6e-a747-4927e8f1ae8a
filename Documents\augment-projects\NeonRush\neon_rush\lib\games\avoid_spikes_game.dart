import 'dart:async';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../core/sound_manager.dart';
import '../models/neon_theme.dart';
import '../models/level_config.dart';
import '../game/game_state.dart';
import '../game/power_up_manager.dart';
import '../game/enemy_system.dart';
import '../ui/neon_widgets.dart';
import '../ui/power_up_hud.dart';
import '../ui/enemy_renderer.dart';
import '../ui/dialogs/level_completion_dialog.dart';

/// Avoid Neon Spikes game - Level 4 of new game mechanics
class AvoidSpikesGame extends StatefulWidget {
  final int levelNumber;
  
  const AvoidSpikesGame({
    super.key,
    this.levelNumber = 1,
  });

  @override
  State<AvoidSpikesGame> createState() => _AvoidSpikesGameState();
}

class _AvoidSpikesGameState extends State<AvoidSpikesGame>
    with TickerProviderStateMixin {
  late Timer _gameTimer;
  late AnimationController _backgroundController;
  late Animation<double> _backgroundAnimation;

  late EnemyManager _enemyManager;
  Offset _playerPosition = const Offset(0.5, 0.5); // Center
  int _timeLeft = 60; // 60 seconds to survive
  bool _gameActive = true;
  int _score = 0;
  late PowerUpManager _powerUpManager;
  late LevelConfig _levelConfig;
  
  @override
  void initState() {
    super.initState();
    _initializeLevel();
    _initializeAnimations();
    _initializePowerUps();
    _initializeEnemies();
    _initializeMusic();
    _startGame();
  }

  void _initializeLevel() {
    _levelConfig = LevelConfigs.getLevelConfig(widget.levelNumber) ??
        LevelConfigs.allLevels[3]; // Default to level 4 (avoid mode)
    _timeLeft = _levelConfig.duration.inSeconds;
  }

  void _initializeMusic() async {
    // Play random game theme music
    await SoundManager().playRandomGameMusic();
  }

  void _initializeAnimations() {
    _backgroundController = AnimationController(
      duration: const Duration(seconds: 4),
      vsync: this,
    );

    _backgroundAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(_backgroundController);

    _backgroundController.repeat();
  }

  void _initializePowerUps() {
    _powerUpManager = PowerUpManager();
  }

  void _initializeEnemies() {
    _enemyManager = EnemyManager();

    // Configure enemy spawning based on level
    final spawnPoints = [
      const Offset(0.5, -0.1),  // Top center
      const Offset(1.1, 0.5),   // Right center
      const Offset(0.5, 1.1),   // Bottom center
      const Offset(-0.1, 0.5),  // Left center
    ];

    _enemyManager.addSpawnConfig(EnemySpawnConfig(
      type: EnemyType.spike,
      spawnRate: 0.8 + (widget.levelNumber * 0.1), // Faster spawning for higher levels
      maxEnemies: 8 + widget.levelNumber,
      spawnPoints: spawnPoints,
      color: NeonThemes.fireOrange.primary,
      sizeMin: 25.0,
      sizeMax: 45.0,
      speedMin: 80.0,
      speedMax: 120.0 + (widget.levelNumber * 10),
    ));
  }
  
  void _startGame() {
    // Start enemy system
    _enemyManager.start();

    // Game timer
    _gameTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (!_gameActive) {
        timer.cancel();
        return;
      }

      setState(() {
        _timeLeft--;
        _score++; // Score increases each second survived
        if (_timeLeft <= 0) {
          _endGame();
        }
      });
    });

    // Collision check timer
    Timer.periodic(const Duration(milliseconds: 16), (timer) {
      if (!_gameActive) {
        timer.cancel();
        return;
      }
      _checkCollisions();
    });
  }
  
  void _checkCollisions() {
    final playerRadius = 25.0;
    final collidingEnemies = _enemyManager.getCollidingEnemies(_playerPosition, playerRadius);

    if (collidingEnemies.isNotEmpty) {
      // Collision detected - game over
      _endGame();
    }
  }
  
  void _onPanUpdate(DragUpdateDetails details) {
    if (!_gameActive) return;
    
    final screenSize = MediaQuery.of(context).size;
    final newPosition = Offset(
      (_playerPosition.dx * screenSize.width + details.delta.dx) / screenSize.width,
      (_playerPosition.dy * screenSize.height + details.delta.dy) / screenSize.height,
    );
    
    setState(() {
      _playerPosition = Offset(
        newPosition.dx.clamp(0.1, 0.9),
        newPosition.dy.clamp(0.1, 0.9),
      );
    });
  }
  
  void _endGame() {
    setState(() {
      _gameActive = false;
    });

    _gameTimer.cancel();
    _enemyManager.stop();

    final gameState = context.read<GameStateManager>();
    final isSuccess = _timeLeft <= 0; // Success if survived the full time

    if (isSuccess) {
      // Calculate rewards based on score and survival time
      final timeBonus = _score * 3;
      final totalXP = 180 + timeBonus;
      final tokensEarned = 40 + (_score ~/ 5);

      // Set current level and award rewards
      gameState.startLevel(_levelConfig);
      gameState.completeLevel(
        finalScore: _score,
        completionTime: Duration(seconds: _levelConfig.duration.inSeconds - _timeLeft),
        timeBonus: timeBonus,
      );

      // Show completion dialog
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => LevelCompletionDialog(
          level: _levelConfig,
          score: _score,
          timeBonus: timeBonus,
          totalXP: totalXP,
          tokensEarned: tokensEarned,
          onReplay: () => _restartGame(),
          onNextLevel: _hasNextLevel() ? () => _goToNextLevel() : null,
          onBackToMenu: () => Navigator.of(context).pop(),
        ),
      );
    } else {
      // Show failure dialog
      _showFailureDialog();
    }
  }

  void _restartGame() {
    setState(() {
      _playerPosition = const Offset(0.5, 0.5);
      _timeLeft = _levelConfig.duration.inSeconds;
      _score = 0;
      _gameActive = true;
    });
    _enemyManager.clear();
    _startGame();
  }

  @override
  void dispose() {
    _gameTimer.cancel();
    _enemyManager.stop();
    _backgroundController.dispose();
    super.dispose();
  }

  bool _hasNextLevel() {
    return widget.levelNumber < 10;
  }
  
  void _goToNextLevel() {
    Navigator.of(context).pushReplacement(
      MaterialPageRoute(
        builder: (context) => AvoidSpikesGame(levelNumber: widget.levelNumber + 1),
      ),
    );
  }
  
  void _showFailureDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => Dialog(
        backgroundColor: Colors.transparent,
        child: NeonContainer.panel(
          glowColor: Colors.red,
          child: Padding(
            padding: const EdgeInsets.all(24),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                NeonText.title(
                  'SPIKED!',
                  glowColor: Colors.red,
                  fontSize: 24,
                ),
                const SizedBox(height: 16),
                NeonText.body(
                  'You hit a neon spike!',
                  glowColor: Colors.white,
                  fontSize: 16,
                ),
                const SizedBox(height: 24),
                Row(
                  children: [
                    Expanded(
                      child: NeonButton.secondary(
                        text: 'RETRY',
                        glowColor: NeonThemes.neonPink.primary,
                        onPressed: () {
                          Navigator.of(context).pop();
                          _restartGame();
                        },
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: NeonButton.secondary(
                        text: 'MENU',
                        glowColor: Colors.grey,
                        onPressed: () {
                          Navigator.of(context).pop();
                          Navigator.of(context).pop();
                        },
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
  


  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: AnimatedBuilder(
        animation: _backgroundAnimation,
        builder: (context, child) {
          return Container(
            decoration: BoxDecoration(
              gradient: RadialGradient(
                center: Alignment.center,
                radius: 1.5,
                colors: [
                  Colors.black,
                  NeonThemes.neonPink.primary.withValues(alpha: 0.05 * _backgroundAnimation.value),
                  Colors.black,
                ],
              ),
            ),
            child: SafeArea(
              child: GestureDetector(
                onPanUpdate: _onPanUpdate,
                child: Stack(
                  children: [
                    // Enemies
                    EnemyRenderer(
                      enemies: _enemyManager.enemies,
                      screenSize: MediaQuery.of(context).size,
                    ),

                    // Player
                    _buildPlayer(),

                    // UI overlay
                    _buildUI(),
                    
                    // Power-up HUD
                    Positioned(
                      top: 80,
                      right: 20,
                      child: ChangeNotifierProvider.value(
                        value: _powerUpManager,
                        child: const PowerUpHUD(),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }
  

  
  Widget _buildPlayer() {
    final screenSize = MediaQuery.of(context).size;
    return Positioned(
      left: _playerPosition.dx * screenSize.width - 25,
      top: _playerPosition.dy * screenSize.height - 25,
      child: Container(
        width: 50,
        height: 50,
        decoration: BoxDecoration(
          color: NeonThemes.cyberBlue.primary.withValues(alpha: 0.3),
          shape: BoxShape.circle,
          border: Border.all(color: NeonThemes.cyberBlue.primary, width: 3),
          boxShadow: NeonEffects.createGlow(
            color: NeonThemes.cyberBlue.primary,
            intensity: 1.0,
          ),
        ),
        child: const Icon(
          Icons.person,
          color: Colors.white,
          size: 25,
        ),
      ),
    );
  }
  
  Widget _buildUI() {
    return Column(
      children: [
        // Header
        Padding(
          padding: const EdgeInsets.all(20),
          child: Row(
            children: [
              NeonCircularButton(
                icon: Icons.arrow_back,
                glowColor: NeonThemes.cyberBlue.primary,
                onPressed: () => Navigator.of(context).pop(),
              ),
              const SizedBox(width: 20),
              Expanded(
                child: NeonText.title(
                  'AVOID NEON SPIKES - LEVEL ${widget.levelNumber}',
                  glowColor: NeonThemes.neonPink.primary,
                  fontSize: 16,
                ),
              ),
            ],
          ),
        ),
        
        // Time remaining
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20),
          child: NeonContainer.card(
            glowColor: _timeLeft <= 10 ? Colors.red : NeonThemes.cyberBlue.primary,
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: NeonText.body(
                'Survive: $_timeLeft seconds',
                glowColor: _timeLeft <= 10 ? Colors.red : NeonThemes.cyberBlue.primary,
                fontSize: 16,
              ),
            ),
          ),
        ),
        
        const Spacer(),
        
        // Instructions
        Padding(
          padding: const EdgeInsets.all(20),
          child: NeonText.body(
            'Drag to move • Avoid the deadly spikes!',
            glowColor: Colors.white,
            fontSize: 14,
            textAlign: TextAlign.center,
          ),
        ),
      ],
    );
  }
}

/// Spike data class
class Spike {
  int id;
  Offset position;
  Offset velocity;
  Color color;
  double size;
  double rotation;
  
  Spike({
    required this.id,
    required this.position,
    required this.velocity,
    required this.color,
    required this.size,
    required this.rotation,
  });
}

/// Custom painter for spike shape
class SpikePainter extends CustomPainter {
  final Color color;
  
  SpikePainter({required this.color});
  
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;
    
    final path = Path();
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width / 2;
    
    // Create a star/spike shape
    for (int i = 0; i < 8; i++) {
      final angle = (i * pi / 4);
      final isOuter = i % 2 == 0;
      final currentRadius = isOuter ? radius : radius * 0.5;
      
      final x = center.dx + cos(angle) * currentRadius;
      final y = center.dy + sin(angle) * currentRadius;
      
      if (i == 0) {
        path.moveTo(x, y);
      } else {
        path.lineTo(x, y);
      }
    }
    path.close();
    
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}




