import 'dart:async';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../core/constants.dart';
import '../../ui/neon_text.dart';
import '../../ui/neon_button.dart';
import '../../ui/neon_effects.dart';
import '../../managers/game_state_manager.dart';
import '../../managers/sound_manager.dart';
import '../../utils/neon_haptics.dart';
import '../../models/neon_theme.dart';

/// ColorZap - Tap when a central circle matches the outer ring color
class ColorZapGame extends StatefulWidget {
  final NeonTheme? theme;
  final Function(int score, int tokens)? onGameComplete;

  const ColorZapGame({
    super.key,
    this.theme,
    this.onGameComplete,
  });

  @override
  State<ColorZapGame> createState() => _ColorZapGameState();
}

class _ColorZapGameState extends State<ColorZapGame>
    with TickerProviderStateMixin {
  late Timer _gameTimer;
  late Timer _colorTimer;
  late AnimationController _pulseController;
  late Animation<double> _pulseAnimation;
  
  Color _centerColor = Colors.red;
  Color _ringColor = Colors.blue;
  bool _colorsMatch = false;
  bool _gameActive = false;
  bool _gameStarted = false;
  int _score = 0;
  int _lives = 3;
  int _level = 1;
  double _colorChangeInterval = 2.0; // seconds
  bool _canTap = true;
  
  static const List<Color> gameColors = [
    Color(0xFF00FFFF), // Cyan
    Color(0xFF00FF00), // Green
    Color(0xFFFFFF00), // Yellow
    Color(0xFFFF6B35), // Orange
    Color(0xFFFF00FF), // Magenta
    Color(0xFF8A2BE2), // Blue Violet
    Color(0xFFFF0000), // Red
    Color(0xFF0080FF), // Blue
  ];
  
  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _startNewGame();
  }

  @override
  void dispose() {
    _gameTimer.cancel();
    _colorTimer.cancel();
    _pulseController.dispose();
    super.dispose();
  }

  void _initializeAnimations() {
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );

    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));
  }

  void _startNewGame() {
    setState(() {
      _score = 0;
      _lives = 3;
      _level = 1;
      _gameActive = true;
      _gameStarted = true;
      _colorChangeInterval = 2.0;
      _canTap = true;
    });

    _generateNewColors();
    _startGameTimer();
    _startColorTimer();
  }

  void _generateNewColors() {
    final random = Random();
    
    // 30% chance for colors to match
    final shouldMatch = random.nextDouble() < 0.3;
    
    if (shouldMatch) {
      final color = gameColors[random.nextInt(gameColors.length)];
      setState(() {
        _centerColor = color;
        _ringColor = color;
        _colorsMatch = true;
      });
    } else {
      Color center, ring;
      do {
        center = gameColors[random.nextInt(gameColors.length)];
        ring = gameColors[random.nextInt(gameColors.length)];
      } while (center == ring);
      
      setState(() {
        _centerColor = center;
        _ringColor = ring;
        _colorsMatch = false;
      });
    }
    
    setState(() {
      _canTap = true;
    });
  }

  void _startGameTimer() {
    _gameTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (!_gameActive) {
        timer.cancel();
        return;
      }
      
      // Check if game should end
      if (_lives <= 0) {
        _endGame();
      }
    });
  }

  void _startColorTimer() {
    _colorTimer = Timer.periodic(Duration(milliseconds: (_colorChangeInterval * 1000).round()), (timer) {
      if (!_gameActive) {
        timer.cancel();
        return;
      }
      
      // If player didn't tap and colors matched, lose a life
      if (_colorsMatch && _canTap) {
        _missedMatch();
      }
      
      _generateNewColors();
      
      // Increase difficulty over time
      if (_score > 0 && _score % 10 == 0) {
        _colorChangeInterval = max(0.8, _colorChangeInterval - 0.1);
        _level = (_score ~/ 10) + 1;
        timer.cancel();
        _startColorTimer();
      }
    });
  }

  void _onTap() async {
    if (!_gameActive || !_canTap) return;
    
    setState(() {
      _canTap = false;
    });
    
    if (_colorsMatch) {
      // Correct tap!
      await _correctTap();
    } else {
      // Wrong tap!
      await _wrongTap();
    }
  }

  Future<void> _correctTap() async {
    await SoundManager().playSfx(SoundType.buttonClick);
    await NeonHaptics.targetHit();
    
    setState(() {
      _score += 10;
    });
    
    // Pulse animation for correct tap
    _pulseController.forward().then((_) {
      _pulseController.reverse();
    });
    
    _generateNewColors();
  }

  Future<void> _wrongTap() async {
    await SoundManager().playSfx(SoundType.gameOver);
    await NeonHaptics.gameOver();
    
    setState(() {
      _lives--;
    });
    
    _generateNewColors();
  }

  Future<void> _missedMatch() async {
    await NeonHaptics.gameOver();
    
    setState(() {
      _lives--;
    });
  }

  void _endGame() async {
    setState(() {
      _gameActive = false;
    });

    await NeonHaptics.gameOver();

    final gameState = context.read<GameStateManager>();
    gameState.addTokens(_score ~/ 5); // 1 token per 5 points

    _showGameOverDialog();
  }

  void _showGameOverDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.black.withValues(alpha: 0.9),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
          side: const BorderSide(color: Color(0xFF00FFFF), width: 2),
        ),
        title: NeonText.heading(
          'Game Over!',
          glowColor: const Color(0xFF00FFFF),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            NeonText.body(
              'Score: $_score',
              glowColor: Colors.white,
            ),
            const SizedBox(height: 8),
            NeonText.body(
              'Level Reached: $_level',
              glowColor: const Color(0xFFFFD700),
            ),
            const SizedBox(height: 8),
            NeonText.body(
              'Tokens Earned: ${_score ~/ 5}',
              glowColor: const Color(0xFFFFD700),
            ),
          ],
        ),
        actions: [
          NeonButton(
            text: 'Play Again',
            onPressed: () {
              Navigator.of(context).pop();
              _startNewGame();
            },
            glowColor: const Color(0xFF00FF00),
          ),
          NeonButton(
            text: 'Back to Menu',
            onPressed: () {
              Navigator.of(context).pop();
              Navigator.of(context).pop();
            },
            glowColor: const Color(0xFFFF6B35),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: SafeArea(
        child: Stack(
          children: [
            // Background
            Container(
              decoration: const BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Color(0xFF0A0A0A),
                    Color(0xFF1A1A2E),
                    Color(0xFF16213E),
                  ],
                ),
              ),
            ),
            
            // Game area
            Positioned.fill(
              child: Center(
                child: GestureDetector(
                  onTap: _onTap,
                  child: AnimatedBuilder(
                    animation: _pulseAnimation,
                    builder: (context, child) {
                      return Transform.scale(
                        scale: _pulseAnimation.value,
                        child: CustomPaint(
                          size: const Size(300, 300),
                          painter: ColorZapPainter(
                            centerColor: _centerColor,
                            ringColor: _ringColor,
                            colorsMatch: _colorsMatch,
                            canTap: _canTap,
                          ),
                        ),
                      );
                    },
                  ),
                ),
              ),
            ),
            
            // UI Overlay
            Positioned(
              top: 20,
              left: 20,
              right: 20,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  NeonButton(
                    text: 'Back',
                    onPressed: () => Navigator.of(context).pop(),
                    glowColor: const Color(0xFFFF6B35),
                    width: 80,
                    height: 40,
                  ),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      NeonText.body(
                        'Score: $_score',
                        glowColor: const Color(0xFF00FFFF),
                        fontSize: 18,
                      ),
                      NeonText.body(
                        'Lives: $_lives',
                        glowColor: const Color(0xFFFF0000),
                        fontSize: 14,
                      ),
                      NeonText.body(
                        'Level: $_level',
                        glowColor: const Color(0xFFFFD700),
                        fontSize: 14,
                      ),
                    ],
                  ),
                ],
              ),
            ),
            
            // Instructions
            if (_gameStarted && _gameActive)
              Positioned(
                bottom: 100,
                left: 20,
                right: 20,
                child: Center(
                  child: Column(
                    children: [
                      NeonText.body(
                        'TAP WHEN COLORS MATCH',
                        glowColor: Colors.white,
                        fontSize: 18,
                      ),
                      const SizedBox(height: 16),
                      NeonText.body(
                        _colorsMatch ? 'COLORS MATCH - TAP NOW!' : 'COLORS DON\'T MATCH - WAIT',
                        glowColor: _colorsMatch ? const Color(0xFF00FF00) : const Color(0xFFFF0000),
                        fontSize: 16,
                      ),
                    ],
                  ),
                ),
              ),
            
            // Speed indicator
            Positioned(
              bottom: 20,
              left: 20,
              right: 20,
              child: Center(
                child: NeonText.body(
                  'Speed: ${(3.0 - _colorChangeInterval).toStringAsFixed(1)}x',
                  glowColor: const Color(0xFFFFD700),
                  fontSize: 14,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// Custom painter for the color zap game
class ColorZapPainter extends CustomPainter {
  final Color centerColor;
  final Color ringColor;
  final bool colorsMatch;
  final bool canTap;

  ColorZapPainter({
    required this.centerColor,
    required this.ringColor,
    required this.colorsMatch,
    required this.canTap,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final centerRadius = 60.0;
    final ringInnerRadius = 80.0;
    final ringOuterRadius = 120.0;
    
    // Draw outer ring
    _drawRing(canvas, center, ringInnerRadius, ringOuterRadius, ringColor);
    
    // Draw center circle
    _drawCenter(canvas, center, centerRadius, centerColor);
    
    // Draw match indicator
    if (colorsMatch && canTap) {
      _drawMatchIndicator(canvas, center, ringOuterRadius + 20);
    }
  }

  void _drawRing(Canvas canvas, Offset center, double innerRadius, double outerRadius, Color color) {
    // Draw glow effect
    final glowPaint = Paint()
      ..color = color.withValues(alpha: 0.3)
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 12);
    
    canvas.drawCircle(center, outerRadius + 8, glowPaint);
    
    // Draw main ring
    final ringPaint = Paint()
      ..color = color.withValues(alpha: 0.8)
      ..style = PaintingStyle.fill;
    
    final path = Path();
    path.addOval(Rect.fromCircle(center: center, radius: outerRadius));
    path.addOval(Rect.fromCircle(center: center, radius: innerRadius));
    path.fillType = PathFillType.evenOdd;
    
    canvas.drawPath(path, ringPaint);
    
    // Draw borders
    final borderPaint = Paint()
      ..color = color
      ..style = PaintingStyle.stroke
      ..strokeWidth = 3;
    
    canvas.drawCircle(center, outerRadius, borderPaint);
    canvas.drawCircle(center, innerRadius, borderPaint);
  }

  void _drawCenter(Canvas canvas, Offset center, double radius, Color color) {
    // Draw glow effect
    final glowPaint = Paint()
      ..color = color.withValues(alpha: 0.3)
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 12);
    
    canvas.drawCircle(center, radius + 8, glowPaint);
    
    // Draw main circle
    final centerPaint = Paint()
      ..color = color.withValues(alpha: 0.9)
      ..style = PaintingStyle.fill;
    
    canvas.drawCircle(center, radius, centerPaint);
    
    // Draw border
    final borderPaint = Paint()
      ..color = color
      ..style = PaintingStyle.stroke
      ..strokeWidth = 3;
    
    canvas.drawCircle(center, radius, borderPaint);
  }

  void _drawMatchIndicator(Canvas canvas, Offset center, double radius) {
    final indicatorPaint = Paint()
      ..color = const Color(0xFF00FF00).withValues(alpha: 0.6)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 6
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 8);
    
    // Draw pulsing ring
    canvas.drawCircle(center, radius, indicatorPaint);
    
    // Draw arrows pointing inward
    final arrowPaint = Paint()
      ..color = const Color(0xFF00FF00)
      ..style = PaintingStyle.fill;
    
    for (int i = 0; i < 8; i++) {
      final angle = (i * pi / 4);
      final arrowCenter = Offset(
        center.dx + cos(angle) * (radius + 15),
        center.dy + sin(angle) * (radius + 15),
      );
      
      _drawArrow(canvas, arrowCenter, angle + pi, arrowPaint);
    }
  }

  void _drawArrow(Canvas canvas, Offset center, double angle, Paint paint) {
    final path = Path();
    const arrowSize = 8.0;
    
    // Arrow pointing in the direction of angle
    final tip = Offset(
      center.dx + cos(angle) * arrowSize,
      center.dy + sin(angle) * arrowSize,
    );
    final left = Offset(
      center.dx + cos(angle + 2.5) * arrowSize * 0.6,
      center.dy + sin(angle + 2.5) * arrowSize * 0.6,
    );
    final right = Offset(
      center.dx + cos(angle - 2.5) * arrowSize * 0.6,
      center.dy + sin(angle - 2.5) * arrowSize * 0.6,
    );
    
    path.moveTo(tip.dx, tip.dy);
    path.lineTo(left.dx, left.dy);
    path.lineTo(right.dx, right.dy);
    path.close();
    
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
