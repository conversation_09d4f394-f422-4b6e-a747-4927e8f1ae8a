import 'dart:async';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../core/constants.dart';
import '../../ui/neon_text.dart';
import '../../ui/neon_button.dart';
import '../../ui/neon_effects.dart';
import '../../managers/game_state_manager.dart';
import '../../managers/sound_manager.dart';
import '../../utils/neon_haptics.dart';
import '../../models/neon_theme.dart';

/// Drag Maze - Drag a glowing orb through a maze without hitting walls
class DragMazeGame extends StatefulWidget {
  final NeonTheme? theme;
  final Function(int score, int tokens)? onGameComplete;

  const DragMazeGame({
    super.key,
    this.theme,
    this.onGameComplete,
  });

  @override
  State<DragMazeGame> createState() => _DragMazeGameState();
}

class _DragMazeGameState extends State<DragMazeGame>
    with TickerProviderStateMixin {
  late Timer _gameTimer;
  
  List<List<MazeCell>> _maze = [];
  Offset _orbPosition = const Offset(50, 50);
  Offset _startPosition = const Offset(50, 50);
  Offset _endPosition = const Offset(350, 350);
  bool _isDragging = false;
  bool _gameActive = false;
  bool _gameStarted = false;
  int _score = 0;
  int _level = 1;
  int _timeLeft = 60;
  List<Offset> _trail = [];
  
  static const int mazeSize = 15;
  static const double cellSize = 25.0;
  static const double orbRadius = 12.0;
  
  @override
  void initState() {
    super.initState();
    _startNewGame();
  }

  @override
  void dispose() {
    _gameTimer.cancel();
    super.dispose();
  }

  void _startNewGame() {
    setState(() {
      _score = 0;
      _level = 1;
      _timeLeft = 60;
      _gameActive = true;
      _gameStarted = true;
      _isDragging = false;
      _trail.clear();
    });

    _generateMaze();
    _startGameTimer();
  }

  void _generateMaze() {
    // Initialize maze with all walls
    _maze = List.generate(mazeSize, (row) =>
        List.generate(mazeSize, (col) => MazeCell(
          row: row,
          col: col,
          isWall: true,
          isPath: false,
        )));
    
    // Generate maze using recursive backtracking
    _carveMaze(1, 1);
    
    // Ensure start and end positions are clear
    _maze[1][1] = _maze[1][1].copyWith(isWall: false, isPath: true);
    _maze[mazeSize - 2][mazeSize - 2] = _maze[mazeSize - 2][mazeSize - 2].copyWith(isWall: false, isPath: true);
    
    // Set orb position to start
    setState(() {
      _orbPosition = Offset(1 * cellSize + cellSize / 2, 1 * cellSize + cellSize / 2);
      _startPosition = _orbPosition;
      _endPosition = Offset((mazeSize - 2) * cellSize + cellSize / 2, (mazeSize - 2) * cellSize + cellSize / 2);
    });
  }

  void _carveMaze(int row, int col) {
    _maze[row][col] = _maze[row][col].copyWith(isWall: false, isPath: true);
    
    final directions = [
      [0, 2], [2, 0], [0, -2], [-2, 0]
    ];
    directions.shuffle();
    
    for (final dir in directions) {
      final newRow = row + dir[0];
      final newCol = col + dir[1];
      
      if (newRow > 0 && newRow < mazeSize - 1 &&
          newCol > 0 && newCol < mazeSize - 1 &&
          _maze[newRow][newCol].isWall) {
        
        // Carve the wall between current cell and new cell
        final wallRow = row + dir[0] ~/ 2;
        final wallCol = col + dir[1] ~/ 2;
        _maze[wallRow][wallCol] = _maze[wallRow][wallCol].copyWith(isWall: false, isPath: true);
        
        _carveMaze(newRow, newCol);
      }
    }
  }

  void _startGameTimer() {
    _gameTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (!_gameActive) {
        timer.cancel();
        return;
      }
      
      setState(() {
        _timeLeft--;
      });
      
      if (_timeLeft <= 0) {
        _endGame();
      }
    });
  }

  void _onPanStart(DragStartDetails details) {
    if (!_gameActive) return;
    
    final localPosition = details.localPosition;
    final distance = (localPosition - _orbPosition).distance;
    
    if (distance <= orbRadius * 2) {
      setState(() {
        _isDragging = true;
        _trail.clear();
        _trail.add(_orbPosition);
      });
    }
  }

  void _onPanUpdate(DragUpdateDetails details) async {
    if (!_gameActive || !_isDragging) return;
    
    final newPosition = details.localPosition;
    
    // Check if new position is valid (not in wall)
    if (_isValidPosition(newPosition)) {
      setState(() {
        _orbPosition = newPosition;
        _trail.add(newPosition);
        
        // Limit trail length
        if (_trail.length > 20) {
          _trail.removeAt(0);
        }
      });
      
      // Check if reached end
      final distanceToEnd = (newPosition - _endPosition).distance;
      if (distanceToEnd <= orbRadius) {
        await _completeLevel();
      }
    } else {
      // Hit wall - game over
      await _hitWall();
    }
  }

  void _onPanEnd(DragEndDetails details) {
    setState(() {
      _isDragging = false;
    });
  }

  bool _isValidPosition(Offset position) {
    final col = (position.dx / cellSize).floor();
    final row = (position.dy / cellSize).floor();
    
    if (row < 0 || row >= mazeSize || col < 0 || col >= mazeSize) {
      return false;
    }
    
    return !_maze[row][col].isWall;
  }

  Future<void> _completeLevel() async {
    setState(() {
      _gameActive = false;
    });

    await NeonHaptics.levelComplete();
    await SoundManager().playSfx(SoundType.buttonClick);

    final timeBonus = _timeLeft * 10;
    setState(() {
      _score += 100 + timeBonus;
      _level++;
    });

    _showLevelCompleteDialog();
  }

  Future<void> _hitWall() async {
    setState(() {
      _gameActive = false;
    });

    await NeonHaptics.gameOver();

    _endGame();
  }

  void _endGame() async {
    setState(() {
      _gameActive = false;
    });

    final gameState = context.read<GameStateManager>();
    gameState.addTokens(_score ~/ 10); // 1 token per 10 points

    _showGameOverDialog();
  }

  void _showLevelCompleteDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.black.withValues(alpha: 0.9),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
          side: const BorderSide(color: Color(0xFF00FF00), width: 2),
        ),
        title: NeonText.heading(
          'Level Complete!',
          glowColor: const Color(0xFF00FF00),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            NeonText.body(
              'Score: $_score',
              glowColor: Colors.white,
            ),
            const SizedBox(height: 8),
            NeonText.body(
              'Time Bonus: ${_timeLeft * 10}',
              glowColor: const Color(0xFFFFD700),
            ),
            const SizedBox(height: 8),
            NeonText.body(
              'Level: $_level',
              glowColor: const Color(0xFF00FFFF),
            ),
          ],
        ),
        actions: [
          NeonButton(
            text: 'Next Level',
            onPressed: () {
              Navigator.of(context).pop();
              _startNextLevel();
            },
            glowColor: const Color(0xFF00FF00),
          ),
          NeonButton(
            text: 'Back to Menu',
            onPressed: () {
              Navigator.of(context).pop();
              Navigator.of(context).pop();
            },
            glowColor: const Color(0xFFFF6B35),
          ),
        ],
      ),
    );
  }

  void _showGameOverDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.black.withValues(alpha: 0.9),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
          side: const BorderSide(color: Color(0xFF00FFFF), width: 2),
        ),
        title: NeonText.heading(
          'Game Over!',
          glowColor: const Color(0xFF00FFFF),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            NeonText.body(
              'Score: $_score',
              glowColor: Colors.white,
            ),
            const SizedBox(height: 8),
            NeonText.body(
              'Level Reached: $_level',
              glowColor: const Color(0xFFFFD700),
            ),
            const SizedBox(height: 8),
            NeonText.body(
              'Tokens Earned: ${_score ~/ 10}',
              glowColor: const Color(0xFFFFD700),
            ),
          ],
        ),
        actions: [
          NeonButton(
            text: 'Play Again',
            onPressed: () {
              Navigator.of(context).pop();
              _startNewGame();
            },
            glowColor: const Color(0xFF00FF00),
          ),
          NeonButton(
            text: 'Back to Menu',
            onPressed: () {
              Navigator.of(context).pop();
              Navigator.of(context).pop();
            },
            glowColor: const Color(0xFFFF6B35),
          ),
        ],
      ),
    );
  }

  void _startNextLevel() {
    setState(() {
      _timeLeft = max(30, 60 - (_level * 5)); // Decrease time each level
      _gameActive = true;
      _isDragging = false;
      _trail.clear();
    });

    _generateMaze();
    _startGameTimer();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: SafeArea(
        child: Stack(
          children: [
            // Background
            Container(
              decoration: const BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Color(0xFF0A0A0A),
                    Color(0xFF1A1A2E),
                    Color(0xFF16213E),
                  ],
                ),
              ),
            ),
            
            // Game area
            Positioned.fill(
              top: 80,
              child: Center(
                child: GestureDetector(
                  onPanStart: _onPanStart,
                  onPanUpdate: _onPanUpdate,
                  onPanEnd: _onPanEnd,
                  child: Container(
                    width: mazeSize * cellSize,
                    height: mazeSize * cellSize,
                    child: CustomPaint(
                      painter: DragMazePainter(
                        maze: _maze,
                        orbPosition: _orbPosition,
                        startPosition: _startPosition,
                        endPosition: _endPosition,
                        trail: _trail,
                        isDragging: _isDragging,
                        cellSize: cellSize,
                        orbRadius: orbRadius,
                      ),
                    ),
                  ),
                ),
              ),
            ),
            
            // UI Overlay
            Positioned(
              top: 20,
              left: 20,
              right: 20,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  NeonButton(
                    text: 'Back',
                    onPressed: () => Navigator.of(context).pop(),
                    glowColor: const Color(0xFFFF6B35),
                    width: 80,
                    height: 40,
                  ),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      NeonText.body(
                        'Score: $_score',
                        glowColor: const Color(0xFF00FFFF),
                        fontSize: 18,
                      ),
                      NeonText.body(
                        'Time: $_timeLeft',
                        glowColor: _timeLeft <= 10 ? const Color(0xFFFF0000) : const Color(0xFFFFD700),
                        fontSize: 14,
                      ),
                      NeonText.body(
                        'Level: $_level',
                        glowColor: const Color(0xFF00FF00),
                        fontSize: 14,
                      ),
                    ],
                  ),
                ],
              ),
            ),
            
            // Instructions
            if (_gameStarted && _gameActive)
              Positioned(
                bottom: 20,
                left: 20,
                right: 20,
                child: Center(
                  child: Column(
                    children: [
                      NeonText.body(
                        'DRAG THE ORB TO THE EXIT',
                        glowColor: Colors.white,
                        fontSize: 16,
                      ),
                      const SizedBox(height: 8),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Container(
                            width: 20,
                            height: 20,
                            decoration: const BoxDecoration(
                              color: Color(0xFF00FF00),
                              shape: BoxShape.circle,
                            ),
                          ),
                          const SizedBox(width: 8),
                          NeonText.body(
                            'Start  ',
                            glowColor: const Color(0xFF00FF00),
                            fontSize: 12,
                          ),
                          Container(
                            width: 20,
                            height: 20,
                            decoration: const BoxDecoration(
                              color: Color(0xFFFFD700),
                              shape: BoxShape.circle,
                            ),
                          ),
                          const SizedBox(width: 8),
                          NeonText.body(
                            'Exit',
                            glowColor: const Color(0xFFFFD700),
                            fontSize: 12,
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}

/// Represents a cell in the maze
class MazeCell {
  final int row;
  final int col;
  final bool isWall;
  final bool isPath;

  const MazeCell({
    required this.row,
    required this.col,
    required this.isWall,
    required this.isPath,
  });

  MazeCell copyWith({
    int? row,
    int? col,
    bool? isWall,
    bool? isPath,
  }) {
    return MazeCell(
      row: row ?? this.row,
      col: col ?? this.col,
      isWall: isWall ?? this.isWall,
      isPath: isPath ?? this.isPath,
    );
  }
}

/// Custom painter for the drag maze game
class DragMazePainter extends CustomPainter {
  final List<List<MazeCell>> maze;
  final Offset orbPosition;
  final Offset startPosition;
  final Offset endPosition;
  final List<Offset> trail;
  final bool isDragging;
  final double cellSize;
  final double orbRadius;

  DragMazePainter({
    required this.maze,
    required this.orbPosition,
    required this.startPosition,
    required this.endPosition,
    required this.trail,
    required this.isDragging,
    required this.cellSize,
    required this.orbRadius,
  });

  @override
  void paint(Canvas canvas, Size size) {
    // Draw maze
    _drawMaze(canvas);
    
    // Draw trail
    _drawTrail(canvas);
    
    // Draw start and end positions
    _drawStartEnd(canvas);
    
    // Draw orb
    _drawOrb(canvas);
  }

  void _drawMaze(Canvas canvas) {
    final wallPaint = Paint()
      ..color = const Color(0xFF00FFFF).withValues(alpha: 0.8)
      ..style = PaintingStyle.fill;
    
    final wallGlowPaint = Paint()
      ..color = const Color(0xFF00FFFF).withValues(alpha: 0.3)
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 4);
    
    for (int row = 0; row < maze.length; row++) {
      for (int col = 0; col < maze[row].length; col++) {
        if (maze[row][col].isWall) {
          final rect = Rect.fromLTWH(
            col * cellSize,
            row * cellSize,
            cellSize,
            cellSize,
          );
          
          canvas.drawRect(rect, wallGlowPaint);
          canvas.drawRect(rect, wallPaint);
        }
      }
    }
  }

  void _drawTrail(Canvas canvas) {
    if (trail.length < 2) return;
    
    final trailPaint = Paint()
      ..color = const Color(0xFF00FF00).withValues(alpha: 0.5)
      ..strokeWidth = 4
      ..strokeCap = StrokeCap.round
      ..style = PaintingStyle.stroke;
    
    final path = Path();
    path.moveTo(trail.first.dx, trail.first.dy);
    
    for (int i = 1; i < trail.length; i++) {
      path.lineTo(trail[i].dx, trail[i].dy);
    }
    
    canvas.drawPath(path, trailPaint);
  }

  void _drawStartEnd(Canvas canvas) {
    // Draw start position
    final startPaint = Paint()
      ..color = const Color(0xFF00FF00).withValues(alpha: 0.8)
      ..style = PaintingStyle.fill;
    
    final startGlowPaint = Paint()
      ..color = const Color(0xFF00FF00).withValues(alpha: 0.3)
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 8);
    
    canvas.drawCircle(startPosition, 15, startGlowPaint);
    canvas.drawCircle(startPosition, 10, startPaint);
    
    // Draw end position
    final endPaint = Paint()
      ..color = const Color(0xFFFFD700).withValues(alpha: 0.8)
      ..style = PaintingStyle.fill;
    
    final endGlowPaint = Paint()
      ..color = const Color(0xFFFFD700).withValues(alpha: 0.3)
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 8);
    
    canvas.drawCircle(endPosition, 15, endGlowPaint);
    canvas.drawCircle(endPosition, 10, endPaint);
  }

  void _drawOrb(Canvas canvas) {
    final orbPaint = Paint()
      ..color = const Color(0xFFFF00FF).withValues(alpha: 0.9)
      ..style = PaintingStyle.fill;
    
    final orbGlowPaint = Paint()
      ..color = const Color(0xFFFF00FF).withValues(alpha: isDragging ? 0.5 : 0.3)
      ..maskFilter = MaskFilter.blur(BlurStyle.normal, isDragging ? 12 : 8);
    
    canvas.drawCircle(orbPosition, orbRadius + 4, orbGlowPaint);
    canvas.drawCircle(orbPosition, orbRadius, orbPaint);
    
    // Draw border
    final borderPaint = Paint()
      ..color = const Color(0xFFFF00FF)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2;
    
    canvas.drawCircle(orbPosition, orbRadius, borderPaint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
