import 'dart:async';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../core/constants.dart';
import '../../ui/neon_text.dart';
import '../../ui/neon_button.dart';
import '../../ui/neon_effects.dart';
import '../../managers/game_state_manager.dart';
import '../../managers/sound_manager.dart';
import '../../utils/neon_haptics.dart';
import '../../models/neon_theme.dart';

/// SlideMatch - Match-3 game with drag to swap tiles
class SlideMatchGame extends StatefulWidget {
  final NeonTheme? theme;
  final Function(int score, int tokens)? onGameComplete;

  const SlideMatchGame({
    super.key,
    this.theme,
    this.onGameComplete,
  });

  @override
  State<SlideMatchGame> createState() => _SlideMatchGameState();
}

class _SlideMatchGameState extends State<SlideMatchGame>
    with TickerProviderStateMixin {
  late Timer _gameTimer;
  
  List<List<MatchTile>> _grid = [];
  List<AnimationController> _animationControllers = [];
  int _score = 0;
  int _moves = 30;
  int _combo = 0;
  bool _gameActive = false;
  bool _gameStarted = false;
  int _level = 1;
  int _target = 1000;
  MatchTile? _selectedTile;
  
  static const int gridSize = 6;
  static const List<Color> tileColors = [
    Color(0xFF00FFFF), // Cyan
    Color(0xFF00FF00), // Green
    Color(0xFFFFFF00), // Yellow
    Color(0xFFFF6B35), // Orange
    Color(0xFFFF00FF), // Magenta
    Color(0xFF8A2BE2), // Blue Violet
  ];
  
  @override
  void initState() {
    super.initState();
    _startNewGame();
  }

  @override
  void dispose() {
    _gameTimer.cancel();
    for (final controller in _animationControllers) {
      controller.dispose();
    }
    super.dispose();
  }

  void _startNewGame() {
    setState(() {
      _score = 0;
      _moves = 30;
      _combo = 0;
      _gameActive = true;
      _gameStarted = true;
      _level = 1;
      _target = 1000;
      _selectedTile = null;
    });

    _initializeGrid();
    _startGameTimer();
  }

  void _initializeGrid() {
    _grid = List.generate(gridSize, (row) =>
        List.generate(gridSize, (col) => _createRandomTile(row, col)));
    
    // Ensure no initial matches
    _removeInitialMatches();
  }

  MatchTile _createRandomTile(int row, int col) {
    final random = Random();
    return MatchTile(
      row: row,
      col: col,
      color: tileColors[random.nextInt(tileColors.length)],
      type: random.nextInt(tileColors.length),
    );
  }

  void _removeInitialMatches() {
    bool hasMatches = true;
    while (hasMatches) {
      hasMatches = false;
      for (int row = 0; row < gridSize; row++) {
        for (int col = 0; col < gridSize; col++) {
          if (_hasMatchAt(row, col)) {
            _grid[row][col] = _createRandomTile(row, col);
            hasMatches = true;
          }
        }
      }
    }
  }

  bool _hasMatchAt(int row, int col) {
    final tile = _grid[row][col];
    
    // Check horizontal match
    int horizontalCount = 1;
    // Check left
    for (int c = col - 1; c >= 0 && _grid[row][c].type == tile.type; c--) {
      horizontalCount++;
    }
    // Check right
    for (int c = col + 1; c < gridSize && _grid[row][c].type == tile.type; c++) {
      horizontalCount++;
    }
    
    // Check vertical match
    int verticalCount = 1;
    // Check up
    for (int r = row - 1; r >= 0 && _grid[r][col].type == tile.type; r--) {
      verticalCount++;
    }
    // Check down
    for (int r = row + 1; r < gridSize && _grid[r][col].type == tile.type; r++) {
      verticalCount++;
    }
    
    return horizontalCount >= 3 || verticalCount >= 3;
  }

  void _startGameTimer() {
    _gameTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (!_gameActive) {
        timer.cancel();
        return;
      }
      
      // Check win/lose conditions
      if (_score >= _target) {
        _winLevel();
      } else if (_moves <= 0) {
        _endGame();
      }
    });
  }

  void _onTileTap(MatchTile tile) async {
    if (!_gameActive) return;
    
    if (_selectedTile == null) {
      // Select tile
      setState(() {
        _selectedTile = tile;
      });
      await SoundManager().playSfx(SoundType.buttonClick);
    } else if (_selectedTile == tile) {
      // Deselect tile
      setState(() {
        _selectedTile = null;
      });
    } else if (_areAdjacent(_selectedTile!, tile)) {
      // Swap tiles
      await _swapTiles(_selectedTile!, tile);
    } else {
      // Select new tile
      setState(() {
        _selectedTile = tile;
      });
      await SoundManager().playSfx(SoundType.buttonClick);
    }
  }

  bool _areAdjacent(MatchTile tile1, MatchTile tile2) {
    final rowDiff = (tile1.row - tile2.row).abs();
    final colDiff = (tile1.col - tile2.col).abs();
    return (rowDiff == 1 && colDiff == 0) || (rowDiff == 0 && colDiff == 1);
  }

  Future<void> _swapTiles(MatchTile tile1, MatchTile tile2) async {
    // Swap tiles in grid
    final temp = _grid[tile1.row][tile1.col];
    _grid[tile1.row][tile1.col] = _grid[tile2.row][tile2.col].copyWith(
      row: tile1.row,
      col: tile1.col,
    );
    _grid[tile2.row][tile2.col] = temp.copyWith(
      row: tile2.row,
      col: tile2.col,
    );
    
    setState(() {
      _selectedTile = null;
    });
    
    // Check for matches
    final matches = _findMatches();
    if (matches.isNotEmpty) {
      await SoundManager().playSfx(SoundType.buttonClick);
      await NeonHaptics.targetHit();
      
      setState(() {
        _moves--;
      });
      
      await _processMatches(matches);
    } else {
      // No matches, swap back
      final temp = _grid[tile1.row][tile1.col];
      _grid[tile1.row][tile1.col] = _grid[tile2.row][tile2.col].copyWith(
        row: tile1.row,
        col: tile1.col,
      );
      _grid[tile2.row][tile2.col] = temp.copyWith(
        row: tile2.row,
        col: tile2.col,
      );
      
      await NeonHaptics.gameOver();
    }
  }

  List<MatchTile> _findMatches() {
    final matches = <MatchTile>[];
    final visited = List.generate(gridSize, (_) => List.filled(gridSize, false));
    
    for (int row = 0; row < gridSize; row++) {
      for (int col = 0; col < gridSize; col++) {
        if (!visited[row][col]) {
          final match = _findMatchGroup(row, col, visited);
          if (match.length >= 3) {
            matches.addAll(match);
          }
        }
      }
    }
    
    return matches;
  }

  List<MatchTile> _findMatchGroup(int row, int col, List<List<bool>> visited) {
    final tile = _grid[row][col];
    final group = <MatchTile>[];
    
    // Check horizontal
    final horizontalGroup = <MatchTile>[tile];
    // Check left
    for (int c = col - 1; c >= 0 && _grid[row][c].type == tile.type; c--) {
      horizontalGroup.add(_grid[row][c]);
    }
    // Check right
    for (int c = col + 1; c < gridSize && _grid[row][c].type == tile.type; c++) {
      horizontalGroup.add(_grid[row][c]);
    }
    
    if (horizontalGroup.length >= 3) {
      group.addAll(horizontalGroup);
      for (final t in horizontalGroup) {
        visited[t.row][t.col] = true;
      }
    }
    
    // Check vertical
    final verticalGroup = <MatchTile>[tile];
    // Check up
    for (int r = row - 1; r >= 0 && _grid[r][col].type == tile.type; r--) {
      verticalGroup.add(_grid[r][col]);
    }
    // Check down
    for (int r = row + 1; r < gridSize && _grid[r][col].type == tile.type; r++) {
      verticalGroup.add(_grid[r][col]);
    }
    
    if (verticalGroup.length >= 3) {
      group.addAll(verticalGroup);
      for (final t in verticalGroup) {
        visited[t.row][t.col] = true;
      }
    }
    
    return group.toSet().toList(); // Remove duplicates
  }

  Future<void> _processMatches(List<MatchTile> matches) async {
    // Calculate score
    final baseScore = matches.length * 10;
    final comboBonus = _combo * 5;
    final totalScore = baseScore + comboBonus;
    
    setState(() {
      _score += totalScore;
      _combo++;
    });
    
    // Remove matched tiles
    for (final match in matches) {
      _grid[match.row][match.col] = _createEmptyTile(match.row, match.col);
    }
    
    // Drop tiles down
    await _dropTiles();
    
    // Fill empty spaces
    _fillEmptySpaces();
    
    // Check for new matches
    final newMatches = _findMatches();
    if (newMatches.isNotEmpty) {
      await _processMatches(newMatches);
    } else {
      setState(() {
        _combo = 0;
      });
    }
  }

  MatchTile _createEmptyTile(int row, int col) {
    return MatchTile(
      row: row,
      col: col,
      color: Colors.transparent,
      type: -1,
    );
  }

  Future<void> _dropTiles() async {
    for (int col = 0; col < gridSize; col++) {
      final column = <MatchTile>[];
      
      // Collect non-empty tiles
      for (int row = gridSize - 1; row >= 0; row--) {
        if (_grid[row][col].type != -1) {
          column.add(_grid[row][col]);
        }
      }
      
      // Fill column from bottom
      for (int row = gridSize - 1; row >= 0; row--) {
        if (row >= gridSize - column.length) {
          final tileIndex = row - (gridSize - column.length);
          _grid[row][col] = column[tileIndex].copyWith(row: row, col: col);
        } else {
          _grid[row][col] = _createEmptyTile(row, col);
        }
      }
    }
  }

  void _fillEmptySpaces() {
    for (int row = 0; row < gridSize; row++) {
      for (int col = 0; col < gridSize; col++) {
        if (_grid[row][col].type == -1) {
          _grid[row][col] = _createRandomTile(row, col);
        }
      }
    }
  }

  void _winLevel() async {
    setState(() {
      _gameActive = false;
      _level++;
    });

    await NeonHaptics.levelComplete();

    final gameState = context.read<GameStateManager>();
    gameState.addTokens(_score ~/ 10);

    _showWinDialog();
  }

  void _endGame() async {
    setState(() {
      _gameActive = false;
    });

    await NeonHaptics.gameOver();

    final gameState = context.read<GameStateManager>();
    gameState.addTokens(_score ~/ 20);

    _showGameOverDialog();
  }

  void _showWinDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.black.withValues(alpha: 0.9),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
          side: const BorderSide(color: Color(0xFF00FF00), width: 2),
        ),
        title: NeonText.heading(
          'Level Complete!',
          glowColor: const Color(0xFF00FF00),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            NeonText.body(
              'Score: $_score',
              glowColor: Colors.white,
            ),
            const SizedBox(height: 8),
            NeonText.body(
              'Level: $_level',
              glowColor: const Color(0xFFFFD700),
            ),
            const SizedBox(height: 8),
            NeonText.body(
              'Tokens Earned: ${_score ~/ 10}',
              glowColor: const Color(0xFFFFD700),
            ),
          ],
        ),
        actions: [
          NeonButton(
            text: 'Next Level',
            onPressed: () {
              Navigator.of(context).pop();
              _startNextLevel();
            },
            glowColor: const Color(0xFF00FF00),
          ),
          NeonButton(
            text: 'Back to Menu',
            onPressed: () {
              Navigator.of(context).pop();
              Navigator.of(context).pop();
            },
            glowColor: const Color(0xFFFF6B35),
          ),
        ],
      ),
    );
  }

  void _showGameOverDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.black.withValues(alpha: 0.9),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
          side: const BorderSide(color: Color(0xFF00FFFF), width: 2),
        ),
        title: NeonText.heading(
          'Game Over!',
          glowColor: const Color(0xFF00FFFF),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            NeonText.body(
              'Score: $_score',
              glowColor: Colors.white,
            ),
            const SizedBox(height: 8),
            NeonText.body(
              'Level Reached: $_level',
              glowColor: const Color(0xFFFFD700),
            ),
            const SizedBox(height: 8),
            NeonText.body(
              'Tokens Earned: ${_score ~/ 20}',
              glowColor: const Color(0xFFFFD700),
            ),
          ],
        ),
        actions: [
          NeonButton(
            text: 'Play Again',
            onPressed: () {
              Navigator.of(context).pop();
              _startNewGame();
            },
            glowColor: const Color(0xFF00FF00),
          ),
          NeonButton(
            text: 'Back to Menu',
            onPressed: () {
              Navigator.of(context).pop();
              Navigator.of(context).pop();
            },
            glowColor: const Color(0xFFFF6B35),
          ),
        ],
      ),
    );
  }

  void _startNextLevel() {
    setState(() {
      _moves = 30;
      _target = _target + 500;
      _gameActive = true;
      _selectedTile = null;
    });
    
    _initializeGrid();
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final tileSize = (screenWidth - 40) / gridSize;
    
    return Scaffold(
      backgroundColor: Colors.black,
      body: SafeArea(
        child: Stack(
          children: [
            // Background
            Container(
              decoration: const BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Color(0xFF0A0A0A),
                    Color(0xFF1A1A2E),
                    Color(0xFF16213E),
                  ],
                ),
              ),
            ),
            
            // Game area
            Positioned.fill(
              top: 120,
              bottom: 100,
              child: Center(
                child: Container(
                  width: screenWidth - 40,
                  height: screenWidth - 40,
                  child: GridView.builder(
                    physics: const NeverScrollableScrollPhysics(),
                    gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: gridSize,
                      crossAxisSpacing: 2,
                      mainAxisSpacing: 2,
                    ),
                    itemCount: gridSize * gridSize,
                    itemBuilder: (context, index) {
                      final row = index ~/ gridSize;
                      final col = index % gridSize;
                      final tile = _grid[row][col];
                      final isSelected = _selectedTile?.row == row && _selectedTile?.col == col;
                      
                      return GestureDetector(
                        onTap: () => _onTileTap(tile),
                        child: Container(
                          decoration: BoxDecoration(
                            color: tile.color.withValues(alpha: tile.type == -1 ? 0.1 : 0.8),
                            border: Border.all(
                              color: isSelected ? Colors.white : tile.color,
                              width: isSelected ? 3 : 1,
                            ),
                            borderRadius: BorderRadius.circular(8),
                            boxShadow: tile.type != -1 ? [
                              BoxShadow(
                                color: tile.color.withValues(alpha: 0.5),
                                blurRadius: isSelected ? 8 : 4,
                                spreadRadius: isSelected ? 2 : 1,
                              ),
                            ] : null,
                          ),
                        ),
                      );
                    },
                  ),
                ),
              ),
            ),
            
            // UI Overlay
            Positioned(
              top: 20,
              left: 20,
              right: 20,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  NeonButton(
                    text: 'Back',
                    onPressed: () => Navigator.of(context).pop(),
                    glowColor: const Color(0xFFFF6B35),
                    width: 80,
                    height: 40,
                  ),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      NeonText.body(
                        'Score: $_score / $_target',
                        glowColor: const Color(0xFF00FFFF),
                        fontSize: 16,
                      ),
                      NeonText.body(
                        'Moves: $_moves',
                        glowColor: const Color(0xFFFFD700),
                        fontSize: 14,
                      ),
                      NeonText.body(
                        'Level: $_level',
                        glowColor: const Color(0xFF00FF00),
                        fontSize: 14,
                      ),
                      if (_combo > 0)
                        NeonText.body(
                          'Combo: x$_combo',
                          glowColor: const Color(0xFFFF00FF),
                          fontSize: 14,
                        ),
                    ],
                  ),
                ],
              ),
            ),
            
            // Instructions
            if (_gameStarted && _gameActive)
              Positioned(
                bottom: 20,
                left: 20,
                right: 20,
                child: Center(
                  child: NeonText.body(
                    'TAP TO SELECT, DRAG TO SWAP',
                    glowColor: Colors.white,
                    fontSize: 16,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}

/// Represents a tile in the match-3 grid
class MatchTile {
  final int row;
  final int col;
  final Color color;
  final int type;

  const MatchTile({
    required this.row,
    required this.col,
    required this.color,
    required this.type,
  });

  MatchTile copyWith({
    int? row,
    int? col,
    Color? color,
    int? type,
  }) {
    return MatchTile(
      row: row ?? this.row,
      col: col ?? this.col,
      color: color ?? this.color,
      type: type ?? this.type,
    );
  }
}
