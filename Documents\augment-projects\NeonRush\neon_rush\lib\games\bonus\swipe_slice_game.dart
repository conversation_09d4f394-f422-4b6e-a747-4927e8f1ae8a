import 'dart:async';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../core/constants.dart';
import '../../ui/neon_text.dart';
import '../../ui/neon_button.dart';
import '../../ui/neon_effects.dart';
import '../../managers/game_state_manager.dart';
import '../../managers/sound_manager.dart';
import '../../utils/neon_haptics.dart';
import '../../models/neon_theme.dart';

/// Swipe Slice - Slice fruits flying upward while avoiding bombs
class SwipeSliceGame extends StatefulWidget {
  final NeonTheme? theme;
  final Function(int score, int tokens)? onGameComplete;

  const SwipeSliceGame({
    super.key,
    this.theme,
    this.onGameComplete,
  });

  @override
  State<SwipeSliceGame> createState() => _SwipeSliceGameState();
}

class _SwipeSliceGameState extends State<SwipeSliceGame>
    with TickerProviderStateMixin {
  late Timer _spawnTimer;
  late Timer _updateTimer;
  
  List<FlyingObject> _objects = [];
  List<SliceEffect> _sliceEffects = [];
  List<Offset> _currentSlicePath = [];
  int _score = 0;
  int _lives = 3;
  bool _gameActive = false;
  bool _gameStarted = false;
  double _spawnRate = 1.5; // seconds between spawns
  int _level = 1;
  
  @override
  void initState() {
    super.initState();
    _startNewGame();
  }

  @override
  void dispose() {
    _spawnTimer.cancel();
    _updateTimer.cancel();
    super.dispose();
  }

  void _startNewGame() {
    setState(() {
      _objects.clear();
      _sliceEffects.clear();
      _currentSlicePath.clear();
      _score = 0;
      _lives = 3;
      _gameActive = true;
      _gameStarted = true;
      _spawnRate = 1.5;
      _level = 1;
    });

    _startSpawning();
    _startUpdating();
  }

  void _startSpawning() {
    _spawnTimer = Timer.periodic(Duration(milliseconds: (_spawnRate * 1000).round()), (timer) {
      if (!_gameActive) {
        timer.cancel();
        return;
      }
      _spawnObject();
      
      // Increase difficulty over time
      if (_score > 0 && _score % 10 == 0) {
        _spawnRate = max(0.5, _spawnRate - 0.1);
        _level = (_score ~/ 10) + 1;
        timer.cancel();
        _startSpawning();
      }
    });
  }

  void _startUpdating() {
    _updateTimer = Timer.periodic(const Duration(milliseconds: 16), (timer) {
      if (!_gameActive) {
        timer.cancel();
        return;
      }
      _updateObjects();
    });
  }

  void _spawnObject() {
    final random = Random();
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    
    // 20% chance for bomb, 80% for fruit
    final isBomb = random.nextDouble() < 0.2;
    
    final object = FlyingObject(
      x: random.nextDouble() * (screenWidth - 60) + 30,
      y: screenHeight + 30,
      velocityY: -200 - (random.nextDouble() * 100), // Upward velocity
      velocityX: (random.nextDouble() - 0.5) * 100, // Slight horizontal drift
      isBomb: isBomb,
      color: isBomb 
        ? const Color(0xFFFF0000)
        : _getFruitColor(),
      size: 40 + random.nextDouble() * 20,
    );

    setState(() {
      _objects.add(object);
    });
  }

  Color _getFruitColor() {
    final colors = [
      const Color(0xFF00FFFF),
      const Color(0xFF00FF00),
      const Color(0xFFFFFF00),
      const Color(0xFFFF6B35),
      const Color(0xFFFF00FF),
    ];
    return colors[Random().nextInt(colors.length)];
  }

  void _updateObjects() {
    final deltaTime = 16 / 1000.0; // 16ms in seconds
    final screenHeight = MediaQuery.of(context).size.height;
    
    setState(() {
      // Update object positions
      for (int i = _objects.length - 1; i >= 0; i--) {
        final object = _objects[i];
        final newX = object.x + (object.velocityX * deltaTime);
        final newY = object.y + (object.velocityY * deltaTime);
        
        _objects[i] = object.copyWith(x: newX, y: newY);
        
        // Remove objects that have fallen off screen or gone too high
        if (newY < -100 || newY > screenHeight + 100) {
          if (!object.isBomb && newY > screenHeight + 100) {
            // Fruit fell off bottom - lose a life
            _loseLife();
          }
          _objects.removeAt(i);
        }
      }
      
      // Update slice effects
      for (int i = _sliceEffects.length - 1; i >= 0; i--) {
        final effect = _sliceEffects[i];
        if (DateTime.now().difference(effect.createdAt).inMilliseconds > 500) {
          _sliceEffects.removeAt(i);
        }
      }
    });
  }

  void _onPanStart(DragStartDetails details) {
    if (!_gameActive) return;
    _currentSlicePath.clear();
    _currentSlicePath.add(details.localPosition);
  }

  void _onPanUpdate(DragUpdateDetails details) {
    if (!_gameActive) return;
    
    setState(() {
      _currentSlicePath.add(details.localPosition);
      
      // Check for collisions with objects
      _checkSliceCollisions(details.localPosition);
    });
  }

  void _onPanEnd(DragEndDetails details) {
    if (!_gameActive) return;
    
    // Clear the slice path after a short delay
    Timer(const Duration(milliseconds: 200), () {
      if (mounted) {
        setState(() {
          _currentSlicePath.clear();
        });
      }
    });
  }

  void _checkSliceCollisions(Offset slicePoint) {
    for (int i = _objects.length - 1; i >= 0; i--) {
      final object = _objects[i];
      final distance = (Offset(object.x, object.y) - slicePoint).distance;
      
      if (distance < object.size / 2) {
        // Object was sliced!
        _sliceObject(object, i);
        break; // Only slice one object per swipe point
      }
    }
  }

  void _sliceObject(FlyingObject object, int index) async {
    setState(() {
      _objects.removeAt(index);
      
      // Add slice effect
      _sliceEffects.add(SliceEffect(
        x: object.x,
        y: object.y,
        color: object.color,
        createdAt: DateTime.now(),
      ));
    });

    if (object.isBomb) {
      // Hit a bomb - game over
      await SoundManager().playSfx(SoundType.gameOver);
      await NeonHaptics.gameOver();
      _endGame();
    } else {
      // Sliced a fruit - score points
      await SoundManager().playSfx(SoundType.buttonClick);
      await NeonHaptics.targetHit();
      setState(() {
        _score += 10;
      });
    }
  }

  void _loseLife() async {
    await NeonHaptics.gameOver();
    setState(() {
      _lives--;
    });
    
    if (_lives <= 0) {
      _endGame();
    }
  }

  void _endGame() async {
    setState(() {
      _gameActive = false;
    });

    final gameState = context.read<GameStateManager>();
    gameState.addTokens(_score ~/ 2); // 1 token per 2 points

    _showGameOverDialog();
  }

  void _showGameOverDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.black.withValues(alpha: 0.9),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
          side: const BorderSide(color: Color(0xFF00FFFF), width: 2),
        ),
        title: NeonText.heading(
          'Game Over!',
          glowColor: const Color(0xFF00FFFF),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            NeonText.body(
              'Score: $_score',
              glowColor: Colors.white,
            ),
            const SizedBox(height: 8),
            NeonText.body(
              'Level Reached: $_level',
              glowColor: const Color(0xFFFFD700),
            ),
            const SizedBox(height: 8),
            NeonText.body(
              'Tokens Earned: ${_score ~/ 2}',
              glowColor: const Color(0xFFFFD700),
            ),
          ],
        ),
        actions: [
          NeonButton(
            text: 'Play Again',
            onPressed: () {
              Navigator.of(context).pop();
              _startNewGame();
            },
            glowColor: const Color(0xFF00FF00),
          ),
          NeonButton(
            text: 'Back to Menu',
            onPressed: () {
              Navigator.of(context).pop();
              Navigator.of(context).pop();
            },
            glowColor: const Color(0xFFFF6B35),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: SafeArea(
        child: Stack(
          children: [
            // Background
            Container(
              decoration: const BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Color(0xFF0A0A0A),
                    Color(0xFF1A1A2E),
                    Color(0xFF16213E),
                  ],
                ),
              ),
            ),
            
            // Game area
            GestureDetector(
              onPanStart: _onPanStart,
              onPanUpdate: _onPanUpdate,
              onPanEnd: _onPanEnd,
              child: Container(
                width: double.infinity,
                height: double.infinity,
                child: CustomPaint(
                  painter: SwipeSlicePainter(
                    objects: _objects,
                    sliceEffects: _sliceEffects,
                    slicePath: _currentSlicePath,
                  ),
                ),
              ),
            ),
            
            // UI Overlay
            Positioned(
              top: 20,
              left: 20,
              right: 20,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  NeonButton(
                    text: 'Back',
                    onPressed: () => Navigator.of(context).pop(),
                    glowColor: const Color(0xFFFF6B35),
                    width: 80,
                    height: 40,
                  ),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      NeonText.body(
                        'Score: $_score',
                        glowColor: const Color(0xFF00FFFF),
                        fontSize: 18,
                      ),
                      NeonText.body(
                        'Lives: $_lives',
                        glowColor: const Color(0xFFFF0000),
                        fontSize: 14,
                      ),
                      NeonText.body(
                        'Level: $_level',
                        glowColor: const Color(0xFFFFD700),
                        fontSize: 14,
                      ),
                    ],
                  ),
                ],
              ),
            ),
            
            // Instructions
            if (_gameStarted && _gameActive)
              Positioned(
                bottom: 100,
                left: 20,
                right: 20,
                child: Center(
                  child: Column(
                    children: [
                      NeonText.body(
                        'SWIPE TO SLICE FRUITS',
                        glowColor: Colors.white,
                        fontSize: 16,
                      ),
                      const SizedBox(height: 8),
                      NeonText.body(
                        'AVOID BOMBS!',
                        glowColor: const Color(0xFFFF0000),
                        fontSize: 14,
                      ),
                    ],
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}

/// Represents a flying object (fruit or bomb)
class FlyingObject {
  final double x;
  final double y;
  final double velocityX;
  final double velocityY;
  final bool isBomb;
  final Color color;
  final double size;

  const FlyingObject({
    required this.x,
    required this.y,
    required this.velocityX,
    required this.velocityY,
    required this.isBomb,
    required this.color,
    required this.size,
  });

  FlyingObject copyWith({
    double? x,
    double? y,
    double? velocityX,
    double? velocityY,
    bool? isBomb,
    Color? color,
    double? size,
  }) {
    return FlyingObject(
      x: x ?? this.x,
      y: y ?? this.y,
      velocityX: velocityX ?? this.velocityX,
      velocityY: velocityY ?? this.velocityY,
      isBomb: isBomb ?? this.isBomb,
      color: color ?? this.color,
      size: size ?? this.size,
    );
  }
}

/// Represents a slice effect
class SliceEffect {
  final double x;
  final double y;
  final Color color;
  final DateTime createdAt;

  const SliceEffect({
    required this.x,
    required this.y,
    required this.color,
    required this.createdAt,
  });
}

/// Custom painter for the swipe slice game
class SwipeSlicePainter extends CustomPainter {
  final List<FlyingObject> objects;
  final List<SliceEffect> sliceEffects;
  final List<Offset> slicePath;

  SwipeSlicePainter({
    required this.objects,
    required this.sliceEffects,
    required this.slicePath,
  });

  @override
  void paint(Canvas canvas, Size size) {
    // Draw flying objects
    for (final object in objects) {
      _drawObject(canvas, object);
    }

    // Draw slice effects
    for (final effect in sliceEffects) {
      _drawSliceEffect(canvas, effect);
    }

    // Draw slice path
    if (slicePath.length > 1) {
      _drawSlicePath(canvas);
    }
  }

  void _drawObject(Canvas canvas, FlyingObject object) {
    final center = Offset(object.x, object.y);
    final radius = object.size / 2;
    
    // Draw glow effect
    final glowPaint = Paint()
      ..color = object.color.withValues(alpha: 0.3)
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 8);
    
    canvas.drawCircle(center, radius + 4, glowPaint);
    
    // Draw main object
    final objectPaint = Paint()
      ..color = object.color.withValues(alpha: 0.8)
      ..style = PaintingStyle.fill;
    
    canvas.drawCircle(center, radius, objectPaint);
    
    // Draw border
    final borderPaint = Paint()
      ..color = object.color
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2;
    
    canvas.drawCircle(center, radius, borderPaint);
    
    // Draw bomb indicator
    if (object.isBomb) {
      final crossPaint = Paint()
        ..color = Colors.white
        ..strokeWidth = 3
        ..strokeCap = StrokeCap.round;
      
      canvas.drawLine(
        Offset(object.x - radius * 0.5, object.y - radius * 0.5),
        Offset(object.x + radius * 0.5, object.y + radius * 0.5),
        crossPaint,
      );
      canvas.drawLine(
        Offset(object.x + radius * 0.5, object.y - radius * 0.5),
        Offset(object.x - radius * 0.5, object.y + radius * 0.5),
        crossPaint,
      );
    }
  }

  void _drawSliceEffect(Canvas canvas, SliceEffect effect) {
    final age = DateTime.now().difference(effect.createdAt).inMilliseconds;
    final alpha = (1.0 - (age / 500.0)).clamp(0.0, 1.0);
    
    final effectPaint = Paint()
      ..color = effect.color.withValues(alpha: alpha * 0.8)
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 12);
    
    canvas.drawCircle(
      Offset(effect.x, effect.y),
      20 + (age / 10),
      effectPaint,
    );
  }

  void _drawSlicePath(Canvas canvas) {
    final pathPaint = Paint()
      ..color = Colors.white.withValues(alpha: 0.8)
      ..strokeWidth = 4
      ..strokeCap = StrokeCap.round
      ..style = PaintingStyle.stroke
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 2);
    
    final path = Path();
    path.moveTo(slicePath.first.dx, slicePath.first.dy);
    
    for (int i = 1; i < slicePath.length; i++) {
      path.lineTo(slicePath[i].dx, slicePath[i].dy);
    }
    
    canvas.drawPath(path, pathPaint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
