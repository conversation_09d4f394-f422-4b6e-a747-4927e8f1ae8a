import 'dart:async';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../core/constants.dart';
import '../../ui/neon_text.dart';
import '../../ui/neon_button.dart';
import '../../ui/neon_effects.dart';
import '../../managers/game_state_manager.dart';
import '../../managers/sound_manager.dart';
import '../../utils/neon_haptics.dart';
import '../../models/neon_theme.dart';

/// TapCraft - Tap rapidly to build a neon structure
class TapCraftGame extends StatefulWidget {
  final NeonTheme? theme;
  final Function(int score, int tokens)? onGameComplete;

  const TapCraftGame({
    super.key,
    this.theme,
    this.onGameComplete,
  });

  @override
  State<TapCraftGame> createState() => _TapCraftGameState();
}

class _TapCraftGameState extends State<TapCraftGame>
    with TickerProviderStateMixin {
  late Timer _gameTimer;
  late Timer _decayTimer;
  late AnimationController _tapController;
  late Animation<double> _tapAnimation;
  
  List<CraftBlock> _blocks = [];
  List<TapEffect> _tapEffects = [];
  double _progress = 0.0;
  double _targetProgress = 100.0;
  bool _gameActive = false;
  bool _gameStarted = false;
  int _score = 0;
  int _level = 1;
  int _timeLeft = 30;
  double _decayRate = 0.5; // progress lost per second
  int _tapsThisSecond = 0;
  int _maxTapsPerSecond = 0;
  
  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _startNewGame();
  }

  @override
  void dispose() {
    _gameTimer.cancel();
    _decayTimer.cancel();
    _tapController.dispose();
    super.dispose();
  }

  void _initializeAnimations() {
    _tapController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );

    _tapAnimation = Tween<double>(
      begin: 1.0,
      end: 1.1,
    ).animate(CurvedAnimation(
      parent: _tapController,
      curve: Curves.easeOut,
    ));
  }

  void _startNewGame() {
    setState(() {
      _blocks.clear();
      _tapEffects.clear();
      _progress = 0.0;
      _targetProgress = 100.0;
      _score = 0;
      _level = 1;
      _timeLeft = 30;
      _gameActive = true;
      _gameStarted = true;
      _decayRate = 0.5;
      _tapsThisSecond = 0;
      _maxTapsPerSecond = 0;
    });

    _startGameTimer();
    _startDecayTimer();
  }

  void _startGameTimer() {
    _gameTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (!_gameActive) {
        timer.cancel();
        return;
      }
      
      setState(() {
        _timeLeft--;
        _maxTapsPerSecond = max(_maxTapsPerSecond, _tapsThisSecond);
        _tapsThisSecond = 0;
      });
      
      if (_timeLeft <= 0) {
        _endGame();
      }
    });
  }

  void _startDecayTimer() {
    _decayTimer = Timer.periodic(const Duration(milliseconds: 100), (timer) {
      if (!_gameActive) {
        timer.cancel();
        return;
      }
      
      setState(() {
        _progress = max(0, _progress - (_decayRate * 0.1));
        
        // Remove blocks as progress decreases
        _updateBlocks();
        
        // Clean up old tap effects
        _tapEffects.removeWhere((effect) =>
            DateTime.now().difference(effect.createdAt).inMilliseconds > 500);
      });
    });
  }

  void _updateBlocks() {
    final targetBlockCount = (_progress / 5).floor(); // One block per 5% progress
    
    if (_blocks.length > targetBlockCount) {
      // Remove blocks from top
      _blocks.removeRange(targetBlockCount, _blocks.length);
    } else if (_blocks.length < targetBlockCount) {
      // Add new blocks
      for (int i = _blocks.length; i < targetBlockCount; i++) {
        _blocks.add(_createBlock(i));
      }
    }
  }

  CraftBlock _createBlock(int level) {
    final colors = [
      const Color(0xFF00FFFF),
      const Color(0xFF00FF00),
      const Color(0xFFFFFF00),
      const Color(0xFFFF6B35),
      const Color(0xFFFF00FF),
      const Color(0xFF8A2BE2),
    ];
    
    return CraftBlock(
      level: level,
      color: colors[level % colors.length],
      width: (80 - (level * 2).clamp(0, 30)).toDouble(),
      height: 20,
    );
  }

  void _onTap(TapDownDetails details) async {
    if (!_gameActive) return;
    
    final tapIncrease = 2.0 + (_level * 0.5); // More progress per tap at higher levels
    
    setState(() {
      _progress = min(_targetProgress, _progress + tapIncrease);
      _tapsThisSecond++;
      
      // Add tap effect
      _tapEffects.add(TapEffect(
        x: details.localPosition.dx,
        y: details.localPosition.dy,
        createdAt: DateTime.now(),
      ));
    });
    
    // Update blocks
    _updateBlocks();
    
    // Check for level completion
    if (_progress >= _targetProgress) {
      await _completeLevel();
    } else {
      await SoundManager().playSfx(SoundType.buttonClick);
      await NeonHaptics.targetHit();
      
      // Tap animation
      _tapController.forward().then((_) {
        _tapController.reverse();
      });
    }
  }

  Future<void> _completeLevel() async {
    setState(() {
      _gameActive = false;
    });

    await NeonHaptics.levelComplete();
    await SoundManager().playSfx(SoundType.buttonClick);

    final timeBonus = _timeLeft * 10;
    final speedBonus = _maxTapsPerSecond * 5;
    setState(() {
      _score += 100 + timeBonus + speedBonus;
      _level++;
    });

    _showLevelCompleteDialog();
  }

  void _endGame() async {
    setState(() {
      _gameActive = false;
    });

    await NeonHaptics.gameOver();

    final gameState = context.read<GameStateManager>();
    gameState.addTokens(_score ~/ 10); // 1 token per 10 points

    _showGameOverDialog();
  }

  void _showLevelCompleteDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.black.withValues(alpha: 0.9),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
          side: const BorderSide(color: Color(0xFF00FF00), width: 2),
        ),
        title: NeonText.heading(
          'Structure Complete!',
          glowColor: const Color(0xFF00FF00),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            NeonText.body(
              'Score: $_score',
              glowColor: Colors.white,
            ),
            const SizedBox(height: 8),
            NeonText.body(
              'Time Bonus: ${_timeLeft * 10}',
              glowColor: const Color(0xFFFFD700),
            ),
            const SizedBox(height: 8),
            NeonText.body(
              'Speed Bonus: ${_maxTapsPerSecond * 5}',
              glowColor: const Color(0xFF00FFFF),
            ),
            const SizedBox(height: 8),
            NeonText.body(
              'Level: $_level',
              glowColor: const Color(0xFF00FF00),
            ),
          ],
        ),
        actions: [
          NeonButton(
            text: 'Next Level',
            onPressed: () {
              Navigator.of(context).pop();
              _startNextLevel();
            },
            glowColor: const Color(0xFF00FF00),
          ),
          NeonButton(
            text: 'Back to Menu',
            onPressed: () {
              Navigator.of(context).pop();
              Navigator.of(context).pop();
            },
            glowColor: const Color(0xFFFF6B35),
          ),
        ],
      ),
    );
  }

  void _showGameOverDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.black.withValues(alpha: 0.9),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
          side: const BorderSide(color: Color(0xFF00FFFF), width: 2),
        ),
        title: NeonText.heading(
          'Time\'s Up!',
          glowColor: const Color(0xFF00FFFF),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            NeonText.body(
              'Score: $_score',
              glowColor: Colors.white,
            ),
            const SizedBox(height: 8),
            NeonText.body(
              'Level Reached: $_level',
              glowColor: const Color(0xFFFFD700),
            ),
            const SizedBox(height: 8),
            NeonText.body(
              'Max Taps/Sec: $_maxTapsPerSecond',
              glowColor: const Color(0xFF00FFFF),
            ),
            const SizedBox(height: 8),
            NeonText.body(
              'Tokens Earned: ${_score ~/ 10}',
              glowColor: const Color(0xFFFFD700),
            ),
          ],
        ),
        actions: [
          NeonButton(
            text: 'Play Again',
            onPressed: () {
              Navigator.of(context).pop();
              _startNewGame();
            },
            glowColor: const Color(0xFF00FF00),
          ),
          NeonButton(
            text: 'Back to Menu',
            onPressed: () {
              Navigator.of(context).pop();
              Navigator.of(context).pop();
            },
            glowColor: const Color(0xFFFF6B35),
          ),
        ],
      ),
    );
  }

  void _startNextLevel() {
    setState(() {
      _progress = 0.0;
      _targetProgress = _targetProgress + 50; // Increase target each level
      _timeLeft = max(20, 30 - (_level * 2)); // Decrease time each level
      _decayRate = _decayRate + 0.2; // Increase decay rate
      _gameActive = true;
      _blocks.clear();
      _tapEffects.clear();
      _maxTapsPerSecond = 0;
    });

    _startGameTimer();
    _startDecayTimer();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: SafeArea(
        child: Stack(
          children: [
            // Background
            Container(
              decoration: const BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Color(0xFF0A0A0A),
                    Color(0xFF1A1A2E),
                    Color(0xFF16213E),
                  ],
                ),
              ),
            ),
            
            // Game area
            GestureDetector(
              onTapDown: _onTap,
              child: AnimatedBuilder(
                animation: _tapAnimation,
                builder: (context, child) {
                  return Transform.scale(
                    scale: _tapAnimation.value,
                    child: Container(
                      width: double.infinity,
                      height: double.infinity,
                      child: CustomPaint(
                        painter: TapCraftPainter(
                          blocks: _blocks,
                          tapEffects: _tapEffects,
                          progress: _progress,
                          targetProgress: _targetProgress,
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
            
            // Progress bar
            Positioned(
              top: 80,
              left: 20,
              right: 20,
              child: Column(
                children: [
                  NeonText.body(
                    'Progress: ${_progress.toStringAsFixed(1)}% / ${_targetProgress.toStringAsFixed(0)}%',
                    glowColor: const Color(0xFF00FFFF),
                    fontSize: 16,
                  ),
                  const SizedBox(height: 8),
                  Container(
                    height: 20,
                    decoration: BoxDecoration(
                      border: Border.all(color: const Color(0xFF00FFFF), width: 2),
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(8),
                      child: LinearProgressIndicator(
                        value: _progress / _targetProgress,
                        backgroundColor: Colors.transparent,
                        valueColor: const AlwaysStoppedAnimation<Color>(Color(0xFF00FFFF)),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            
            // UI Overlay
            Positioned(
              top: 20,
              left: 20,
              right: 20,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  NeonButton(
                    text: 'Back',
                    onPressed: () => Navigator.of(context).pop(),
                    glowColor: const Color(0xFFFF6B35),
                    width: 80,
                    height: 40,
                  ),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      NeonText.body(
                        'Score: $_score',
                        glowColor: const Color(0xFF00FFFF),
                        fontSize: 18,
                      ),
                      NeonText.body(
                        'Time: $_timeLeft',
                        glowColor: _timeLeft <= 10 ? const Color(0xFFFF0000) : const Color(0xFFFFD700),
                        fontSize: 14,
                      ),
                      NeonText.body(
                        'Level: $_level',
                        glowColor: const Color(0xFF00FF00),
                        fontSize: 14,
                      ),
                    ],
                  ),
                ],
              ),
            ),
            
            // Instructions
            if (_gameStarted && _gameActive)
              Positioned(
                bottom: 100,
                left: 20,
                right: 20,
                child: Center(
                  child: Column(
                    children: [
                      NeonText.body(
                        'TAP RAPIDLY TO BUILD',
                        glowColor: Colors.white,
                        fontSize: 18,
                      ),
                      const SizedBox(height: 8),
                      NeonText.body(
                        'Structure decays over time!',
                        glowColor: const Color(0xFFFF6B35),
                        fontSize: 14,
                      ),
                    ],
                  ),
                ),
              ),
            
            // Stats
            Positioned(
              bottom: 20,
              left: 20,
              right: 20,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  NeonText.body(
                    'Taps/Sec: $_tapsThisSecond',
                    glowColor: const Color(0xFF00FF00),
                    fontSize: 12,
                  ),
                  NeonText.body(
                    'Max: $_maxTapsPerSecond',
                    glowColor: const Color(0xFFFFD700),
                    fontSize: 12,
                  ),
                  NeonText.body(
                    'Blocks: ${_blocks.length}',
                    glowColor: const Color(0xFF00FFFF),
                    fontSize: 12,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// Represents a block in the structure
class CraftBlock {
  final int level;
  final Color color;
  final double width;
  final double height;

  const CraftBlock({
    required this.level,
    required this.color,
    required this.width,
    required this.height,
  });
}

/// Represents a tap effect
class TapEffect {
  final double x;
  final double y;
  final DateTime createdAt;

  const TapEffect({
    required this.x,
    required this.y,
    required this.createdAt,
  });
}

/// Custom painter for the tap craft game
class TapCraftPainter extends CustomPainter {
  final List<CraftBlock> blocks;
  final List<TapEffect> tapEffects;
  final double progress;
  final double targetProgress;

  TapCraftPainter({
    required this.blocks,
    required this.tapEffects,
    required this.progress,
    required this.targetProgress,
  });

  @override
  void paint(Canvas canvas, Size size) {
    // Draw base platform
    _drawBase(canvas, size);
    
    // Draw blocks
    for (int i = 0; i < blocks.length; i++) {
      _drawBlock(canvas, size, blocks[i], i);
    }
    
    // Draw tap effects
    for (final effect in tapEffects) {
      _drawTapEffect(canvas, effect);
    }
  }

  void _drawBase(Canvas canvas, Size size) {
    final baseRect = RRect.fromRectAndRadius(
      Rect.fromLTWH(
        size.width / 2 - 60,
        size.height - 150,
        120,
        30,
      ),
      const Radius.circular(15),
    );
    
    final basePaint = Paint()
      ..color = const Color(0xFF444444)
      ..style = PaintingStyle.fill;
    
    final baseGlowPaint = Paint()
      ..color = const Color(0xFF00FFFF).withValues(alpha: 0.3)
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 8);
    
    canvas.drawRRect(baseRect, baseGlowPaint);
    canvas.drawRRect(baseRect, basePaint);
    
    final borderPaint = Paint()
      ..color = const Color(0xFF00FFFF)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2;
    
    canvas.drawRRect(baseRect, borderPaint);
  }

  void _drawBlock(Canvas canvas, Size size, CraftBlock block, int index) {
    final blockRect = RRect.fromRectAndRadius(
      Rect.fromLTWH(
        size.width / 2 - block.width / 2,
        size.height - 150 - ((index + 1) * (block.height + 2)),
        block.width,
        block.height,
      ),
      const Radius.circular(8),
    );
    
    // Draw glow effect
    final glowPaint = Paint()
      ..color = block.color.withValues(alpha: 0.3)
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 6);
    
    canvas.drawRRect(blockRect, glowPaint);
    
    // Draw main block
    final blockPaint = Paint()
      ..color = block.color.withValues(alpha: 0.8)
      ..style = PaintingStyle.fill;
    
    canvas.drawRRect(blockRect, blockPaint);
    
    // Draw border
    final borderPaint = Paint()
      ..color = block.color
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2;
    
    canvas.drawRRect(blockRect, borderPaint);
  }

  void _drawTapEffect(Canvas canvas, TapEffect effect) {
    final age = DateTime.now().difference(effect.createdAt).inMilliseconds;
    final alpha = (1.0 - (age / 500.0)).clamp(0.0, 1.0);
    final size = 10 + (age / 25);
    
    final effectPaint = Paint()
      ..color = const Color(0xFF00FFFF).withValues(alpha: alpha * 0.8)
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 8);
    
    canvas.drawCircle(
      Offset(effect.x, effect.y),
      size,
      effectPaint,
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
