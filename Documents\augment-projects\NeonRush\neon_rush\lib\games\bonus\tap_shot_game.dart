import 'dart:async';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../core/constants.dart';
import '../../ui/neon_text.dart';
import '../../ui/neon_button.dart';
import '../../ui/neon_effects.dart';
import '../../managers/game_state_manager.dart';
import '../../managers/sound_manager.dart';
import '../../utils/neon_haptics.dart';
import '../../models/neon_theme.dart';

/// TapShot - Tap lanes to shoot falling targets
class TapShotGame extends StatefulWidget {
  final NeonTheme? theme;
  final Function(int score, int tokens)? onGameComplete;

  const TapShotGame({
    super.key,
    this.theme,
    this.onGameComplete,
  });

  @override
  State<TapShotGame> createState() => _TapShotGameState();
}

class _TapShotGameState extends State<TapShotGame>
    with TickerProviderStateMixin {
  late Timer _spawnTimer;
  late Timer _updateTimer;
  
  List<FallingTarget> _targets = [];
  List<Bullet> _bullets = [];
  List<Explosion> _explosions = [];
  int _score = 0;
  int _lives = 3;
  bool _gameActive = false;
  bool _gameStarted = false;
  double _spawnRate = 2.0; // seconds between spawns
  int _level = 1;
  
  static const int laneCount = 4;
  static const double targetSpeed = 100.0; // pixels per second
  static const double bulletSpeed = 300.0; // pixels per second
  
  @override
  void initState() {
    super.initState();
    _startNewGame();
  }

  @override
  void dispose() {
    _spawnTimer.cancel();
    _updateTimer.cancel();
    super.dispose();
  }

  void _startNewGame() {
    setState(() {
      _targets.clear();
      _bullets.clear();
      _explosions.clear();
      _score = 0;
      _lives = 3;
      _gameActive = true;
      _gameStarted = true;
      _spawnRate = 2.0;
      _level = 1;
    });

    _startSpawning();
    _startUpdating();
  }

  void _startSpawning() {
    _spawnTimer = Timer.periodic(Duration(milliseconds: (_spawnRate * 1000).round()), (timer) {
      if (!_gameActive) {
        timer.cancel();
        return;
      }
      _spawnTarget();
      
      // Increase difficulty over time
      if (_score > 0 && _score % 20 == 0) {
        _spawnRate = max(0.5, _spawnRate - 0.1);
        _level = (_score ~/ 20) + 1;
        timer.cancel();
        _startSpawning();
      }
    });
  }

  void _startUpdating() {
    _updateTimer = Timer.periodic(const Duration(milliseconds: 16), (timer) {
      if (!_gameActive) {
        timer.cancel();
        return;
      }
      _updateGame();
    });
  }

  void _spawnTarget() {
    final random = Random();
    final screenWidth = MediaQuery.of(context).size.width;
    final laneWidth = screenWidth / laneCount;
    final lane = random.nextInt(laneCount);
    
    // Random target type
    final isSpecial = random.nextDouble() < 0.2; // 20% chance for special target
    
    final target = FallingTarget(
      x: lane * laneWidth + laneWidth / 2,
      y: -30,
      lane: lane,
      isSpecial: isSpecial,
      color: isSpecial 
        ? const Color(0xFFFFD700) // Gold for special
        : const Color(0xFFFF0000), // Red for normal
      points: isSpecial ? 20 : 10,
    );

    setState(() {
      _targets.add(target);
    });
  }

  void _updateGame() {
    final deltaTime = 16 / 1000.0; // 16ms in seconds
    final screenHeight = MediaQuery.of(context).size.height;
    
    setState(() {
      // Update targets
      for (int i = _targets.length - 1; i >= 0; i--) {
        final target = _targets[i];
        final newY = target.y + (targetSpeed * deltaTime);
        _targets[i] = target.copyWith(y: newY);
        
        // Remove targets that have fallen off screen
        if (newY > screenHeight + 50) {
          _targets.removeAt(i);
          _loseLife();
        }
      }
      
      // Update bullets
      for (int i = _bullets.length - 1; i >= 0; i--) {
        final bullet = _bullets[i];
        final newY = bullet.y - (bulletSpeed * deltaTime);
        _bullets[i] = bullet.copyWith(y: newY);
        
        // Remove bullets that have gone off screen
        if (newY < -50) {
          _bullets.removeAt(i);
        }
      }
      
      // Update explosions
      for (int i = _explosions.length - 1; i >= 0; i--) {
        final explosion = _explosions[i];
        if (DateTime.now().difference(explosion.createdAt).inMilliseconds > 500) {
          _explosions.removeAt(i);
        }
      }
    });
    
    // Check collisions
    _checkCollisions();
  }

  void _checkCollisions() {
    for (int bulletIndex = _bullets.length - 1; bulletIndex >= 0; bulletIndex--) {
      final bullet = _bullets[bulletIndex];
      
      for (int targetIndex = _targets.length - 1; targetIndex >= 0; targetIndex--) {
        final target = _targets[targetIndex];
        
        // Check if bullet hits target (same lane and close enough)
        if (bullet.lane == target.lane &&
            (bullet.y - target.y).abs() < 30 &&
            (bullet.x - target.x).abs() < 30) {
          
          // Hit!
          _hitTarget(target, targetIndex);
          _bullets.removeAt(bulletIndex);
          break;
        }
      }
    }
  }

  void _hitTarget(FallingTarget target, int targetIndex) async {
    setState(() {
      _targets.removeAt(targetIndex);
      _score += target.points;
      
      // Add explosion effect
      _explosions.add(Explosion(
        x: target.x,
        y: target.y,
        color: target.color,
        createdAt: DateTime.now(),
      ));
    });

    if (target.isSpecial) {
      await NeonHaptics.comboAchieved();
    } else {
      await NeonHaptics.targetHit();
    }
    
    await SoundManager().playSfx(SoundType.buttonClick);
  }

  void _loseLife() async {
    await NeonHaptics.gameOver();
    setState(() {
      _lives--;
    });
    
    if (_lives <= 0) {
      _endGame();
    }
  }

  void _shootBullet(int lane) async {
    if (!_gameActive) return;
    
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final laneWidth = screenWidth / laneCount;
    
    final bullet = Bullet(
      x: lane * laneWidth + laneWidth / 2,
      y: screenHeight - 100,
      lane: lane,
      color: const Color(0xFF00FFFF),
    );
    
    setState(() {
      _bullets.add(bullet);
    });
    
    await SoundManager().playSfx(SoundType.buttonClick);
    await NeonHaptics.targetHit();
  }

  void _endGame() async {
    setState(() {
      _gameActive = false;
    });

    await NeonHaptics.gameOver();

    final gameState = context.read<GameStateManager>();
    gameState.addTokens(_score ~/ 5); // 1 token per 5 points

    _showGameOverDialog();
  }

  void _showGameOverDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.black.withValues(alpha: 0.9),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
          side: const BorderSide(color: Color(0xFF00FFFF), width: 2),
        ),
        title: NeonText.heading(
          'Game Over!',
          glowColor: const Color(0xFF00FFFF),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            NeonText.body(
              'Score: $_score',
              glowColor: Colors.white,
            ),
            const SizedBox(height: 8),
            NeonText.body(
              'Level Reached: $_level',
              glowColor: const Color(0xFFFFD700),
            ),
            const SizedBox(height: 8),
            NeonText.body(
              'Tokens Earned: ${_score ~/ 5}',
              glowColor: const Color(0xFFFFD700),
            ),
          ],
        ),
        actions: [
          NeonButton(
            text: 'Play Again',
            onPressed: () {
              Navigator.of(context).pop();
              _startNewGame();
            },
            glowColor: const Color(0xFF00FF00),
          ),
          NeonButton(
            text: 'Back to Menu',
            onPressed: () {
              Navigator.of(context).pop();
              Navigator.of(context).pop();
            },
            glowColor: const Color(0xFFFF6B35),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final laneWidth = screenWidth / laneCount;
    
    return Scaffold(
      backgroundColor: Colors.black,
      body: SafeArea(
        child: Stack(
          children: [
            // Background
            Container(
              decoration: const BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Color(0xFF0A0A0A),
                    Color(0xFF1A1A2E),
                    Color(0xFF16213E),
                  ],
                ),
              ),
            ),
            
            // Lane dividers
            ...List.generate(laneCount - 1, (index) {
              final x = (index + 1) * laneWidth;
              return Positioned(
                left: x - 1,
                top: 80,
                bottom: 0,
                child: Container(
                  width: 2,
                  decoration: BoxDecoration(
                    color: const Color(0xFF00FFFF).withValues(alpha: 0.3),
                    boxShadow: [
                      BoxShadow(
                        color: const Color(0xFF00FFFF).withValues(alpha: 0.5),
                        blurRadius: 4,
                        spreadRadius: 1,
                      ),
                    ],
                  ),
                ),
              );
            }),
            
            // Game area
            Positioned.fill(
              top: 80,
              child: CustomPaint(
                painter: TapShotPainter(
                  targets: _targets,
                  bullets: _bullets,
                  explosions: _explosions,
                ),
              ),
            ),
            
            // Shooting lanes
            Positioned(
              bottom: 0,
              left: 0,
              right: 0,
              height: 100,
              child: Row(
                children: List.generate(laneCount, (lane) {
                  return Expanded(
                    child: GestureDetector(
                      onTap: () => _shootBullet(lane),
                      child: Container(
                        decoration: BoxDecoration(
                          color: const Color(0xFF00FFFF).withValues(alpha: 0.1),
                          border: Border.all(
                            color: const Color(0xFF00FFFF).withValues(alpha: 0.3),
                            width: 1,
                          ),
                        ),
                        child: Center(
                          child: Icon(
                            Icons.keyboard_arrow_up,
                            color: const Color(0xFF00FFFF),
                            size: 40,
                          ),
                        ),
                      ),
                    ),
                  );
                }),
              ),
            ),
            
            // UI Overlay
            Positioned(
              top: 20,
              left: 20,
              right: 20,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  NeonButton(
                    text: 'Back',
                    onPressed: () => Navigator.of(context).pop(),
                    glowColor: const Color(0xFFFF6B35),
                    width: 80,
                    height: 40,
                  ),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      NeonText.body(
                        'Score: $_score',
                        glowColor: const Color(0xFF00FFFF),
                        fontSize: 18,
                      ),
                      NeonText.body(
                        'Lives: $_lives',
                        glowColor: const Color(0xFFFF0000),
                        fontSize: 14,
                      ),
                      NeonText.body(
                        'Level: $_level',
                        glowColor: const Color(0xFFFFD700),
                        fontSize: 14,
                      ),
                    ],
                  ),
                ],
              ),
            ),
            
            // Instructions
            if (_gameStarted && _gameActive)
              Positioned(
                bottom: 120,
                left: 20,
                right: 20,
                child: Center(
                  child: Column(
                    children: [
                      NeonText.body(
                        'TAP LANES TO SHOOT',
                        glowColor: Colors.white,
                        fontSize: 16,
                      ),
                      const SizedBox(height: 8),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Container(
                            width: 20,
                            height: 20,
                            decoration: const BoxDecoration(
                              color: Color(0xFFFF0000),
                              shape: BoxShape.circle,
                            ),
                          ),
                          const SizedBox(width: 8),
                          NeonText.body(
                            '10 pts  ',
                            glowColor: const Color(0xFFFF0000),
                            fontSize: 12,
                          ),
                          Container(
                            width: 20,
                            height: 20,
                            decoration: const BoxDecoration(
                              color: Color(0xFFFFD700),
                              shape: BoxShape.circle,
                            ),
                          ),
                          const SizedBox(width: 8),
                          NeonText.body(
                            '20 pts',
                            glowColor: const Color(0xFFFFD700),
                            fontSize: 12,
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}

/// Represents a falling target
class FallingTarget {
  final double x;
  final double y;
  final int lane;
  final bool isSpecial;
  final Color color;
  final int points;

  const FallingTarget({
    required this.x,
    required this.y,
    required this.lane,
    required this.isSpecial,
    required this.color,
    required this.points,
  });

  FallingTarget copyWith({
    double? x,
    double? y,
    int? lane,
    bool? isSpecial,
    Color? color,
    int? points,
  }) {
    return FallingTarget(
      x: x ?? this.x,
      y: y ?? this.y,
      lane: lane ?? this.lane,
      isSpecial: isSpecial ?? this.isSpecial,
      color: color ?? this.color,
      points: points ?? this.points,
    );
  }
}

/// Represents a bullet
class Bullet {
  final double x;
  final double y;
  final int lane;
  final Color color;

  const Bullet({
    required this.x,
    required this.y,
    required this.lane,
    required this.color,
  });

  Bullet copyWith({
    double? x,
    double? y,
    int? lane,
    Color? color,
  }) {
    return Bullet(
      x: x ?? this.x,
      y: y ?? this.y,
      lane: lane ?? this.lane,
      color: color ?? this.color,
    );
  }
}

/// Represents an explosion effect
class Explosion {
  final double x;
  final double y;
  final Color color;
  final DateTime createdAt;

  const Explosion({
    required this.x,
    required this.y,
    required this.color,
    required this.createdAt,
  });
}

/// Custom painter for the tap shot game
class TapShotPainter extends CustomPainter {
  final List<FallingTarget> targets;
  final List<Bullet> bullets;
  final List<Explosion> explosions;

  TapShotPainter({
    required this.targets,
    required this.bullets,
    required this.explosions,
  });

  @override
  void paint(Canvas canvas, Size size) {
    // Draw targets
    for (final target in targets) {
      _drawTarget(canvas, target);
    }

    // Draw bullets
    for (final bullet in bullets) {
      _drawBullet(canvas, bullet);
    }

    // Draw explosions
    for (final explosion in explosions) {
      _drawExplosion(canvas, explosion);
    }
  }

  void _drawTarget(Canvas canvas, FallingTarget target) {
    final center = Offset(target.x, target.y);
    final radius = target.isSpecial ? 20.0 : 15.0;
    
    // Draw glow effect
    final glowPaint = Paint()
      ..color = target.color.withValues(alpha: 0.3)
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 8);
    
    canvas.drawCircle(center, radius + 4, glowPaint);
    
    // Draw main target
    final targetPaint = Paint()
      ..color = target.color.withValues(alpha: 0.8)
      ..style = PaintingStyle.fill;
    
    canvas.drawCircle(center, radius, targetPaint);
    
    // Draw border
    final borderPaint = Paint()
      ..color = target.color
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2;
    
    canvas.drawCircle(center, radius, borderPaint);
    
    // Draw special indicator
    if (target.isSpecial) {
      final starPaint = Paint()
        ..color = Colors.white
        ..style = PaintingStyle.fill;
      
      _drawStar(canvas, center, 8, starPaint);
    }
  }

  void _drawBullet(Canvas canvas, Bullet bullet) {
    final center = Offset(bullet.x, bullet.y);
    const radius = 8.0;
    
    // Draw glow effect
    final glowPaint = Paint()
      ..color = bullet.color.withValues(alpha: 0.3)
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 6);
    
    canvas.drawCircle(center, radius + 2, glowPaint);
    
    // Draw main bullet
    final bulletPaint = Paint()
      ..color = bullet.color.withValues(alpha: 0.9)
      ..style = PaintingStyle.fill;
    
    canvas.drawCircle(center, radius, bulletPaint);
    
    // Draw border
    final borderPaint = Paint()
      ..color = bullet.color
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1;
    
    canvas.drawCircle(center, radius, borderPaint);
  }

  void _drawExplosion(Canvas canvas, Explosion explosion) {
    final age = DateTime.now().difference(explosion.createdAt).inMilliseconds;
    final alpha = (1.0 - (age / 500.0)).clamp(0.0, 1.0);
    final size = 10 + (age / 10);
    
    final explosionPaint = Paint()
      ..color = explosion.color.withValues(alpha: alpha * 0.8)
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 12);
    
    canvas.drawCircle(
      Offset(explosion.x, explosion.y),
      size,
      explosionPaint,
    );
  }

  void _drawStar(Canvas canvas, Offset center, double size, Paint paint) {
    final path = Path();
    const points = 5;
    const angle = (2 * pi) / points;
    
    for (int i = 0; i < points; i++) {
      final outerAngle = i * angle - pi / 2;
      final innerAngle = outerAngle + angle / 2;
      
      final outerX = center.dx + cos(outerAngle) * size;
      final outerY = center.dy + sin(outerAngle) * size;
      final innerX = center.dx + cos(innerAngle) * size * 0.5;
      final innerY = center.dy + sin(innerAngle) * size * 0.5;
      
      if (i == 0) {
        path.moveTo(outerX, outerY);
      } else {
        path.lineTo(outerX, outerY);
      }
      path.lineTo(innerX, innerY);
    }
    path.close();
    
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
