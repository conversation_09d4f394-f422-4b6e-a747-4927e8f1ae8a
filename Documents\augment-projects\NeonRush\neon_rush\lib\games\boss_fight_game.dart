import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'dart:math';
import 'dart:async';


import '../core/sound_manager.dart';
import '../utils/haptic_feedback.dart';
import '../ui/neon_text.dart';
import '../ui/dynamic_background.dart';
import '../ui/neon_effects.dart';

import '../models/neon_theme.dart';

/// Boss fight game for every 10th level
class BossFightGame extends StatefulWidget {
  final int levelNumber;
  final Function(int score, int taps) onGameComplete;

  const BossFightGame({
    super.key,
    required this.levelNumber,
    required this.onGameComplete,
  });

  @override
  State<BossFightGame> createState() => _BossFightGameState();
}

class _BossFightGameState extends State<BossFightGame>
    with TickerProviderStateMixin {
  late AnimationController _bossController;
  late AnimationController _pulseController;
  late Animation<double> _bossScale;
  late Animation<double> _pulseAnimation;
  
  int _currentStage = 1;
  int _tapsRequired = 150;
  int _currentTaps = 0;
  int _totalTaps = 0;
  int _timeLeft = 90; // 1.5 minutes
  bool _gameActive = true;
  late Timer _gameTimer;
  
  final List<Offset> _redAreas = [];
  late Timer _redAreaTimer;
  
  final List<Widget> _particleEffects = [];

  // Theme for boss fight
  final NeonTheme _theme = NeonThemes.neonPink;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _startGame();
    _startRedAreaSpawning();
  }

  void _initializeAnimations() {
    _bossController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    )..repeat(reverse: true);

    _bossScale = Tween<double>(
      begin: 1.0,
      end: 0.9,
    ).animate(CurvedAnimation(
      parent: _bossController,
      curve: Curves.easeInOut,
    ));

    _pulseAnimation = Tween<double>(
      begin: 0.8,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));
  }

  void _startGame() {
    _gameTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (!_gameActive) {
        timer.cancel();
        return;
      }
      
      setState(() {
        _timeLeft--;
        if (_timeLeft <= 0) {
          _endGame();
        }
      });
    });
  }

  void _startRedAreaSpawning() {
    _redAreaTimer = Timer.periodic(const Duration(seconds: 3), (timer) {
      if (!_gameActive) {
        timer.cancel();
        return;
      }
      _spawnRedArea();
    });
  }

  void _spawnRedArea() {
    final random = Random();
    final screenSize = MediaQuery.of(context).size;
    
    final x = random.nextDouble() * (screenSize.width - 100) + 50;
    final y = random.nextDouble() * (screenSize.height - 300) + 150;
    
    setState(() {
      _redAreas.add(Offset(x, y));
    });
    
    // Remove red area after 2 seconds
    Timer(const Duration(seconds: 2), () {
      setState(() {
        _redAreas.removeWhere((area) => area == Offset(x, y));
      });
    });
  }

  void _tapBoss(Offset tapPosition) {
    if (!_gameActive) return;
    
    // Check if tapped on red area
    bool hitRedArea = false;
    for (final redArea in _redAreas) {
      final distance = (tapPosition - redArea).distance;
      if (distance < 50) {
        hitRedArea = true;
        // Penalty: lose 5 seconds
        setState(() {
          _timeLeft = (_timeLeft - 5).clamp(0, 90);
        });
        NeonHaptics.gameOver();
        break;
      }
    }
    
    if (!hitRedArea) {
      setState(() {
        _currentTaps++;
        _totalTaps++;
      });
      
      // Boss hit animation
      _bossController.forward().then((_) => _bossController.reverse());
      
      NeonHaptics.targetHit();
      SoundManager().playSfx(SoundType.buttonClick);
      
      // Check stage completion
      if (_currentTaps >= _tapsRequired) {
        _nextStage();
      }
    }
  }

  void _nextStage() {
    if (_currentStage >= 3) {
      _endGame();
      return;
    }
    
    setState(() {
      _currentStage++;
      _currentTaps = 0;
      _tapsRequired = _currentStage == 2 ? 200 : 250;
    });
    
    SoundManager().playSfx(SoundType.reward);
  }

  void _endGame() {
    setState(() {
      _gameActive = false;
    });
    
    _gameTimer.cancel();
    _redAreaTimer.cancel();
    
    // Calculate score based on taps and time
    final score = (_totalTaps * 10) + (_timeLeft * 5);
    widget.onGameComplete(score, _totalTaps);
  }

  @override
  void dispose() {
    _bossController.dispose();
    _pulseController.dispose();
    _gameTimer.cancel();
    _redAreaTimer.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: DynamicNeonBackground(
        theme: _theme,
        intensity: 1.0,
        child: SafeArea(
          child: Stack(
            children: [
              // Boss
              _buildBoss(),
              
              // Red penalty areas
              ..._redAreas.map((area) => _buildRedArea(area)),
              
              // Particle effects
              ..._particleEffects,
              
              // UI
              _buildUI(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildBoss() {
    final screenSize = MediaQuery.of(context).size;
    final bossSize = _getBossSize();
    
    return Positioned(
      left: (screenSize.width - bossSize) / 2,
      top: (screenSize.height - bossSize) / 2,
      child: GestureDetector(
        onTapDown: (details) => _tapBoss(details.globalPosition),
        child: AnimatedBuilder(
          animation: Listenable.merge([_bossScale, _pulseAnimation]),
          builder: (context, child) {
            return Transform.scale(
              scale: _bossScale.value * _pulseAnimation.value,
              child: Container(
                width: bossSize,
                height: bossSize,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  gradient: RadialGradient(
                    colors: [
                      _theme.primary.withValues(alpha: 0.8),
                      _theme.primary.withValues(alpha: 0.4),
                      Colors.transparent,
                    ],
                  ),
                  boxShadow: NeonEffects.createGlow(
                    color: _theme.primary,
                    intensity: 1.5,
                  ),
                ),
                child: Center(
                  child: NeonText.title(
                    'BOSS',
                    glowColor: _theme.primary,
                    fontSize: 24,
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildRedArea(Offset position) {
    return Positioned(
      left: position.dx - 50,
      top: position.dy - 50,
      child: Container(
        width: 100,
        height: 100,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: Colors.red.withValues(alpha: 0.3),
          border: Border.all(color: Colors.red, width: 3),
          boxShadow: NeonEffects.createGlow(
            color: Colors.red,
            intensity: 1.0,
          ),
        ),
      ).animate(onPlay: (controller) => controller.repeat())
        .shimmer(duration: 500.ms, color: Colors.red),
    );
  }

  Widget _buildUI() {
    return Positioned(
      top: 20,
      left: 20,
      right: 20,
      child: Column(
        children: [
          // Stage and progress
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              NeonText.body(
                'Stage $_currentStage/3',
                glowColor: _theme.primary,
                fontSize: 18,
              ),
              NeonText.body(
                'Time: $_timeLeft',
                glowColor: _timeLeft <= 30 ? Colors.red : _theme.primary,
                fontSize: 18,
              ),
            ],
          ),
          const SizedBox(height: 16),
          
          // Progress bar
          Container(
            height: 20,
            decoration: BoxDecoration(
              color: _theme.background,
              borderRadius: BorderRadius.circular(10),
              border: Border.all(color: _theme.primary),
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: LinearProgressIndicator(
                value: _currentTaps / _tapsRequired,
                backgroundColor: Colors.transparent,
                valueColor: AlwaysStoppedAnimation<Color>(_theme.accent),
              ),
            ),
          ),
          const SizedBox(height: 8),
          
          NeonText.body(
            'Taps: $_currentTaps / $_tapsRequired',
            glowColor: _theme.primary,
            fontSize: 16,
          ),
        ],
      ),
    );
  }

  double _getBossSize() {
    switch (_currentStage) {
      case 1:
        return 200.0;
      case 2:
        return 150.0;
      case 3:
        return 100.0;
      default:
        return 200.0;
    }
  }
}
