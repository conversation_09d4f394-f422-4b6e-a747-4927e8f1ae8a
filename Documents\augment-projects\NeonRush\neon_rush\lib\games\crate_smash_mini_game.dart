import 'dart:async';
import 'dart:math';
import 'package:flutter/material.dart';
import '../core/sound_manager.dart';
import '../models/neon_theme.dart';
import '../ui/neon_text.dart';
import '../ui/neon_button.dart';
import '../ui/neon_container.dart';
import '../ui/neon_effects.dart';

/// Crate Smash mini-game that appears after level completion
class CrateSmashMiniGame extends StatefulWidget {
  final int rewardTokens;
  final VoidCallback onComplete;
  
  const CrateSmashMiniGame({
    super.key,
    required this.rewardTokens,
    required this.onComplete,
  });

  @override
  State<CrateSmashMiniGame> createState() => _CrateSmashMiniGameState();
}

class _CrateSmashMiniGameState extends State<CrateSmashMiniGame>
    with TickerProviderStateMixin {
  List<CrateData> _crates = [];
  int _tokensEarned = 0;
  bool _gameComplete = false;
  Timer? _gameTimer;
  
  late AnimationController _backgroundController;
  late Animation<double> _backgroundAnimation;
  
  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _initializeCrates();
    _startGame();
  }
  
  void _initializeAnimations() {
    _backgroundController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );

    _backgroundAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(_backgroundController);

    _backgroundController.repeat();
  }
  
  void _initializeCrates() {
    final random = Random();
    // Create 6 crates, only one will contain the reward
    final rewardCrateIndex = random.nextInt(6);
    _crates = List.generate(6, (index) {
      return CrateData(
        id: index,
        position: Offset(
          (index % 3) * 100.0 + 50,
          (index ~/ 3) * 100.0 + 120,
        ),
        tokens: index == rewardCrateIndex ? widget.rewardTokens : 0,
        isSmashed: false,
        color: _getRandomNeonColor(),
      );
    });
  }
  
  Color _getRandomNeonColor() {
    final colors = [
      NeonThemes.electricGreen.primary,
      NeonThemes.cyberBlue.primary,
      NeonThemes.neonPink.primary,
    ];
    return colors[Random().nextInt(colors.length)];
  }
  
  void _startGame() {
    _gameTimer = Timer(const Duration(seconds: 10), () {
      if (!_gameComplete) {
        _endGame();
      }
    });
  }
  
  void _smashCrate(CrateData crate) async {
    if (crate.isSmashed || _gameComplete) return;

    await SoundManager().playSfx(SoundType.buttonClick);

    setState(() {
      crate.isSmashed = true;
      _tokensEarned = crate.tokens; // Only get tokens from the one crate smashed
    });

    // End game immediately after smashing one crate
    _endGame();
  }
  
  void _endGame() {
    setState(() {
      _gameComplete = true;
    });
    _gameTimer?.cancel();

    // Auto-close after showing results
    Timer(const Duration(seconds: 2), () {
      widget.onComplete();
    });
  }
  
  @override
  void dispose() {
    _gameTimer?.cancel();
    _backgroundController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      child: AnimatedBuilder(
        animation: _backgroundAnimation,
        builder: (context, child) {
          return Container(
            width: 400,
            height: 500,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  Colors.black,
                  NeonThemes.electricGreen.primary.withValues(alpha: 0.05 * _backgroundAnimation.value),
                  NeonThemes.cyberBlue.primary.withValues(alpha: 0.05 * _backgroundAnimation.value),
                  Colors.black,
                ],
              ),
              border: Border.all(
                color: NeonThemes.electricGreen.primary,
                width: 2,
              ),
              borderRadius: BorderRadius.circular(16),
              boxShadow: NeonEffects.createGlow(
                color: NeonThemes.electricGreen.primary,
                intensity: 0.8,
              ),
            ),
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                children: [
                  _buildHeader(),
                  const SizedBox(height: 20),
                  Expanded(
                    child: _buildGameArea(),
                  ),
                  _buildFooter(),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
  
  Widget _buildHeader() {
    return Column(
      children: [
        NeonText.title(
          'CRATE SMASH!',
          glowColor: NeonThemes.electricGreen.primary,
          fontSize: 24,
          fontWeight: FontWeight.w900,
        ),
        const SizedBox(height: 8),
        if (!_gameComplete) ...[
          NeonText.body(
            'Pick one crate to smash and claim your reward!',
            glowColor: NeonThemes.cyberBlue.secondary,
            fontSize: 14,
            textAlign: TextAlign.center,
          ),
        ] else ...[
          NeonText.body(
            'Game Complete!',
            glowColor: NeonThemes.electricGreen.primary,
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ],
      ],
    );
  }
  
  Widget _buildGameArea() {
    return Stack(
      children: [
        // Crates grid
        ..._crates.map((crate) => _buildCrate(crate)),
        
        // Tokens earned display
        Positioned(
          top: 10,
          right: 10,
          child: NeonContainer.card(
            glowColor: NeonThemes.electricGreen.primary,
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              child: NeonText.body(
                '🪙 $_tokensEarned',
                glowColor: NeonThemes.electricGreen.primary,
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),
      ],
    );
  }
  
  Widget _buildCrate(CrateData crate) {
    return Positioned(
      left: crate.position.dx,
      top: crate.position.dy,
      child: GestureDetector(
        onTap: () => _smashCrate(crate),
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 200),
          width: 60,
          height: 60,
          decoration: BoxDecoration(
            color: crate.isSmashed 
                ? Colors.transparent 
                : crate.color.withValues(alpha: 0.3),
            border: Border.all(
              color: crate.isSmashed ? Colors.transparent : crate.color,
              width: 2,
            ),
            borderRadius: BorderRadius.circular(8),
            boxShadow: crate.isSmashed ? null : NeonEffects.createGlow(
              color: crate.color,
              intensity: 0.6,
            ),
          ),
          child: crate.isSmashed 
              ? Center(
                  child: Icon(
                    Icons.star,
                    color: NeonThemes.electricGreen.primary,
                    size: 30,
                  ),
                )
              : Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.inventory_2,
                        color: crate.color,
                        size: 24,
                      ),
                      NeonText.body(
                        '${crate.tokens}',
                        glowColor: crate.color,
                        fontSize: 10,
                        fontWeight: FontWeight.bold,
                      ),
                    ],
                  ),
                ),
        ),
      ),
    );
  }
  
  Widget _buildFooter() {
    if (_gameComplete) {
      return Column(
        children: [
          NeonText.body(
            'You earned $_tokensEarned tokens!',
            glowColor: NeonThemes.electricGreen.primary,
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
          const SizedBox(height: 8),
          NeonText.body(
            'Closing automatically...',
            glowColor: NeonThemes.cyberBlue.secondary,
            fontSize: 12,
          ),
        ],
      );
    }
    
    return NeonText.body(
      'Tap all crates before time runs out!',
      glowColor: NeonThemes.cyberBlue.secondary,
      fontSize: 12,
      textAlign: TextAlign.center,
    );
  }
}

/// Data class for crate information
class CrateData {
  final int id;
  final Offset position;
  final int tokens;
  final Color color;
  bool isSmashed;
  
  CrateData({
    required this.id,
    required this.position,
    required this.tokens,
    required this.color,
    this.isSmashed = false,
  });
}
