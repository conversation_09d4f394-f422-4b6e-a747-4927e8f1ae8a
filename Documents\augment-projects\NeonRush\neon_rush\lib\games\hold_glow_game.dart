import 'dart:async';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../core/sound_manager.dart';
import '../core/constants.dart';
import '../models/neon_theme.dart';
import '../models/level_config.dart';
import '../models/level_progression.dart';
import '../game/game_state.dart';
import '../game/power_up_manager.dart';
import '../game/combo_system.dart';
import '../ui/neon_widgets.dart';
import '../ui/power_up_hud.dart';
import '../ui/dynamic_background.dart';
import '../ui/dialogs/level_completion_dialog.dart';

/// Hold the Glow game - Level 3 of new game mechanics
class HoldGlowGame extends StatefulWidget {
  final int levelNumber;
  
  const HoldGlowGame({
    super.key,
    this.levelNumber = 1,
  });

  @override
  State<HoldGlowGame> createState() => _HoldGlowGameState();
}

class _HoldGlowGameState extends State<HoldGlowGame>
    with TickerProviderStateMixin {
  late Timer _gameTimer;
  late AnimationController _backgroundController;
  late AnimationController _glowController;
  late Animation<double> _backgroundAnimation;
  late Animation<double> _glowAnimation;
  
  bool _isHolding = false;
  double _holdProgress = 0.0; // 0.0 to 1.0
  double _timeLeft = 60.0; // 60 seconds
  bool _gameActive = true;
  int _holdStreakCount = 0;
  late PowerUpManager _powerUpManager;
  late ComboSystem _comboSystem;
  late AdaptiveDifficulty _adaptiveDifficulty;
  late HoldGlowLevelConfig _levelConfig;
  
  final double _targetHoldTime = 45.0; // Need to hold for 45 seconds total
  
  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _initializePowerUps();
    _initializeMusic();
    _startGame();
  }

  void _initializeMusic() async {
    // Play random game theme music
    await SoundManager().playRandomGameMusic();
  }
  
  void _initializeAnimations() {
    _backgroundController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    );

    _glowController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _backgroundAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(_backgroundController);

    _glowAnimation = Tween<double>(
      begin: 0.5,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _glowController,
      curve: Curves.easeInOut,
    ));

    _backgroundController.repeat();
    _glowController.repeat(reverse: true);
  }
  
  void _initializePowerUps() {
    _powerUpManager = PowerUpManager();
  }
  
  void _startGame() {
    // Game timer
    _gameTimer = Timer.periodic(const Duration(milliseconds: 100), (timer) {
      if (!_gameActive) {
        timer.cancel();
        return;
      }
      
      setState(() {
        _timeLeft -= 0.1;
        
        if (_isHolding) {
          _holdProgress += 0.1 / _targetHoldTime;
          _holdProgress = _holdProgress.clamp(0.0, 1.0);
          
          // Check for completion
          if (_holdProgress >= 1.0) {
            _endGame();
            return;
          }
        } else {
          // Slowly decrease progress when not holding
          _holdProgress -= 0.05 / _targetHoldTime;
          _holdProgress = _holdProgress.clamp(0.0, 1.0);
        }
        
        if (_timeLeft <= 0) {
          _endGame();
        }
      });
    });
  }
  
  void _onHoldStart() {
    if (!_gameActive) return;
    
    setState(() {
      _isHolding = true;
    });
    
    SoundManager().playSfx(SoundType.buttonClick);
  }
  
  void _onHoldEnd() {
    if (!_gameActive) return;
    
    setState(() {
      _isHolding = false;
    });
  }
  
  void _endGame() {
    setState(() {
      _gameActive = false;
    });
    
    _gameTimer.cancel();
    
    final gameState = context.read<GameStateManager>();
    final isSuccess = _holdProgress >= 1.0;
    
    if (isSuccess) {
      // Calculate rewards
      final timeBonus = (_timeLeft * 5).round();
      final totalXP = 150 + timeBonus;
      final tokensEarned = 35 + (timeBonus ~/ 10);
      
      // Set current level and award rewards
      gameState.startLevel(_createLevelConfig());
      gameState.completeLevel(
        finalScore: (_holdProgress * 100).round(),
        completionTime: Duration(seconds: (60.0 - _timeLeft).round()),
        timeBonus: timeBonus,
      );
      
      // Show completion dialog
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => LevelCompletionDialog(
          level: _createLevelConfig(),
          score: (_holdProgress * 100).round(),
          timeBonus: timeBonus,
          totalXP: totalXP,
          tokensEarned: tokensEarned,
          onReplay: () => _restartGame(),
          onNextLevel: _hasNextLevel() ? () => _goToNextLevel() : null,
          onBackToMenu: () => Navigator.of(context).pop(),
        ),
      );
    } else {
      // Show failure dialog
      _showFailureDialog();
    }
  }
  
  void _restartGame() {
    setState(() {
      _isHolding = false;
      _holdProgress = 0.0;
      _timeLeft = 60.0;
      _gameActive = true;
    });
    _startGame();
  }
  
  bool _hasNextLevel() {
    return widget.levelNumber < 10;
  }
  
  void _goToNextLevel() {
    Navigator.of(context).pushReplacement(
      MaterialPageRoute(
        builder: (context) => HoldGlowGame(levelNumber: widget.levelNumber + 1),
      ),
    );
  }
  
  void _showFailureDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => Dialog(
        backgroundColor: Colors.transparent,
        child: NeonContainer.panel(
          glowColor: Colors.red,
          child: Padding(
            padding: const EdgeInsets.all(24),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                NeonText.title(
                  'TIME\'S UP!',
                  glowColor: Colors.red,
                  fontSize: 24,
                ),
                const SizedBox(height: 16),
                NeonText.body(
                  'Progress: ${(_holdProgress * 100).toStringAsFixed(1)}%',
                  glowColor: Colors.white,
                  fontSize: 16,
                ),
                const SizedBox(height: 24),
                Row(
                  children: [
                    Expanded(
                      child: NeonButton.secondary(
                        text: 'RETRY',
                        glowColor: NeonThemes.electricGreen.primary,
                        onPressed: () {
                          Navigator.of(context).pop();
                          _restartGame();
                        },
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: NeonButton.secondary(
                        text: 'MENU',
                        glowColor: Colors.grey,
                        onPressed: () {
                          Navigator.of(context).pop();
                          Navigator.of(context).pop();
                        },
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
  
  LevelConfig _createLevelConfig() {
    return LevelConfig(
      levelNumber: widget.levelNumber,
      title: 'Hold the Glow Level ${widget.levelNumber}',
      description: 'Hold the glow button to fill the progress bar!',
      mode: GameMode.hold,
      duration: const Duration(seconds: 60),
      goal: 100,
      speed: 1.0,
      theme: NeonThemes.electricGreen,
      difficulty: DifficultyLevel.medium,
      xpReward: 160,
      tokenReward: 35,
      instructions: ['Hold the glow button', 'Fill the progress bar to 100%'],
    );
  }
  
  @override
  void dispose() {
    _gameTimer.cancel();
    _backgroundController.dispose();
    _glowController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: AnimatedBuilder(
        animation: Listenable.merge([_backgroundAnimation, _glowAnimation]),
        builder: (context, child) {
          return Container(
            decoration: BoxDecoration(
              gradient: RadialGradient(
                center: Alignment.center,
                radius: 1.0,
                colors: [
                  NeonThemes.electricGreen.primary.withValues(alpha: 0.1 * _backgroundAnimation.value),
                  Colors.black,
                  Colors.black,
                ],
              ),
            ),
            child: SafeArea(
              child: Stack(
                children: [
                  // Main glow area
                  Center(
                    child: GestureDetector(
                      onTapDown: (_) => _onHoldStart(),
                      onTapUp: (_) => _onHoldEnd(),
                      onTapCancel: () => _onHoldEnd(),
                      child: _buildGlowArea(),
                    ),
                  ),
                  
                  // UI overlay
                  _buildUI(),
                  
                  // Power-up HUD
                  Positioned(
                    top: 80,
                    right: 20,
                    child: ChangeNotifierProvider.value(
                      value: _powerUpManager,
                      child: const PowerUpHUD(),
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
  
  Widget _buildGlowArea() {
    final glowIntensity = _isHolding ? _glowAnimation.value : 0.3;
    final size = 200.0 + (_holdProgress * 100);
    
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        color: NeonThemes.electricGreen.primary.withValues(alpha: 0.2 * glowIntensity),
        shape: BoxShape.circle,
        border: Border.all(
          color: NeonThemes.electricGreen.primary,
          width: 3,
        ),
        boxShadow: [
          BoxShadow(
            color: NeonThemes.electricGreen.primary.withValues(alpha: 0.6 * glowIntensity),
            blurRadius: 30 * glowIntensity,
            spreadRadius: 10 * glowIntensity,
          ),
          BoxShadow(
            color: NeonThemes.electricGreen.primary.withValues(alpha: 0.3 * glowIntensity),
            blurRadius: 60 * glowIntensity,
            spreadRadius: 20 * glowIntensity,
          ),
        ],
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              _isHolding ? Icons.radio_button_checked : Icons.radio_button_unchecked,
              color: Colors.white,
              size: 40,
            ),
            const SizedBox(height: 10),
            NeonText.body(
              _isHolding ? 'HOLDING' : 'HOLD ME',
              glowColor: NeonThemes.electricGreen.primary,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildUI() {
    return Column(
      children: [
        // Header
        Padding(
          padding: const EdgeInsets.all(20),
          child: Row(
            children: [
              NeonCircularButton(
                icon: Icons.arrow_back,
                glowColor: NeonThemes.cyberBlue.primary,
                onPressed: () => Navigator.of(context).pop(),
              ),
              const SizedBox(width: 20),
              Expanded(
                child: NeonText.title(
                  'HOLD THE GLOW - LEVEL ${widget.levelNumber}',
                  glowColor: NeonThemes.electricGreen.primary,
                  fontSize: 18,
                ),
              ),
            ],
          ),
        ),
        
        // Progress and time
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20),
          child: Column(
            children: [
              // Progress bar
              NeonContainer.card(
                glowColor: NeonThemes.electricGreen.primary,
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    children: [
                      NeonText.body(
                        'Progress: ${(_holdProgress * 100).toStringAsFixed(1)}%',
                        glowColor: NeonThemes.electricGreen.primary,
                        fontSize: 16,
                      ),
                      const SizedBox(height: 8),
                      LinearProgressIndicator(
                        value: _holdProgress,
                        backgroundColor: Colors.grey.withValues(alpha: 0.3),
                        valueColor: AlwaysStoppedAnimation<Color>(
                          NeonThemes.electricGreen.primary,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              
              const SizedBox(height: 16),
              
              // Time remaining
              NeonContainer.card(
                glowColor: _timeLeft <= 10 ? Colors.red : NeonThemes.cyberBlue.primary,
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  child: NeonText.body(
                    'Time: ${_timeLeft.toStringAsFixed(1)}s',
                    glowColor: _timeLeft <= 10 ? Colors.red : NeonThemes.cyberBlue.primary,
                    fontSize: 16,
                  ),
                ),
              ),
            ],
          ),
        ),
        
        const Spacer(),
        
        // Instructions
        Padding(
          padding: const EdgeInsets.all(20),
          child: NeonText.body(
            'Hold the glow for ${_targetHoldTime.toInt()} seconds total!',
            glowColor: Colors.white,
            fontSize: 14,
            textAlign: TextAlign.center,
          ),
        ),
      ],
    );
  }
}


