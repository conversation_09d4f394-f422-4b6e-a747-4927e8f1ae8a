import 'dart:async';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../core/sound_manager.dart';
import '../core/constants.dart';
import '../models/neon_theme.dart';
import '../models/level_config.dart';
import '../models/level_progression.dart';
import '../game/game_state.dart';
import '../game/power_up_manager.dart';
import '../game/combo_system.dart';
import '../ui/neon_widgets.dart';
import '../ui/power_up_hud.dart';
import '../ui/dynamic_background.dart';
import '../ui/particle_system.dart';
import '../ui/dialogs/level_completion_dialog.dart';

/// Swipe to Avoid game - Level 2 of new game mechanics
class SwipeAvoidGame extends StatefulWidget {
  final int levelNumber;
  
  const SwipeAvoidGame({
    super.key,
    this.levelNumber = 1,
  });

  @override
  State<SwipeAvoidGame> createState() => _SwipeAvoidGameState();
}

class _SwipeAvoidGameState extends State<SwipeAvoidGame>
    with TickerProviderStateMixin {
  late Timer _gameTimer;
  late Timer _spawnTimer;
  late AnimationController _backgroundController;
  late Animation<double> _backgroundAnimation;
  
  List<Obstacle> _obstacles = [];
  final List<Widget> _particleEffects = [];
  Offset _playerPosition = const Offset(0.5, 0.8); // Center bottom
  int _score = 0;
  int _timeLeft = 45; // 45 seconds
  bool _gameActive = true;
  int _dodgeCount = 0;
  late PowerUpManager _powerUpManager;
  late ComboSystem _comboSystem;
  late AdaptiveDifficulty _adaptiveDifficulty;
  late SwipeAvoidLevelConfig _levelConfig;
  
  @override
  void initState() {
    super.initState();
    _initializeLevel();
    _initializeAnimations();
    _initializePowerUps();
    _initializeGameSystems();
    _initializeMusic();
    _startGame();
  }

  void _initializeMusic() async {
    // Play random game theme music
    await SoundManager().playRandomGameMusic();
  }

  void _initializeLevel() {
    _levelConfig = LevelProgression.getSwipeAvoidConfig(widget.levelNumber);
    _timeLeft = _levelConfig.duration;
  }

  void _initializeGameSystems() {
    _comboSystem = ComboSystem(
      comboWindow: const Duration(milliseconds: 1500),
      maxComboLevel: 15,
      baseMultiplier: 1.0,
      multiplierIncrement: 0.15,
    );

    _adaptiveDifficulty = AdaptiveDifficulty(
      targetPerformance: 0.8, // Higher target for survival game
      adjustmentRate: 0.08,
    );
  }
  
  void _initializeAnimations() {
    _backgroundController = AnimationController(
      duration: const Duration(seconds: 4),
      vsync: this,
    );

    _backgroundAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(_backgroundController);

    _backgroundController.repeat();
  }
  
  void _initializePowerUps() {
    _powerUpManager = PowerUpManager();
  }
  
  void _startGame() {
    // Game timer
    _gameTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (!_gameActive) {
        timer.cancel();
        return;
      }
      
      setState(() {
        _timeLeft--;
        if (_timeLeft <= 0) {
          _endGame();
        }
      });
    });
    
    // Obstacle spawn timer with adaptive difficulty
    final baseInterval = _levelConfig.spawnInterval;
    final adaptiveInterval = Duration(
      milliseconds: (baseInterval.inMilliseconds / _adaptiveDifficulty.adjustSpawnRate(1.0)).round(),
    );

    _spawnTimer = Timer.periodic(adaptiveInterval, (timer) {
      if (!_gameActive) {
        timer.cancel();
        return;
      }
      _spawnObstacle();
    });
    
    // Update obstacles position
    Timer.periodic(const Duration(milliseconds: 50), (timer) {
      if (!_gameActive) {
        timer.cancel();
        return;
      }
      _updateObstacles();
    });
  }
  
  void _spawnObstacle() {
    final random = Random();
    
    final obstacle = Obstacle(
      id: DateTime.now().millisecondsSinceEpoch,
      position: Offset(random.nextDouble(), -0.1), // Start from top
      velocity: 0.008 + (random.nextDouble() * 0.004), // Random speed
      color: _getRandomNeonColor(),
      size: 40.0 + (random.nextDouble() * 20), // Random size
    );
    
    setState(() {
      _obstacles.add(obstacle);
    });
  }
  
  Color _getRandomNeonColor() {
    final colors = [
      NeonThemes.neonPink.primary,
      NeonThemes.fireOrange.primary,
      Colors.red,
    ];
    return colors[Random().nextInt(colors.length)];
  }
  
  void _updateObstacles() {
    setState(() {
      // Move obstacles down with level speed multiplier
      final speedMultiplier = _adaptiveDifficulty.adjustSpeed(_levelConfig.speedMultiplier);
      for (var obstacle in _obstacles) {
        obstacle.position = Offset(
          obstacle.position.dx,
          obstacle.position.dy + (obstacle.velocity * speedMultiplier),
        );
      }
      
      // Remove obstacles that are off screen and register successful dodges
      final removedObstacles = _obstacles.where((obstacle) => obstacle.position.dy > 1.1).toList();
      for (var obstacle in removedObstacles) {
        _dodgeCount++;
        _comboSystem.registerSuccess();

        // Add particle effect for successful dodge
        _particleEffects.add(
          ParticleEffects.gameObjectTrail(
            Offset(_playerPosition.dx * MediaQuery.of(context).size.width,
                   _playerPosition.dy * MediaQuery.of(context).size.height),
            NeonThemes.electricGreen.primary,
          ),
        );
      }
      _obstacles.removeWhere((obstacle) => obstacle.position.dy > 1.1);
      
      // Check for collisions
      _checkCollisions();
    });
  }
  
  void _checkCollisions() {
    final playerRadius = 30.0;
    
    for (var obstacle in _obstacles) {
      final screenSize = MediaQuery.of(context).size;
      final obstacleScreenPos = Offset(
        obstacle.position.dx * screenSize.width,
        obstacle.position.dy * screenSize.height,
      );
      final playerScreenPos = Offset(
        _playerPosition.dx * screenSize.width,
        _playerPosition.dy * screenSize.height,
      );
      
      final distance = (obstacleScreenPos - playerScreenPos).distance;
      
      if (distance < (playerRadius + obstacle.size / 2)) {
        // Collision detected - break combo and end game
        _comboSystem.registerFailure();
        _endGame();
        return;
      }
    }
  }
  
  void _onPanUpdate(DragUpdateDetails details) {
    if (!_gameActive) return;
    
    final screenSize = MediaQuery.of(context).size;
    final newPosition = Offset(
      (_playerPosition.dx * screenSize.width + details.delta.dx) / screenSize.width,
      (_playerPosition.dy * screenSize.height + details.delta.dy) / screenSize.height,
    );
    
    setState(() {
      _playerPosition = Offset(
        newPosition.dx.clamp(0.1, 0.9),
        newPosition.dy.clamp(0.1, 0.9),
      );
    });
  }
  
  void _endGame() {
    setState(() {
      _gameActive = false;
    });
    
    _gameTimer.cancel();
    _spawnTimer.cancel();
    
    final gameState = context.read<GameStateManager>();
    final isSuccess = _timeLeft <= 0; // Success if survived the full time
    
    if (isSuccess) {
      // Calculate rewards
      final timeBonus = _obstacles.length * 2; // Bonus for obstacles avoided
      final totalXP = 120 + timeBonus;
      final tokensEarned = 30 + (_obstacles.length ~/ 5);
      
      // Set current level and award rewards
      gameState.startLevel(_createLevelConfig());
      gameState.completeLevel(
        finalScore: _score,
        completionTime: Duration(seconds: 45 - _timeLeft),
        timeBonus: timeBonus,
      );
      
      // Show completion dialog
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => LevelCompletionDialog(
          level: _createLevelConfig(),
          score: _score,
          timeBonus: timeBonus,
          totalXP: totalXP,
          tokensEarned: tokensEarned,
          onReplay: () => _restartGame(),
          onNextLevel: _hasNextLevel() ? () => _goToNextLevel() : null,
          onBackToMenu: () => Navigator.of(context).pop(),
        ),
      );
    } else {
      // Show failure dialog
      _showFailureDialog();
    }
  }
  
  void _restartGame() {
    setState(() {
      _obstacles.clear();
      _playerPosition = const Offset(0.5, 0.8);
      _score = 0;
      _timeLeft = 45;
      _gameActive = true;
    });
    _startGame();
  }
  
  bool _hasNextLevel() {
    return widget.levelNumber < 10;
  }
  
  void _goToNextLevel() {
    Navigator.of(context).pushReplacement(
      MaterialPageRoute(
        builder: (context) => SwipeAvoidGame(levelNumber: widget.levelNumber + 1),
      ),
    );
  }
  
  void _showFailureDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => Dialog(
        backgroundColor: Colors.transparent,
        child: NeonContainer.panel(
          glowColor: Colors.red,
          child: Padding(
            padding: const EdgeInsets.all(24),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                NeonText.title(
                  'COLLISION!',
                  glowColor: Colors.red,
                  fontSize: 24,
                ),
                const SizedBox(height: 16),
                NeonText.body(
                  'You hit an obstacle!',
                  glowColor: Colors.white,
                  fontSize: 16,
                ),
                const SizedBox(height: 24),
                Row(
                  children: [
                    Expanded(
                      child: NeonButton.secondary(
                        text: 'RETRY',
                        glowColor: NeonThemes.neonPink.primary,
                        onPressed: () {
                          Navigator.of(context).pop();
                          _restartGame();
                        },
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: NeonButton.secondary(
                        text: 'MENU',
                        glowColor: Colors.grey,
                        onPressed: () {
                          Navigator.of(context).pop();
                          Navigator.of(context).pop();
                        },
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
  
  LevelConfig _createLevelConfig() {
    return LevelConfig(
      levelNumber: widget.levelNumber,
      title: 'Swipe to Avoid Level ${widget.levelNumber}',
      description: 'Drag to avoid the moving obstacles!',
      mode: GameMode.swipe,
      duration: const Duration(seconds: 45),
      goal: 100,
      speed: 1.0,
      theme: NeonThemes.neonPink,
      difficulty: DifficultyLevel.medium,
      xpReward: 150,
      tokenReward: 30,
      instructions: ['Drag to move your player', 'Avoid the obstacles'],
    );
  }
  
  @override
  void dispose() {
    _gameTimer.cancel();
    _spawnTimer.cancel();
    _backgroundController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: DynamicNeonBackground(
        theme: NeonThemes.neonPink,
        intensity: 0.6,
        child: SafeArea(
          child: GestureDetector(
            onPanUpdate: _onPanUpdate,
            child: Stack(
              children: [
                // Obstacles
                ..._obstacles.map((obstacle) => _buildObstacle(obstacle)),

                // Particle effects
                ..._particleEffects,

                // Player
                _buildPlayer(),

                // UI overlay
                _buildUI(),

                // Combo display
                _buildComboDisplay(),

                // Power-up HUD
                Positioned(
                  top: 80,
                  right: 20,
                  child: ChangeNotifierProvider.value(
                    value: _powerUpManager,
                    child: const PowerUpHUD(),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
  
  Widget _buildComboDisplay() {
    if (!_comboSystem.hasActiveCombo) return const SizedBox.shrink();

    return Positioned(
      top: 120,
      left: 20,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: _comboSystem.comboTier.color.withValues(alpha: 0.2),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: _comboSystem.comboTier.color,
            width: 2,
          ),
          boxShadow: [
            BoxShadow(
              color: _comboSystem.comboTier.color.withValues(alpha: 0.5),
              blurRadius: 10,
              spreadRadius: 2,
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'DODGES',
              style: TextStyle(
                color: _comboSystem.comboTier.color,
                fontSize: 12,
                fontWeight: FontWeight.bold,
                shadows: [
                  Shadow(
                    color: _comboSystem.comboTier.color,
                    blurRadius: 5,
                  ),
                ],
              ),
            ),
            Text(
              '${_comboSystem.currentCombo}x',
              style: TextStyle(
                color: _comboSystem.comboTier.color,
                fontSize: 20,
                fontWeight: FontWeight.bold,
                shadows: [
                  Shadow(
                    color: _comboSystem.comboTier.color,
                    blurRadius: 8,
                  ),
                ],
              ),
            ),
            if (_comboSystem.comboTier != ComboTier.none)
              Text(
                _comboSystem.comboTier.name,
                style: TextStyle(
                  color: _comboSystem.comboTier.color,
                  fontSize: 10,
                  fontWeight: FontWeight.bold,
                  shadows: [
                    Shadow(
                      color: _comboSystem.comboTier.color,
                      blurRadius: 3,
                    ),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildObstacle(Obstacle obstacle) {
    final screenSize = MediaQuery.of(context).size;
    return Positioned(
      left: obstacle.position.dx * screenSize.width - obstacle.size / 2,
      top: obstacle.position.dy * screenSize.height - obstacle.size / 2,
      child: Container(
        width: obstacle.size,
        height: obstacle.size,
        decoration: BoxDecoration(
          color: obstacle.color.withValues(alpha: 0.3),
          shape: BoxShape.circle,
          border: Border.all(color: obstacle.color, width: 2),
          boxShadow: NeonEffects.createGlow(
            color: obstacle.color,
            intensity: 0.8,
          ),
        ),
      ),
    );
  }
  
  Widget _buildPlayer() {
    final screenSize = MediaQuery.of(context).size;
    return Positioned(
      left: _playerPosition.dx * screenSize.width - 30,
      top: _playerPosition.dy * screenSize.height - 30,
      child: RepaintBoundary(
        child: Container(
          width: 60,
          height: 60,
          decoration: BoxDecoration(
            color: NeonThemes.cyberBlue.primary.withValues(alpha: 0.5),
            shape: BoxShape.circle,
            border: Border.all(color: NeonThemes.cyberBlue.primary, width: 3),
            boxShadow: NeonEffects.createGlow(
              color: NeonThemes.cyberBlue.primary,
              intensity: 1.2,
            ),
          ),
          child: const Icon(
            Icons.person,
            color: Colors.white,
            size: 30,
          ),
        ),
      ),
    );
  }
  
  Widget _buildUI() {
    return Column(
      children: [
        // Header
        Padding(
          padding: const EdgeInsets.all(20),
          child: Row(
            children: [
              NeonCircularButton(
                icon: Icons.arrow_back,
                glowColor: NeonThemes.cyberBlue.primary,
                onPressed: () => Navigator.of(context).pop(),
              ),
              const SizedBox(width: 20),
              Expanded(
                child: NeonText.title(
                  'SWIPE TO AVOID - LEVEL ${widget.levelNumber}',
                  glowColor: NeonThemes.neonPink.primary,
                  fontSize: 18,
                ),
              ),
            ],
          ),
        ),
        
        // Time remaining
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20),
          child: NeonContainer.card(
            glowColor: _timeLeft <= 10 ? Colors.red : NeonThemes.cyberBlue.primary,
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: NeonText.body(
                'Time: $_timeLeft',
                glowColor: _timeLeft <= 10 ? Colors.red : NeonThemes.cyberBlue.primary,
                fontSize: 16,
              ),
            ),
          ),
        ),
        
        const Spacer(),
        
        // Instructions
        Padding(
          padding: const EdgeInsets.all(20),
          child: NeonText.body(
            'Drag to move • Avoid the obstacles!',
            glowColor: Colors.white,
            fontSize: 14,
            textAlign: TextAlign.center,
          ),
        ),
      ],
    );
  }
}

/// Obstacle data class
class Obstacle {
  int id;
  Offset position;
  double velocity;
  Color color;
  double size;
  
  Obstacle({
    required this.id,
    required this.position,
    required this.velocity,
    required this.color,
    required this.size,
  });
}


