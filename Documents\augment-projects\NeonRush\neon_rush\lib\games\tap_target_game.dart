import 'dart:async';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../core/sound_manager.dart';
import '../core/constants.dart';
import '../models/neon_theme.dart';
import '../models/level_config.dart';
import '../models/level_progression.dart';
import '../game/game_state.dart';
import '../game/power_up_manager.dart';
import '../game/combo_system.dart';
import '../ui/neon_widgets.dart';
import '../ui/power_up_hud.dart';
import '../ui/dynamic_background.dart';
import '../ui/particle_system.dart';
import '../ui/dialogs/level_completion_dialog.dart';
import '../utils/performance_optimizer.dart';
import '../utils/haptic_feedback.dart';

/// Tap Target game - Level 1 of new game mechanics
class TapTargetGame extends StatefulWidget {
  final int levelNumber;
  
  const TapTargetGame({
    super.key,
    this.levelNumber = 1,
  });

  @override
  State<TapTargetGame> createState() => _TapTargetGameState();
}

class _TapTargetGameState extends State<TapTargetGame>
    with TickerProviderStateMixin {
  late Timer _gameTimer;
  late Timer _spawnTimer;
  late AnimationController _backgroundController;
  late Animation<double> _backgroundAnimation;
  
  List<Target> _targets = [];
  final List<Widget> _particleEffects = [];
  int _score = 0;
  int _timeLeft = 30; // 30 seconds
  bool _gameActive = true;
  int _nextTargetId = 0;
  late PowerUpManager _powerUpManager;
  late ComboSystem _comboSystem;
  late AdaptiveDifficulty _adaptiveDifficulty;
  late TapTargetLevelConfig _levelConfig;
  late ObjectPool<Target> _targetPool;
  late Timer _targetUpdateTimer; // For moving targets

  /// Get theme based on level number
  NeonTheme _getLevelTheme() {
    final themes = [
      NeonThemes.electricGreen,  // Levels 1-10
      NeonThemes.cyberBlue,      // Levels 11-20
      NeonThemes.neonPink,       // Levels 21-30
      NeonThemes.fireOrange,     // Levels 31-40
      NeonThemes.purpleHaze,     // Levels 41-50
      NeonThemes.goldRush,       // Levels 51-60
      NeonThemes.iceBlue,        // Levels 61-70
      NeonThemes.crimsonRed,     // Levels 71-80
      NeonThemes.silverStorm,    // Levels 81-90
      NeonThemes.rainbow,        // Levels 91-100
    ];

    final themeIndex = ((widget.levelNumber - 1) ~/ 10).clamp(0, themes.length - 1);
    return themes[themeIndex];
  }

  @override
  void initState() {
    super.initState();
    _initializeLevel();
    _initializeAnimations();
    _initializePowerUps();
    _initializeGameSystems();
    _initializeMusic();
    _startGame();
  }

  void _initializeMusic() async {
    // Play random game theme music
    await SoundManager().playRandomGameMusic();
  }

  void _initializeLevel() {
    _levelConfig = LevelProgression.getTapTargetConfig(widget.levelNumber);
    _timeLeft = _levelConfig.duration;
  }

  void _initializeGameSystems() {
    _comboSystem = ComboSystem(
      comboWindow: const Duration(seconds: 1),
      maxComboLevel: 10,
      baseMultiplier: 1.0,
      multiplierIncrement: 0.2,
    );

    _adaptiveDifficulty = AdaptiveDifficulty(
      targetPerformance: 0.75,
      adjustmentRate: 0.1,
    );

    // Initialize object pool for targets
    _targetPool = ObjectPool<Target>(
      factory: () => Target(
        id: 0,
        position: Offset.zero,
        color: Colors.white,
        points: 0,
      ),
      reset: (target) {
        // Reset target properties when returned to pool
      },
      maxSize: 20,
    );
  }
  
  void _initializeAnimations() {
    _backgroundController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    );

    _backgroundAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(_backgroundController);

    _backgroundController.repeat();
  }
  
  void _initializePowerUps() {
    _powerUpManager = PowerUpManager();
  }
  
  void _startGame() {
    // Game timer
    _gameTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (!_gameActive) {
        timer.cancel();
        return;
      }

      setState(() {
        _timeLeft--;
        if (_timeLeft <= 0) {
          _endGame();
        }
      });
    });

    // Target spawn timer - faster spawn rate
    _spawnTimer = Timer.periodic(const Duration(milliseconds: 800), (timer) {
      if (!_gameActive) {
        timer.cancel();
        return;
      }
      _spawnTarget();

      // Check for multiball power-up and spawn hard balls
      if (_powerUpManager.isMultiballActive()) {
        final random = Random();
        // 30% chance to spawn hard ball when multiball is active
        if (random.nextDouble() < 0.3) {
          _spawnHardBall();
        }
      }
    });

    // Target movement timer
    _targetUpdateTimer = Timer.periodic(const Duration(milliseconds: 16), (timer) {
      if (!_gameActive) {
        timer.cancel();
        return;
      }
      _updateTargets();
    });
  }
  
  void _spawnTarget() {
    final random = Random();
    final screenSize = MediaQuery.of(context).size;

    // Spawn from top of screen
    final x = random.nextDouble() * (screenSize.width - 120) + 60;
    final y = -80.0; // Start above screen

    // 20% chance for red target (penalty)
    final isRed = random.nextDouble() < 0.2;

    int points;
    Color targetColor;

    if (isRed) {
      points = -15; // Red targets give -15 points
      targetColor = Colors.red;
    } else {
      // Use level config for point ranges (now 15 points for normal targets)
      points = 15;
      targetColor = _getLevelTheme().primary;
    }

    final target = Target(
      id: _nextTargetId++,
      position: Offset(x, y),
      color: targetColor,
      points: points,
      isRed: isRed,
      fallSpeed: 150.0 + (widget.levelNumber * 10), // Faster as levels progress
    );

    setState(() {
      _targets.add(target);
    });

    // Remove target after 2 seconds if not tapped (faster timeout)
    Timer(const Duration(seconds: 2), () {
      setState(() {
        _targets.removeWhere((t) => t.id == target.id);
      });
    });
  }

  void _spawnSplitBalls(Target hardBall) {
    final random = Random();
    final screenSize = MediaQuery.of(context).size;

    // Spawn split balls in different directions from the hard ball position
    for (int i = 0; i < hardBall.splitCount; i++) {
      // Calculate spread angle for each split ball
      final angle = (i * 2 * pi / hardBall.splitCount) + (random.nextDouble() * 0.5 - 0.25);
      final distance = 80.0 + random.nextDouble() * 40.0; // Random distance from center

      final splitX = (hardBall.position.dx + cos(angle) * distance).clamp(30.0, screenSize.width - 30.0);
      final splitY = (hardBall.position.dy + sin(angle) * distance).clamp(100.0, screenSize.height * 0.7);

      final splitBall = Target(
        id: _nextTargetId++,
        position: Offset(splitX, splitY),
        color: hardBall.color.withValues(alpha: 0.8), // Slightly transparent
        points: hardBall.splitScore,
        isRed: false,
        fallSpeed: hardBall.fallSpeed * 1.5, // Faster than original
        size: 40.0, // Smaller than hard ball
      );

      _targets.add(splitBall);

      // Remove split ball after shorter time (they're faster)
      Timer(const Duration(milliseconds: 1500), () {
        setState(() {
          _targets.removeWhere((t) => t.id == splitBall.id);
        });
      });
    }
  }

  void _spawnHardBall() {
    final random = Random();
    final screenSize = MediaQuery.of(context).size;

    // Spawn hard ball from top of screen
    final x = random.nextDouble() * (screenSize.width - 120) + 60;
    final y = -80.0; // Start above screen

    final hardBall = Target(
      id: _nextTargetId++,
      position: Offset(x, y),
      color: const Color(0xFFFF6B35), // Orange color for hard ball
      points: 10, // Initial score for hard ball
      isRed: false,
      fallSpeed: 120.0, // Slightly slower than normal targets
      isHardBall: true,
      splitCount: 4,
      splitScore: 20,
      size: 80.0, // Larger than normal targets
    );

    setState(() {
      _targets.add(hardBall);
    });

    // Remove hard ball after longer time (it's more valuable)
    Timer(const Duration(seconds: 4), () {
      setState(() {
        _targets.removeWhere((t) => t.id == hardBall.id);
      });
    });
  }

  void _updateTargets() {
    if (!_gameActive) return;

    final screenHeight = MediaQuery.of(context).size.height;
    final deltaTime = 16 / 1000.0; // 16ms in seconds

    setState(() {
      // Update target positions
      for (int i = _targets.length - 1; i >= 0; i--) {
        final target = _targets[i];
        final newY = target.position.dy + (target.fallSpeed * deltaTime);

        if (newY > screenHeight + 100) {
          // Target fell off screen, remove it
          _targets.removeAt(i);
        } else {
          // Update position
          _targets[i] = target.copyWith(
            position: Offset(target.position.dx, newY),
          );
        }
      }
    });
  }

  void _onBackgroundTapWithPosition(TapDownDetails details) {
    if (!_gameActive) return;

    // Penalty for tapping background
    setState(() {
      _score = (_score - 15).clamp(0, double.infinity).toInt();

      // Add particle effect at tap position
      _particleEffects.add(
        ParticleEffects.backgroundTapEffect(details.localPosition),
      );
    });

    // Remove particle effect after animation
    Timer(const Duration(milliseconds: 1000), () {
      if (mounted && _particleEffects.isNotEmpty) {
        setState(() {
          _particleEffects.removeAt(0);
        });
      }
    });

    // Haptic feedback for penalty
    NeonHaptics.gameOver();
  }
  
  void _tapTarget(Target target) async {
    if (!_gameActive) return;

    await SoundManager().playSfx(SoundType.buttonClick);

    // Haptic feedback for target hit
    await NeonHaptics.targetHit();

    // Register successful hit with combo system
    _comboSystem.registerSuccess();

    // Check for combo achievement
    if (_comboSystem.currentCombo > 0 && _comboSystem.currentCombo % 5 == 0) {
      await NeonHaptics.comboAchieved();
    }

    // Calculate points with combo multiplier
    final basePoints = target.points;
    final comboPoints = _comboSystem.getComboPoints(basePoints);

    setState(() {
      _score += comboPoints;
      _targets.removeWhere((t) => t.id == target.id);

      // Handle hard ball splitting
      if (target.isHardBall && target.splitCount > 0) {
        _spawnSplitBalls(target);
      }

      // Add particle effect at target position - special effect for different target types
      if (target.isRed) {
        _particleEffects.add(
          ParticleEffects.redTargetExplosion(target.position),
        );
      } else if (target.isHardBall) {
        _particleEffects.add(
          ParticleEffects.targetHitEffect(target.position, target.color),
        );
      } else {
        _particleEffects.add(
          ParticleEffects.targetHitEffect(target.position, target.color),
        );
      }
    });

    // Remove particle effect after animation
    Timer(const Duration(seconds: 2), () {
      if (mounted && _particleEffects.isNotEmpty) {
        setState(() {
          _particleEffects.removeAt(0);
        });
      }
    });

    // Check for level completion using dynamic goal
    final goal = _levelConfig.goal;
    if (_score >= goal) {
      _endGame();
    }
  }
  
  void _endGame() {
    setState(() {
      _gameActive = false;
    });

    _gameTimer.cancel();
    _spawnTimer.cancel();
    _targetUpdateTimer.cancel();

    final gameState = context.read<GameStateManager>();
    final goal = _levelConfig.goal;
    final isSuccess = _score >= goal;

    // Record performance for adaptive difficulty
    final performance = isSuccess ? (_score / goal).clamp(0.0, 1.0) : 0.0;
    _adaptiveDifficulty.recordPerformance(performance);

    if (isSuccess) {
      // Haptic feedback for level completion
      NeonHaptics.levelComplete();

      // Calculate enhanced rewards based on level config
      final rewardConfig = LevelProgression.getRewardConfig(widget.levelNumber);
      final timeEfficiency = _timeLeft / _levelConfig.duration;
      final comboBonus = (_comboSystem.maxCombo * 10).clamp(0, 100);

      final timeBonus = timeEfficiency > 0.9 ? rewardConfig.timeBonusXP : 0;
      final totalXP = rewardConfig.baseXP + timeBonus + comboBonus;
      final tokensEarned = rewardConfig.baseTokens + (_comboSystem.maxCombo ~/ 3);
      
      // Set current level and award rewards
      gameState.startLevel(_createLevelConfig());
      gameState.completeLevel(
        finalScore: _score,
        completionTime: Duration(seconds: 30 - _timeLeft),
        timeBonus: timeBonus,
      );
      
      // Show completion dialog
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => LevelCompletionDialog(
          level: _createLevelConfig(),
          score: _score,
          timeBonus: timeBonus,
          totalXP: totalXP,
          tokensEarned: tokensEarned,
          onReplay: () => _restartGame(),
          onNextLevel: _hasNextLevel() ? () => _goToNextLevel() : null,
          onBackToMenu: () => Navigator.of(context).pop(),
        ),
      );
    } else {
      // Haptic feedback for game over
      NeonHaptics.gameOver();

      // Show failure dialog
      _showFailureDialog();
    }
  }
  
  void _restartGame() {
    setState(() {
      _targets.clear();
      _score = 0;
      _timeLeft = 30;
      _gameActive = true;
    });
    _startGame();
  }
  
  bool _hasNextLevel() {
    return widget.levelNumber < 10; // 10 levels per game
  }
  
  void _goToNextLevel() {
    Navigator.of(context).pushReplacement(
      MaterialPageRoute(
        builder: (context) => TapTargetGame(levelNumber: widget.levelNumber + 1),
      ),
    );
  }
  
  void _showFailureDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => Dialog(
        backgroundColor: Colors.transparent,
        child: NeonContainer.panel(
          glowColor: Colors.red,
          child: Padding(
            padding: const EdgeInsets.all(24),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                NeonText.title(
                  'LEVEL FAILED',
                  glowColor: Colors.red,
                  fontSize: 24,
                ),
                const SizedBox(height: 16),
                NeonText.body(
                  'Score: $_score / ${_levelConfig.goal}',
                  glowColor: Colors.white,
                  fontSize: 16,
                ),
                const SizedBox(height: 24),
                Row(
                  children: [
                    Expanded(
                      child: NeonButton.secondary(
                        text: 'RETRY',
                        glowColor: _getLevelTheme().primary,
                        onPressed: () {
                          Navigator.of(context).pop();
                          _restartGame();
                        },
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: NeonButton.secondary(
                        text: 'MENU',
                        glowColor: Colors.grey,
                        onPressed: () {
                          Navigator.of(context).pop();
                          Navigator.of(context).pop();
                        },
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
  
  // Create a level config for the completion dialog
  LevelConfig _createLevelConfig() {
    return LevelConfig(
      levelNumber: widget.levelNumber,
      title: 'Tap Target Level ${widget.levelNumber}',
      description: 'Tap the glowing targets to score points!',
      mode: GameMode.tap,
      duration: Duration(seconds: _levelConfig.duration),
      goal: _levelConfig.goal,
      speed: 1.0,
      theme: _getLevelTheme(),
      difficulty: DifficultyLevel.easy,
      xpReward: 100 + (widget.levelNumber * 25),
      tokenReward: 25 + (widget.levelNumber * 10),
      instructions: ['Tap the glowing targets', 'Score ${_levelConfig.goal} points to win'],
    );
  }
  
  @override
  void dispose() {
    _gameTimer.cancel();
    _spawnTimer.cancel();
    _targetUpdateTimer.cancel();
    _backgroundController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: DynamicNeonBackground(
        theme: _getLevelTheme(),
        intensity: 0.8,
        child: SafeArea(
          child: Stack(
            children: [
              // Background tap area (transparent overlay for penalty taps)
              Positioned.fill(
                top: 140, // Start below the UI elements
                child: GestureDetector(
                  onTapDown: _onBackgroundTapWithPosition,
                  behavior: HitTestBehavior.translucent,
                  child: Container(
                    color: Colors.transparent,
                  ),
                ),
              ),

              // Game area with targets
              ..._targets.map((target) => _buildTarget(target)),

              // Particle effects
              ..._particleEffects,

              // UI overlay (positioned above the tap area)
              _buildUI(),

              // Combo display
              _buildComboDisplay(),

              // Power-up HUD
              Positioned(
                top: 80,
                right: 20,
                child: ChangeNotifierProvider.value(
                  value: _powerUpManager,
                  child: const PowerUpHUD(),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
  
  Widget _buildComboDisplay() {
    if (!_comboSystem.hasActiveCombo) return const SizedBox.shrink();

    return Positioned(
      top: 120,
      left: 20,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: _comboSystem.comboTier.color.withValues(alpha: 0.2),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: _comboSystem.comboTier.color,
            width: 2,
          ),
          boxShadow: [
            BoxShadow(
              color: _comboSystem.comboTier.color.withValues(alpha: 0.5),
              blurRadius: 10,
              spreadRadius: 2,
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'COMBO',
              style: TextStyle(
                color: _comboSystem.comboTier.color,
                fontSize: 12,
                fontWeight: FontWeight.bold,
                shadows: [
                  Shadow(
                    color: _comboSystem.comboTier.color,
                    blurRadius: 5,
                  ),
                ],
              ),
            ),
            Text(
              '${_comboSystem.currentCombo}x',
              style: TextStyle(
                color: _comboSystem.comboTier.color,
                fontSize: 20,
                fontWeight: FontWeight.bold,
                shadows: [
                  Shadow(
                    color: _comboSystem.comboTier.color,
                    blurRadius: 8,
                  ),
                ],
              ),
            ),
            if (_comboSystem.comboTier != ComboTier.none)
              Text(
                _comboSystem.comboTier.name,
                style: TextStyle(
                  color: _comboSystem.comboTier.color,
                  fontSize: 10,
                  fontWeight: FontWeight.bold,
                  shadows: [
                    Shadow(
                      color: _comboSystem.comboTier.color,
                      blurRadius: 3,
                    ),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildTarget(Target target) {
    final targetSize = target.size;
    final containerSize = targetSize + 20; // Add padding for tap area

    return Positioned(
      left: target.position.dx - (containerSize / 2),
      top: target.position.dy - (containerSize / 2),
      child: RepaintBoundary(
        child: GestureDetector(
          onTap: () => _tapTarget(target),
          behavior: HitTestBehavior.opaque, // Ensure targets intercept taps
          child: Container(
            width: containerSize,
            height: containerSize,
            alignment: Alignment.center,
            // Add a higher z-index to ensure targets are above the background tap area
            child: Container(
              width: targetSize,
              height: targetSize,
              decoration: BoxDecoration(
                color: target.color.withValues(alpha: target.isHardBall ? 0.5 : 0.3),
                shape: BoxShape.circle,
                border: Border.all(
                  color: target.color,
                  width: target.isHardBall ? 4 : (target.isRed ? 3 : 2), // Thicker border for hard balls and red targets
                ),
                boxShadow: NeonEffects.createGlow(
                  color: target.color,
                  intensity: target.isHardBall ? 1.2 : (target.isRed ? 1.0 : 0.8), // More intense glow for hard balls
                ),
              ),
              child: Center(
                child: NeonText.body(
                  target.isRed ? '${target.points}' : '+${target.points}',
                  glowColor: target.color,
                  fontSize: target.isHardBall ? 20 : 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
  
  Widget _buildUI() {
    return Column(
      children: [
        // Header
        Padding(
          padding: const EdgeInsets.all(20),
          child: Row(
            children: [
              NeonCircularButton(
                icon: Icons.arrow_back,
                glowColor: NeonThemes.cyberBlue.primary,
                onPressed: () => Navigator.of(context).pop(),
              ),
              const SizedBox(width: 20),
              Expanded(
                child: NeonText.title(
                  'TAP TARGET - LEVEL ${widget.levelNumber}',
                  glowColor: _getLevelTheme().primary,
                  fontSize: 20,
                ),
              ),
            ],
          ),
        ),
        
        // Score and time
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              NeonContainer.card(
                glowColor: _getLevelTheme().primary,
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  child: NeonText.body(
                    'Score: $_score / ${_levelConfig.goal}',
                    glowColor: _getLevelTheme().primary,
                    fontSize: 16,
                  ),
                ),
              ),
              NeonContainer.card(
                glowColor: _timeLeft <= 10 ? Colors.red : NeonThemes.cyberBlue.primary,
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  child: NeonText.body(
                    'Time: $_timeLeft',
                    glowColor: _timeLeft <= 10 ? Colors.red : NeonThemes.cyberBlue.primary,
                    fontSize: 16,
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}

/// Target data class
class Target {
  final int id;
  final Offset position;
  final Color color;
  final int points;
  final bool isRed; // Red targets give negative points
  final double fallSpeed; // Speed at which target falls
  final bool isHardBall; // Hard ball that splits into multiple balls
  final int splitCount; // Number of balls to split into
  final int splitScore; // Score for each split ball
  final double size; // Size of the target

  Target({
    required this.id,
    required this.position,
    required this.color,
    required this.points,
    this.isRed = false,
    this.fallSpeed = 100.0, // pixels per second
    this.isHardBall = false,
    this.splitCount = 0,
    this.splitScore = 0,
    this.size = 60.0,
  });

  /// Create a copy with updated position
  Target copyWith({
    int? id,
    Offset? position,
    Color? color,
    int? points,
    bool? isRed,
    double? fallSpeed,
    bool? isHardBall,
    int? splitCount,
    int? splitScore,
    double? size,
  }) {
    return Target(
      id: id ?? this.id,
      position: position ?? this.position,
      color: color ?? this.color,
      points: points ?? this.points,
      isRed: isRed ?? this.isRed,
      fallSpeed: fallSpeed ?? this.fallSpeed,
      isHardBall: isHardBall ?? this.isHardBall,
      splitCount: splitCount ?? this.splitCount,
      splitScore: splitScore ?? this.splitScore,
      size: size ?? this.size,
    );
  }
}


