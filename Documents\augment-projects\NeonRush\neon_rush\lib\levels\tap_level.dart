import 'dart:async';
import 'dart:math';
import 'package:flutter/material.dart';
import '../core/constants.dart';
import '../models/level_config.dart';
import '../game/game_state.dart';
import '../game/power_up_manager.dart';

/// Represents a single dot in the tap game
class TapDot {
  final String id;
  final Offset position;
  final Color color;
  final double size;
  final DateTime spawnTime;
  final Duration lifetime;
  bool isHit = false;

  TapDot({
    required this.id,
    required this.position,
    required this.color,
    required this.size,
    required this.spawnTime,
    required this.lifetime,
  });

  /// Check if the dot has expired
  bool get isExpired {
    return DateTime.now().difference(spawnTime) > lifetime;
  }

  /// Get the remaining lifetime as a percentage (1.0 = just spawned, 0.0 = expired)
  double get lifetimeProgress {
    final elapsed = DateTime.now().difference(spawnTime);
    return (1.0 - (elapsed.inMilliseconds / lifetime.inMilliseconds)).clamp(0.0, 1.0);
  }

  /// Get the current opacity based on lifetime
  double get opacity {
    return lifetimeProgress;
  }

  /// Get the current scale based on lifetime
  double get scale {
    final progress = lifetimeProgress;
    // Pulse effect: start small, grow to full size, then shrink as it expires
    if (progress > 0.8) {
      // Growing phase
      return 0.5 + (1.0 - progress) * 2.5; // 0.5 to 1.0
    } else if (progress > 0.2) {
      // Stable phase
      return 1.0;
    } else {
      // Shrinking phase
      return progress * 5.0; // 1.0 to 0.0
    }
  }

  /// Check if a tap position hits this dot
  bool hitTest(Offset tapPosition, {double hitboxMultiplier = 1.0}) {
    final distance = (tapPosition - position).distance;
    final hitRadius = (size / 2) * hitboxMultiplier;
    return distance <= hitRadius;
  }

  /// Convert to map for game state storage
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'x': position.dx,
      'y': position.dy,
      'color': color.value,
      'size': size,
      'spawnTime': spawnTime.millisecondsSinceEpoch,
      'lifetime': lifetime.inMilliseconds,
      'isHit': isHit,
    };
  }

  /// Create from map
  factory TapDot.fromMap(Map<String, dynamic> map) {
    return TapDot(
      id: map['id'],
      position: Offset(map['x'], map['y']),
      color: Color(map['color']),
      size: map['size'],
      spawnTime: DateTime.fromMillisecondsSinceEpoch(map['spawnTime']),
      lifetime: Duration(milliseconds: map['lifetime']),
    )..isHit = map['isHit'];
  }
}

/// Manages the tap game logic
class TapGameLogic {
  final LevelConfig levelConfig;
  final GameStateManager gameState;
  final PowerUpManager? powerUpManager;
  final Random _random = Random();
  
  Timer? _gameTimer;
  Timer? _spawnTimer;
  DateTime? _gameStartTime;
  
  // Configuration from level
  late double _dotSize;
  late double _spawnRate;
  late double _dotLifetime;
  late Size _gameArea;

  TapGameLogic({
    required this.levelConfig,
    required this.gameState,
    this.powerUpManager,
  }) {
    _initializeConfig();
  }

  /// Initialize configuration from level config
  void _initializeConfig() {
    final config = levelConfig.modeSpecificConfig;
    _dotSize = config['dotSize']?.toDouble() ?? 60.0;
    _spawnRate = config['spawnRate']?.toDouble() ?? 1.5;
    _dotLifetime = config['dotLifetime']?.toDouble() ?? 3.0;
  }

  /// Start the tap game
  void startGame(Size gameArea) {
    _gameArea = gameArea;
    _gameStartTime = DateTime.now();
    
    // Start the main game timer
    _gameTimer = Timer.periodic(const Duration(milliseconds: 16), _updateGame);
    
    // Start spawning dots
    _startSpawning();
  }

  /// Stop the tap game
  void stopGame() {
    _gameTimer?.cancel();
    _spawnTimer?.cancel();
    _gameTimer = null;
    _spawnTimer = null;
  }

  /// Pause the game
  void pauseGame() {
    _gameTimer?.cancel();
    _spawnTimer?.cancel();
  }

  /// Resume the game
  void resumeGame() {
    if (_gameStartTime != null) {
      _gameTimer = Timer.periodic(const Duration(milliseconds: 16), _updateGame);
      _startSpawning();
    }
  }

  /// Handle tap at position
  void handleTap(Offset position) {
    final activeDots = _getActiveDots();
    bool hitAnyDot = false;
    
    // Check for hits (reverse order to prioritize top dots)
    for (int i = activeDots.length - 1; i >= 0; i--) {
      final dot = activeDots[i];
      if (!dot.isHit && dot.hitTest(position, hitboxMultiplier: _getHitboxMultiplier())) {
        _hitDot(dot);
        hitAnyDot = true;
        break; // Only hit one dot per tap
      }
    }
    
    if (!hitAnyDot) {
      _handleMiss();
    }
  }

  /// Get hitbox multiplier based on active power-ups
  double _getHitboxMultiplier() {
    return powerUpManager?.getMagnetTouchMultiplier() ?? 1.0;
  }

  /// Start spawning dots
  void _startSpawning() {
    final spawnInterval = Duration(milliseconds: (1000 / _spawnRate).round());
    _spawnTimer = Timer.periodic(spawnInterval, (_) => _spawnDot());
  }

  /// Spawn a new dot
  void _spawnDot() {
    if (gameState.currentState != GameState.playing) return;

    // Apply slow motion effect - reduce spawn rate
    final slowMotionFactor = powerUpManager?.getSlowMotionFactor() ?? 1.0;
    if (slowMotionFactor < 1.0 && _random.nextDouble() > slowMotionFactor) {
      return;
    }
    
    final dot = _createRandomDot();
    final activeDots = _getActiveDots();
    activeDots.add(dot);
    _updateActiveDots(activeDots);
    
    // Update spawn count
    final dotsSpawned = gameState.getGameData<int>('dotsSpawned') ?? 0;
    gameState.updateGameData('dotsSpawned', dotsSpawned + 1);
  }

  /// Create a random dot
  TapDot _createRandomDot() {
    // Ensure dot spawns within game area with padding
    final padding = _dotSize;
    final x = padding + _random.nextDouble() * (_gameArea.width - 2 * padding);
    final y = padding + _random.nextDouble() * (_gameArea.height - 2 * padding);
    
    final position = Offset(x, y);
    final color = levelConfig.theme.primary;
    final lifetime = Duration(milliseconds: (_dotLifetime * 1000).round());
    
    return TapDot(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      position: position,
      color: color,
      size: _dotSize,
      spawnTime: DateTime.now(),
      lifetime: lifetime,
    );
  }

  /// Handle hitting a dot
  void _hitDot(TapDot dot) {
    dot.isHit = true;
    
    // Update score and stats
    gameState.addScore(10);
    final dotsHit = gameState.getGameData<int>('dotsHit') ?? 0;
    gameState.updateGameData('dotsHit', dotsHit + 1);
    
    // Remove the hit dot
    final activeDots = _getActiveDots();
    activeDots.removeWhere((d) => d.id == dot.id);
    _updateActiveDots(activeDots);
    
    // Check if level is complete
    if (gameState.isLevelGoalAchieved()) {
      gameState.endGame(completed: true);
    }
  }

  /// Handle missing a tap
  void _handleMiss() {
    // Check if shield can protect from penalty
    if (powerUpManager?.useShield() == true) {
      // Shield absorbed the penalty
      return;
    }

    // Small score penalty for missing
    gameState.addScore(-1);
  }

  /// Update game state
  void _updateGame(Timer timer) {
    if (gameState.currentState != GameState.playing) return;
    
    // Update time
    final elapsed = DateTime.now().difference(_gameStartTime!);
    final remaining = levelConfig.duration - elapsed;
    
    gameState.updateTimeElapsed(elapsed);
    gameState.updateTimeRemaining(remaining);
    
    // Check for time up
    if (remaining <= Duration.zero) {
      gameState.endGame(completed: gameState.isLevelGoalAchieved());
      return;
    }
    
    // Remove expired dots
    _removeExpiredDots();
  }

  /// Remove expired dots
  void _removeExpiredDots() {
    final activeDots = _getActiveDots();
    final initialCount = activeDots.length;
    
    activeDots.removeWhere((dot) {
      if (dot.isExpired && !dot.isHit) {
        // Count as missed
        final dotsMissed = gameState.getGameData<int>('dotsMissed') ?? 0;
        gameState.updateGameData('dotsMissed', dotsMissed + 1);
        return true;
      }
      return false;
    });
    
    if (activeDots.length != initialCount) {
      _updateActiveDots(activeDots);
    }
  }

  /// Get active dots from game state
  List<TapDot> _getActiveDots() {
    final dotsData = gameState.getGameData<List<Map<String, dynamic>>>('activeDots') ?? [];
    return dotsData.map((data) => TapDot.fromMap(data)).toList();
  }

  /// Update active dots in game state
  void _updateActiveDots(List<TapDot> dots) {
    final dotsData = dots.map((dot) => dot.toMap()).toList();
    gameState.updateGameData('activeDots', dotsData);
  }

  /// Get current game statistics
  Map<String, dynamic> getGameStats() {
    return {
      'dotsHit': gameState.getGameData<int>('dotsHit') ?? 0,
      'dotsSpawned': gameState.getGameData<int>('dotsSpawned') ?? 0,
      'dotsMissed': gameState.getGameData<int>('dotsMissed') ?? 0,
      'accuracy': _calculateAccuracy(),
      'score': gameState.currentScore,
      'timeElapsed': gameState.timeElapsed,
      'timeRemaining': gameState.timeRemaining,
    };
  }

  /// Calculate accuracy percentage
  double _calculateAccuracy() {
    final dotsHit = gameState.getGameData<int>('dotsHit') ?? 0;
    final dotsSpawned = gameState.getGameData<int>('dotsSpawned') ?? 0;
    
    if (dotsSpawned == 0) return 0.0;
    return (dotsHit / dotsSpawned * 100).clamp(0.0, 100.0);
  }

  /// Dispose resources
  void dispose() {
    stopGame();
  }
}
