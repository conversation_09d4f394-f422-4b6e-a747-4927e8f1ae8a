import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';

import '../models/player_profile.dart';

/// Manages game state and player progress
class GameStateManager extends ChangeNotifier {
  static final GameStateManager _instance = GameStateManager._internal();
  factory GameStateManager() => _instance;
  GameStateManager._internal();

  PlayerProfile? _playerProfile;
  bool _isInitialized = false;

  /// Get the current player profile
  PlayerProfile? get playerProfile => _playerProfile;

  /// Check if the manager is initialized
  bool get isInitialized => _isInitialized;

  /// Initialize the game state manager
  Future<void> initialize() async {
    if (_isInitialized) return;

    await _loadPlayerProfile();
    _isInitialized = true;
    notifyListeners();
  }

  /// Load player profile from storage
  Future<void> _loadPlayerProfile() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final profileJson = prefs.getString('player_profile');
      
      if (profileJson != null) {
        final profileData = json.decode(profileJson);
        _playerProfile = PlayerProfile.fromJson(profileData);
      } else {
        // Create new player profile
        _playerProfile = PlayerProfile.newPlayer(
          playerId: 'player_${DateTime.now().millisecondsSinceEpoch}',
          playerName: 'Player',
        );
        await savePlayerProfile();
      }
    } catch (e) {
      debugPrint('Error loading player profile: $e');
      // Create default profile on error
      _playerProfile = PlayerProfile.newPlayer(
        playerId: 'player_${DateTime.now().millisecondsSinceEpoch}',
        playerName: 'Player',
      );
    }
  }

  /// Save player profile to storage
  Future<void> savePlayerProfile() async {
    if (_playerProfile == null) return;

    try {
      final prefs = await SharedPreferences.getInstance();
      final profileJson = json.encode(_playerProfile!.toJson());
      await prefs.setString('player_profile', profileJson);
    } catch (e) {
      debugPrint('Error saving player profile: $e');
    }
  }

  /// Update player profile
  Future<void> updatePlayerProfile(PlayerProfile newProfile) async {
    _playerProfile = newProfile;
    await savePlayerProfile();
    notifyListeners();
  }

  /// Add tokens to player
  Future<void> addTokens(int amount) async {
    if (_playerProfile == null) return;

    _playerProfile = _playerProfile!.copyWith(
      tokens: _playerProfile!.tokens + amount,
    );
    await savePlayerProfile();
    notifyListeners();
  }

  /// Spend tokens
  Future<bool> spendTokens(int amount) async {
    if (_playerProfile == null || _playerProfile!.tokens < amount) {
      return false;
    }

    _playerProfile = _playerProfile!.copyWith(
      tokens: _playerProfile!.tokens - amount,
    );
    await savePlayerProfile();
    notifyListeners();
    return true;
  }

  /// Add score to player (simplified)
  Future<void> addScore(int score) async {
    // TODO: Implement score tracking with proper PlayerProfile structure
    notifyListeners();
  }

  /// Complete a level (simplified)
  Future<void> completeLevel(int levelNumber, int score) async {
    // TODO: Implement level completion with proper PlayerProfile structure
    notifyListeners();
  }

  /// Check if a level is unlocked (simplified)
  bool isLevelUnlocked(int levelNumber) {
    // TODO: Implement with proper PlayerProfile structure
    return true; // Allow all levels for now
  }

  /// Check if a level is completed (simplified)
  bool isLevelCompleted(int levelNumber) {
    // TODO: Implement with proper PlayerProfile structure
    return false;
  }

  /// Reset player progress
  Future<void> resetProgress() async {
    _playerProfile = PlayerProfile.newPlayer(
      playerId: _playerProfile?.playerId ?? 'player_${DateTime.now().millisecondsSinceEpoch}',
      playerName: _playerProfile?.playerName ?? 'Player',
    );
    await savePlayerProfile();
    notifyListeners();
  }

  /// Get player statistics
  Map<String, dynamic> getPlayerStats() {
    if (_playerProfile == null) return {};

    return {
      'tokens': _playerProfile!.tokens,
      'themes': _playerProfile!.unlockedThemes.length,
      'powerUps': _playerProfile!.ownedPowerUps.length,
    };
  }
}
