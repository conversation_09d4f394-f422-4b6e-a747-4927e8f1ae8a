import 'dart:math';
import 'package:flutter/material.dart';

import '../core/constants.dart';

/// Boss types for different levels
enum BossType {
  neonGuardian,    // Level 5
  cyberSentinel,   // Level 10
  plasmaTitan,     // Level 20
  voidLord,        // Level 50
  quantumEmperor,  // Level 80
  neonOverlord,    // Level 100
}

/// Boss attack patterns
enum BossAttackType {
  basicTap,        // Requires tapping boss
  rapidFire,       // Multiple quick taps needed
  chargedAttack,   // Hold to charge, then tap
  shieldBreaker,   // Must tap weak points
  ultimateAttack,  // Complex pattern
}

/// Boss state during fight
enum BossState {
  idle,
  attacking,
  vulnerable,
  charging,
  defeated,
}

/// Represents a boss enemy with health, attacks, and behaviors
class Boss {
  final String id;
  final BossType type;
  final String name;
  final String description;
  final int level;
  
  // Combat stats
  final int maxHealth;
  int currentHealth;
  final int damage;
  final double attackSpeed;
  
  // Visual properties
  final Color primaryColor;
  final Color secondaryColor;
  final IconData icon;
  final double size;
  
  // Position and movement
  Offset position;
  Offset velocity;
  double rotation;
  
  // State management
  BossState state;
  DateTime lastAttackTime;
  DateTime stateChangeTime;
  
  // Attack patterns
  final List<BossAttackType> attackPatterns;
  int currentAttackIndex;
  
  // Special effects
  double glowIntensity;
  double scale;
  double opacity;
  
  Boss({
    required this.id,
    required this.type,
    required this.name,
    required this.description,
    required this.level,
    required this.maxHealth,
    required this.damage,
    required this.attackSpeed,
    required this.primaryColor,
    required this.secondaryColor,
    required this.icon,
    required this.size,
    required this.position,
    required this.attackPatterns,
    this.velocity = Offset.zero,
    this.rotation = 0.0,
    this.state = BossState.idle,
    this.currentAttackIndex = 0,
    this.glowIntensity = 1.0,
    this.scale = 1.0,
    this.opacity = 1.0,
  }) : currentHealth = maxHealth,
       lastAttackTime = DateTime.now(),
       stateChangeTime = DateTime.now();

  /// Update boss logic each frame
  void update(Duration deltaTime) {
    final now = DateTime.now();
    final timeSinceLastAttack = now.difference(lastAttackTime);
    final timeSinceStateChange = now.difference(stateChangeTime);
    
    // Update position
    position += velocity * (deltaTime.inMilliseconds / 1000.0);
    
    // Update rotation for visual effect
    rotation += 0.5 * (deltaTime.inMilliseconds / 1000.0);
    
    // Update glow effect
    glowIntensity = 0.8 + 0.2 * sin(now.millisecondsSinceEpoch / 500.0);
    
    // State machine
    switch (state) {
      case BossState.idle:
        if (timeSinceStateChange.inMilliseconds > 2000) {
          _startAttack();
        }
        break;
        
      case BossState.attacking:
        if (timeSinceStateChange.inMilliseconds > 3000) {
          _becomeVulnerable();
        }
        break;
        
      case BossState.vulnerable:
        if (timeSinceStateChange.inMilliseconds > 2000) {
          _returnToIdle();
        }
        break;
        
      case BossState.charging:
        scale = 1.0 + 0.3 * (timeSinceStateChange.inMilliseconds / 2000.0);
        if (timeSinceStateChange.inMilliseconds > 2000) {
          _executeUltimate();
        }
        break;
        
      case BossState.defeated:
        opacity = max(0.0, opacity - deltaTime.inMilliseconds / 2000.0);
        scale += deltaTime.inMilliseconds / 1000.0;
        break;
    }
  }

  /// Take damage from player attack
  bool takeDamage(int damage) {
    if (state != BossState.vulnerable && state != BossState.idle) {
      return false; // Boss is not vulnerable
    }
    
    currentHealth = max(0, currentHealth - damage);
    
    // Visual feedback
    glowIntensity = 1.5;
    scale = 1.2;
    
    if (currentHealth <= 0) {
      _defeat();
      return true;
    }
    
    return true;
  }

  /// Get current health percentage
  double get healthPercentage => currentHealth / maxHealth;

  /// Check if boss is defeated
  bool get isDefeated => state == BossState.defeated;

  /// Get current attack pattern
  BossAttackType get currentAttackPattern {
    if (attackPatterns.isEmpty) return BossAttackType.basicTap;
    return attackPatterns[currentAttackIndex % attackPatterns.length];
  }

  /// Get display color based on state
  Color getDisplayColor() {
    switch (state) {
      case BossState.idle:
        return primaryColor;
      case BossState.attacking:
        return Colors.red;
      case BossState.vulnerable:
        return Colors.yellow;
      case BossState.charging:
        return secondaryColor;
      case BossState.defeated:
        return primaryColor.withValues(alpha: opacity);
    }
  }

  /// Get current size with scale applied
  double getCurrentSize() => size * scale;

  // Private methods for state transitions
  void _startAttack() {
    state = BossState.attacking;
    stateChangeTime = DateTime.now();
    lastAttackTime = DateTime.now();
    currentAttackIndex++;
  }

  void _becomeVulnerable() {
    state = BossState.vulnerable;
    stateChangeTime = DateTime.now();
  }

  void _returnToIdle() {
    state = BossState.idle;
    stateChangeTime = DateTime.now();
    scale = 1.0;
  }

  void _executeUltimate() {
    state = BossState.attacking;
    stateChangeTime = DateTime.now();
    scale = 1.0;
  }

  void _defeat() {
    state = BossState.defeated;
    stateChangeTime = DateTime.now();
  }

  /// Convert to JSON for serialization
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type.name,
      'name': name,
      'description': description,
      'level': level,
      'maxHealth': maxHealth,
      'currentHealth': currentHealth,
      'damage': damage,
      'attackSpeed': attackSpeed,
      'primaryColor': primaryColor.value,
      'secondaryColor': secondaryColor.value,
      'size': size,
      'position': {'dx': position.dx, 'dy': position.dy},
      'state': state.name,
      'currentAttackIndex': currentAttackIndex,
    };
  }

  /// Create from JSON
  factory Boss.fromJson(Map<String, dynamic> json) {
    return Boss(
      id: json['id'],
      type: BossType.values.firstWhere((t) => t.name == json['type']),
      name: json['name'],
      description: json['description'],
      level: json['level'],
      maxHealth: json['maxHealth'],
      damage: json['damage'],
      attackSpeed: json['attackSpeed'].toDouble(),
      primaryColor: Color(json['primaryColor']),
      secondaryColor: Color(json['secondaryColor']),
      icon: Icons.warning, // Default icon, would need icon mapping
      size: json['size'].toDouble(),
      position: Offset(json['position']['dx'], json['position']['dy']),
      attackPatterns: [BossAttackType.basicTap], // Default pattern
    )..currentHealth = json['currentHealth']
     ..state = BossState.values.firstWhere((s) => s.name == json['state'])
     ..currentAttackIndex = json['currentAttackIndex'];
  }
}

/// Predefined boss configurations
class BossConfigs {
  static Boss createBossForLevel(int level, Offset spawnPosition) {
    switch (level) {
      case 5:
        return _createNeonGuardian(spawnPosition);
      case 10:
        return _createCyberSentinel(spawnPosition);
      case 20:
        return _createPlasmaTitan(spawnPosition);
      case 50:
        return _createVoidLord(spawnPosition);
      case 80:
        return _createQuantumEmperor(spawnPosition);
      case 100:
        return _createNeonOverlord(spawnPosition);
      default:
        throw ArgumentError('No boss configured for level $level');
    }
  }

  static Boss _createNeonGuardian(Offset position) {
    return Boss(
      id: 'neon_guardian_5',
      type: BossType.neonGuardian,
      name: 'Neon Guardian',
      description: 'A basic guardian protecting the neon realm',
      level: 5,
      maxHealth: 100,
      damage: 10,
      attackSpeed: 1.0,
      primaryColor: const Color(0xFF00FFFF),
      secondaryColor: const Color(0xFF0080FF),
      icon: Icons.shield,
      size: 120.0,
      position: position,
      attackPatterns: [BossAttackType.basicTap, BossAttackType.rapidFire],
    );
  }

  static Boss _createCyberSentinel(Offset position) {
    return Boss(
      id: 'cyber_sentinel_10',
      type: BossType.cyberSentinel,
      name: 'Cyber Sentinel',
      description: 'An advanced AI guardian with enhanced defenses',
      level: 10,
      maxHealth: 200,
      damage: 15,
      attackSpeed: 1.2,
      primaryColor: const Color(0xFFFF00FF),
      secondaryColor: const Color(0xFF8000FF),
      icon: Icons.computer,
      size: 140.0,
      position: position,
      attackPatterns: [BossAttackType.basicTap, BossAttackType.chargedAttack],
    );
  }

  static Boss _createPlasmaTitan(Offset position) {
    return Boss(
      id: 'plasma_titan_20',
      type: BossType.plasmaTitan,
      name: 'Plasma Titan',
      description: 'A massive energy being with devastating attacks',
      level: 20,
      maxHealth: 350,
      damage: 20,
      attackSpeed: 1.5,
      primaryColor: const Color(0xFF00FF00),
      secondaryColor: const Color(0xFF80FF00),
      icon: Icons.flash_on,
      size: 160.0,
      position: position,
      attackPatterns: [BossAttackType.rapidFire, BossAttackType.shieldBreaker],
    );
  }

  static Boss _createVoidLord(Offset position) {
    return Boss(
      id: 'void_lord_50',
      type: BossType.voidLord,
      name: 'Void Lord',
      description: 'Master of darkness with reality-bending powers',
      level: 50,
      maxHealth: 500,
      damage: 30,
      attackSpeed: 2.0,
      primaryColor: const Color(0xFF8000FF),
      secondaryColor: const Color(0xFF4000FF),
      icon: Icons.dark_mode,
      size: 180.0,
      position: position,
      attackPatterns: [BossAttackType.chargedAttack, BossAttackType.ultimateAttack],
    );
  }

  static Boss _createQuantumEmperor(Offset position) {
    return Boss(
      id: 'quantum_emperor_80',
      type: BossType.quantumEmperor,
      name: 'Quantum Emperor',
      description: 'Ruler of quantum dimensions with unpredictable attacks',
      level: 80,
      maxHealth: 750,
      damage: 40,
      attackSpeed: 2.5,
      primaryColor: const Color(0xFFFFD700),
      secondaryColor: const Color(0xFFFF8000),
      icon: Icons.auto_awesome,
      size: 200.0,
      position: position,
      attackPatterns: [BossAttackType.shieldBreaker, BossAttackType.ultimateAttack],
    );
  }

  static Boss _createNeonOverlord(Offset position) {
    return Boss(
      id: 'neon_overlord_100',
      type: BossType.neonOverlord,
      name: 'Neon Overlord',
      description: 'The ultimate boss with all attack patterns',
      level: 100,
      maxHealth: 1000,
      damage: 50,
      attackSpeed: 3.0,
      primaryColor: const Color(0xFFFF0080),
      secondaryColor: const Color(0xFF8000FF),
      icon: Icons.star,
      size: 220.0,
      position: position,
      attackPatterns: [
        BossAttackType.basicTap,
        BossAttackType.rapidFire,
        BossAttackType.chargedAttack,
        BossAttackType.shieldBreaker,
        BossAttackType.ultimateAttack,
      ],
    );
  }
}
