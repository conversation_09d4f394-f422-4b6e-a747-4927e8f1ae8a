import 'package:flutter/material.dart';

/// Represents a visual theme for a specific layer range in Breakfinity
class BreakfinityLayerTheme {
  final String id;
  final String name;
  final String description;
  final int startLayer;
  final int endLayer;
  final Color primaryColor;
  final Color secondaryColor;
  final Color accentColor;
  final Color backgroundColor;
  final List<Color> gradientColors;
  final String particleType;
  final double glowIntensity;
  final Map<String, dynamic> visualEffects;

  const BreakfinityLayerTheme({
    required this.id,
    required this.name,
    required this.description,
    required this.startLayer,
    required this.endLayer,
    required this.primaryColor,
    required this.secondaryColor,
    required this.accentColor,
    required this.backgroundColor,
    required this.gradientColors,
    required this.particleType,
    this.glowIntensity = 1.0,
    this.visualEffects = const {},
  });

  /// Generate section color based on theme and section properties
  Color getSectionColor(int sectionIndex, String sectionType) {
    final baseHue = _getBaseHue();
    final variation = (sectionIndex * 15) % 60 - 30; // ±30 degree variation
    final finalHue = (baseHue + variation) % 360;

    double saturation = 0.7;
    double brightness = 0.8;

    // Adjust based on section type
    switch (sectionType) {
      case 'weak':
        saturation *= 0.5;
        brightness *= 1.2;
        break;
      case 'reinforced':
        saturation *= 1.3;
        brightness *= 0.7;
        break;
      case 'crystal':
        saturation = 1.0;
        brightness = 1.0;
        break;
      case 'mystery':
        // Use accent color for mystery sections
        return accentColor;
    }

    return HSVColor.fromAHSV(1.0, finalHue.toDouble(), saturation, brightness).toColor();
  }

  double _getBaseHue() {
    // Extract hue from primary color
    final hsv = HSVColor.fromColor(primaryColor);
    return hsv.hue;
  }

  /// Get particle color for breaking effects
  Color getParticleColor() {
    return Color.lerp(primaryColor, accentColor, 0.5) ?? primaryColor;
  }

  /// Get glow color for section outlines
  Color getGlowColor() {
    return accentColor.withValues(alpha: 0.6 * glowIntensity);
  }
}

/// Predefined layer themes for different depth ranges
class BreakfinityLayerThemes {
  // 1–10: Neon Plasma Core — Bright cyan with pulsing plasma & electric glow
  static const neonPlasmaCore = BreakfinityLayerTheme(
    id: 'neon_plasma_core',
    name: 'Neon Plasma Core',
    description: 'Bright cyan with pulsing plasma & electric glow',
    startLayer: 1,
    endLayer: 10,
    primaryColor: Color(0xFF00FFFF),
    secondaryColor: Color(0xFF0080FF),
    accentColor: Color(0xFF80FFFF),
    backgroundColor: Color(0xFF001122),
    gradientColors: [Color(0xFF00FFFF), Color(0xFF0080FF), Color(0xFF004080)],
    particleType: 'plasma',
    glowIntensity: 1.2,
    visualEffects: {
      'pulseRate': 2.0,
      'electricArcs': true,
      'coreGlow': true,
    },
  );

  // 11–25: Purple Wave Glass — Transparent glassy shards with purple ripple lighting
  static const purpleWaveGlass = BreakfinityLayerTheme(
    id: 'purple_wave_glass',
    name: 'Purple Wave Glass',
    description: 'Transparent glassy shards with purple ripple lighting',
    startLayer: 11,
    endLayer: 25,
    primaryColor: Color(0xFF8000FF),
    secondaryColor: Color(0xFF4000AA),
    accentColor: Color(0xFFB040FF),
    backgroundColor: Color(0xFF200040),
    gradientColors: [Color(0xFF8000FF), Color(0xFF4000AA), Color(0xFF200055)],
    particleType: 'crystal',
    glowIntensity: 1.0,
    visualEffects: {
      'waveEffect': true,
      'glassReflection': true,
      'shimmering': true,
    },
  );

  // 26–50: Electric Grid — Dark panels with neon circuitry (blue & yellow tracers)
  static const electricGrid = BreakfinityLayerTheme(
    id: 'electric_grid',
    name: 'Electric Grid',
    description: 'Dark panels with neon circuitry (blue & yellow tracers)',
    startLayer: 26,
    endLayer: 50,
    primaryColor: Color(0xFF0080FF),
    secondaryColor: Color(0xFFFFFF00),
    accentColor: Color(0xFF80CCFF),
    backgroundColor: Color(0xFF0A0A0A),
    gradientColors: [Color(0xFF0080FF), Color(0xFFFFFF00), Color(0xFF004080)],
    particleType: 'electric',
    glowIntensity: 1.5,
    visualEffects: {
      'electricArcs': true,
      'gridPattern': true,
      'sparks': true,
    },
  );

  // 51–75: Ember Dust — Glowing ember pieces, red/orange flickering dust
  static const emberDust = BreakfinityLayerTheme(
    id: 'ember_dust',
    name: 'Ember Dust',
    description: 'Glowing ember pieces, red/orange flickering dust',
    startLayer: 51,
    endLayer: 75,
    primaryColor: Color(0xFFFF4500),
    secondaryColor: Color(0xFFFF0000),
    accentColor: Color(0xFFFF8040),
    backgroundColor: Color(0xFF1A0A00),
    gradientColors: [Color(0xFFFF4500), Color(0xFFFF0000), Color(0xFF800000)],
    particleType: 'ember',
    glowIntensity: 1.3,
    visualEffects: {
      'flames': true,
      'heatWaves': true,
      'ember': true,
      'flickering': true,
    },
  );

  // 76–100: Quantum Ice — Sharp icy chunks with glowing blue/pink refraction
  static const quantumIce = BreakfinityLayerTheme(
    id: 'quantum_ice',
    name: 'Quantum Ice',
    description: 'Sharp icy chunks with glowing blue/pink refraction',
    startLayer: 76,
    endLayer: 100,
    primaryColor: Color(0xFF00AAFF),
    secondaryColor: Color(0xFFFF00AA),
    accentColor: Color(0xFF80CCFF),
    backgroundColor: Color(0xFF001133),
    gradientColors: [Color(0xFF00AAFF), Color(0xFFFF00AA), Color(0xFF003366)],
    particleType: 'ice',
    glowIntensity: 1.1,
    visualEffects: {
      'iceShards': true,
      'refraction': true,
      'quantumGlow': true,
    },
  );

  // 101–130: Magenta Crystal — Gemstone-like facets with bright magenta glow
  static const magentaCrystal = BreakfinityLayerTheme(
    id: 'magenta_crystal',
    name: 'Magenta Crystal',
    description: 'Gemstone-like facets with bright magenta glow',
    startLayer: 101,
    endLayer: 130,
    primaryColor: Color(0xFFFF00FF),
    secondaryColor: Color(0xFFAA00AA),
    accentColor: Color(0xFFFF80FF),
    backgroundColor: Color(0xFF330033),
    gradientColors: [Color(0xFFFF00FF), Color(0xFFAA00AA), Color(0xFF550055)],
    particleType: 'crystal',
    glowIntensity: 1.4,
    visualEffects: {
      'crystalline': true,
      'facets': true,
      'magentaGlow': true,
    },
  );

  // 131–160: Toxic Reactor — Slime green energy core with radiation sparks
  static const toxicReactor = BreakfinityLayerTheme(
    id: 'toxic_reactor',
    name: 'Toxic Reactor',
    description: 'Slime green energy core with radiation sparks',
    startLayer: 131,
    endLayer: 160,
    primaryColor: Color(0xFF00FF00),
    secondaryColor: Color(0xFF80FF00),
    accentColor: Color(0xFF40FF40),
    backgroundColor: Color(0xFF001100),
    gradientColors: [Color(0xFF00FF00), Color(0xFF80FF00), Color(0xFF004400)],
    particleType: 'toxic',
    glowIntensity: 1.5,
    visualEffects: {
      'radiation': true,
      'toxicGlow': true,
      'sparks': true,
    },
  );

  // 161–200: Solar Emberstone — Lava-like neon orange rocks, animated heat shimmer
  static const solarEmberstone = BreakfinityLayerTheme(
    id: 'solar_emberstone',
    name: 'Solar Emberstone',
    description: 'Lava-like neon orange rocks, animated heat shimmer',
    startLayer: 161,
    endLayer: 200,
    primaryColor: Color(0xFFFF4500),
    secondaryColor: Color(0xFFFF8C00),
    accentColor: Color(0xFFFFB347),
    backgroundColor: Color(0xFF1A0A00),
    gradientColors: [Color(0xFFFF4500), Color(0xFFFF8C00), Color(0xFF8B4513)],
    particleType: 'lava',
    glowIntensity: 1.6,
    visualEffects: {
      'heatShimmer': true,
      'lavaFlow': true,
      'emberGlow': true,
    },
  );

  static const cosmicStorm = BreakfinityLayerTheme(
    id: 'cosmic_storm',
    name: 'Cosmic Storm',
    description: 'Swirling galactic energies',
    startLayer: 501,
    endLayer: 1000,
    primaryColor: Color(0xFFFF1493),
    secondaryColor: Color(0xFF9932CC),
    accentColor: Color(0xFFFF69B4),
    backgroundColor: Color(0xFF191970),
    gradientColors: [Color(0xFFFF1493), Color(0xFF9932CC), Color(0xFF4B0082)],
    particleType: 'cosmic',
    glowIntensity: 2.0,
    visualEffects: {
      'nebula': true,
      'starField': true,
      'cosmicWind': true,
    },
  );

  static const infiniteDepth = BreakfinityLayerTheme(
    id: 'infinite_depth',
    name: 'Infinite Depth',
    description: 'Beyond comprehension',
    startLayer: 1001,
    endLayer: 999999,
    primaryColor: Color(0xFFFFFFFF),
    secondaryColor: Color(0xFFC0C0C0),
    accentColor: Color(0xFFFFD700),
    backgroundColor: Color(0xFF000000),
    gradientColors: [Color(0xFFFFFFFF), Color(0xFFC0C0C0), Color(0xFF808080)],
    particleType: 'transcendent',
    glowIntensity: 3.0,
    visualEffects: {
      'reality_distortion': true,
      'infinite_fractals': true,
      'transcendence': true,
    },
  );

  /// Get all available themes
  static List<BreakfinityLayerTheme> get allThemes => [
        neonPlasmaCore,        // 1-10
        purpleWaveGlass,       // 11-25
        electricGrid,          // 26-50
        emberDust,             // 51-75
        quantumIce,            // 76-100
        magentaCrystal,        // 101-130
        toxicReactor,          // 131-160
        solarEmberstone,       // 161-200
        // TODO: Add remaining themes for layers 201-1000+
      ];

  /// Get the appropriate theme for a given layer
  static BreakfinityLayerTheme getThemeForLayer(int layer) {
    final themes = allThemes;
    for (final theme in themes) {
      if (layer >= theme.startLayer && layer <= theme.endLayer) {
        return theme;
      }
    }
    // Return default theme if no match found
    return themes.first;
  }

  /// Get theme by ID
  static BreakfinityLayerTheme? getThemeById(String id) {
    try {
      return allThemes.firstWhere((theme) => theme.id == id);
    } catch (e) {
      return null;
    }
  }
}


