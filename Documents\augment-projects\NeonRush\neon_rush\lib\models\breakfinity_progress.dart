import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'breakfinity_section.dart';

/// Manages the overall progress and state of the Breakfinity game
class BreakfinityProgress {
  final int currentLayer;
  final int totalLayersBroken;
  final int totalSectionsBroken;
  final int totalTokensEarned;
  final int totalTaps;
  final int baseDamage;
  final double tapMultiplier;
  final Map<String, int> powerUps;
  final Map<String, dynamic> upgrades;
  final List<String> unlockedThemes;
  final DateTime lastPlayedAt;
  final Map<int, LayerProgress> layerProgress;
  final Map<String, dynamic> statistics;
  final Map<String, Map<String, dynamic>> savedSections; // Store section states by layer

  const BreakfinityProgress({
    this.currentLayer = 1,
    this.totalLayersBroken = 0,
    this.totalSectionsBroken = 0,
    this.totalTokensEarned = 0,
    this.totalTaps = 0,
    this.baseDamage = 5, // Increased from 1 to make sections breakable with fewer taps
    this.tapMultiplier = 1.0,
    this.powerUps = const {},
    this.upgrades = const {},
    this.unlockedThemes = const [],
    required this.lastPlayedAt,
    this.layerProgress = const {},
    this.statistics = const {},
    this.savedSections = const {},
  });

  /// Create a copy with updated properties
  BreakfinityProgress copyWith({
    int? currentLayer,
    int? totalLayersBroken,
    int? totalSectionsBroken,
    int? totalTokensEarned,
    int? totalTaps,
    int? baseDamage,
    double? tapMultiplier,
    Map<String, int>? powerUps,
    Map<String, dynamic>? upgrades,
    List<String>? unlockedThemes,
    DateTime? lastPlayedAt,
    Map<int, LayerProgress>? layerProgress,
    Map<String, dynamic>? statistics,
    Map<String, Map<String, dynamic>>? savedSections,
  }) {
    return BreakfinityProgress(
      currentLayer: currentLayer ?? this.currentLayer,
      totalLayersBroken: totalLayersBroken ?? this.totalLayersBroken,
      totalSectionsBroken: totalSectionsBroken ?? this.totalSectionsBroken,
      totalTokensEarned: totalTokensEarned ?? this.totalTokensEarned,
      totalTaps: totalTaps ?? this.totalTaps,
      baseDamage: baseDamage ?? this.baseDamage,
      tapMultiplier: tapMultiplier ?? this.tapMultiplier,
      powerUps: powerUps ?? this.powerUps,
      upgrades: upgrades ?? this.upgrades,
      unlockedThemes: unlockedThemes ?? this.unlockedThemes,
      lastPlayedAt: lastPlayedAt ?? this.lastPlayedAt,
      layerProgress: layerProgress ?? this.layerProgress,
      statistics: statistics ?? this.statistics,
      savedSections: savedSections ?? this.savedSections,
    );
  }

  /// Calculate effective damage per tap
  int getEffectiveDamage() {
    double damage = baseDamage.toDouble();

    // Apply tap multiplier
    damage *= tapMultiplier;

    // Apply damage boost upgrade (each level adds 20% damage)
    if (upgrades.containsKey('damage_boost')) {
      final damageBoostLevel = upgrades['damage_boost'] as int;
      damage *= (1.0 + (damageBoostLevel * 0.2)); // 20% per level
    }

    // Apply legacy damage multiplier (for backwards compatibility)
    if (upgrades.containsKey('damage_multiplier')) {
      damage *= upgrades['damage_multiplier'] as double;
    }

    // Apply power-up effects
    if (powerUps.containsKey('mega_tap') && powerUps['mega_tap']! > 0) {
      damage *= 5.0;
    }

    return damage.round();
  }

  /// Get progress percentage for current layer
  double getCurrentLayerProgress() {
    final progress = layerProgress[currentLayer];
    if (progress == null) return 0.0;
    
    return progress.sectionsDestroyed / progress.totalSections;
  }

  /// Check if a layer is completely broken
  bool isLayerComplete(int layer) {
    final progress = layerProgress[layer];
    if (progress == null) return false;
    
    return progress.sectionsDestroyed >= progress.totalSections;
  }

  /// Get total progress through all layers
  double getTotalProgress() {
    if (totalLayersBroken == 0) return 0.0;
    return totalLayersBroken / 1000.0; // Assuming 1000 total layers
  }

  /// Save section states for a layer
  BreakfinityProgress saveSectionStates(int layer, Map<String, dynamic> sections) {
    final newSavedSections = Map<String, Map<String, dynamic>>.from(savedSections);
    newSavedSections[layer.toString()] = Map<String, dynamic>.from(sections);

    return copyWith(
      savedSections: newSavedSections,
      lastPlayedAt: DateTime.now(),
    );
  }

  /// Get saved section states for a layer
  Map<String, dynamic> getSectionStates(int layer) {
    return Map<String, dynamic>.from(savedSections[layer.toString()] ?? {});
  }

  /// Check if there are saved sections for a layer
  bool hasSavedSections(int layer) {
    return savedSections.containsKey(layer.toString()) &&
           savedSections[layer.toString()]!.isNotEmpty;
  }

  /// Convert to JSON for persistence
  Map<String, dynamic> toJson() {
    return {
      'currentLayer': currentLayer,
      'totalLayersBroken': totalLayersBroken,
      'totalSectionsBroken': totalSectionsBroken,
      'totalTokensEarned': totalTokensEarned,
      'totalTaps': totalTaps,
      'baseDamage': baseDamage,
      'tapMultiplier': tapMultiplier,
      'powerUps': powerUps,
      'upgrades': upgrades,
      'unlockedThemes': unlockedThemes,
      'lastPlayedAt': lastPlayedAt.toIso8601String(),
      'layerProgress': layerProgress.map((k, v) => MapEntry(k.toString(), v.toJson())),
      'statistics': statistics,
      'savedSections': savedSections,
    };
  }

  /// Create from JSON
  factory BreakfinityProgress.fromJson(Map<String, dynamic> json) {
    return BreakfinityProgress(
      currentLayer: json['currentLayer'] ?? 1,
      totalLayersBroken: json['totalLayersBroken'] ?? 0,
      totalSectionsBroken: json['totalSectionsBroken'] ?? 0,
      totalTokensEarned: json['totalTokensEarned'] ?? 0,
      totalTaps: json['totalTaps'] ?? 0,
      baseDamage: json['baseDamage'] ?? 5, // Updated default to match constructor
      tapMultiplier: json['tapMultiplier']?.toDouble() ?? 1.0,
      powerUps: Map<String, int>.from(json['powerUps'] ?? {}),
      upgrades: Map<String, dynamic>.from(json['upgrades'] ?? {}),
      unlockedThemes: List<String>.from(json['unlockedThemes'] ?? []),
      lastPlayedAt: json['lastPlayedAt'] != null 
          ? DateTime.parse(json['lastPlayedAt'])
          : DateTime.now(),
      layerProgress: (json['layerProgress'] as Map<String, dynamic>?)?.map(
        (k, v) => MapEntry(int.parse(k), LayerProgress.fromJson(v))
      ) ?? {},
      statistics: Map<String, dynamic>.from(json['statistics'] ?? {}),
      savedSections: Map<String, Map<String, dynamic>>.from(json['savedSections'] ?? {}),
    );
  }

  /// Save progress to SharedPreferences
  Future<void> save() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('breakfinity_progress', jsonEncode(toJson()));
  }

  /// Load progress from SharedPreferences
  static Future<BreakfinityProgress> load() async {
    final prefs = await SharedPreferences.getInstance();
    final progressJson = prefs.getString('breakfinity_progress');
    
    if (progressJson == null) {
      return BreakfinityProgress(lastPlayedAt: DateTime.now());
    }
    
    try {
      final data = jsonDecode(progressJson) as Map<String, dynamic>;
      return BreakfinityProgress.fromJson(data);
    } catch (e) {
      // If loading fails, return default progress
      return BreakfinityProgress(lastPlayedAt: DateTime.now());
    }
  }
}

/// Progress tracking for individual layers
class LayerProgress {
  final int layerNumber;
  final int totalSections;
  final int sectionsDestroyed;
  final int sectionsRevealed;
  final DateTime firstAccessedAt;
  final DateTime? completedAt;
  final Map<String, dynamic> rewards;

  const LayerProgress({
    required this.layerNumber,
    required this.totalSections,
    this.sectionsDestroyed = 0,
    this.sectionsRevealed = 0,
    required this.firstAccessedAt,
    this.completedAt,
    this.rewards = const {},
  });

  /// Create a copy with updated properties
  LayerProgress copyWith({
    int? layerNumber,
    int? totalSections,
    int? sectionsDestroyed,
    int? sectionsRevealed,
    DateTime? firstAccessedAt,
    DateTime? completedAt,
    Map<String, dynamic>? rewards,
  }) {
    return LayerProgress(
      layerNumber: layerNumber ?? this.layerNumber,
      totalSections: totalSections ?? this.totalSections,
      sectionsDestroyed: sectionsDestroyed ?? this.sectionsDestroyed,
      sectionsRevealed: sectionsRevealed ?? this.sectionsRevealed,
      firstAccessedAt: firstAccessedAt ?? this.firstAccessedAt,
      completedAt: completedAt ?? this.completedAt,
      rewards: rewards ?? this.rewards,
    );
  }

  /// Get completion percentage
  double get completionPercentage => sectionsDestroyed / totalSections;

  /// Check if layer is complete
  bool get isComplete => sectionsDestroyed >= totalSections;

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'layerNumber': layerNumber,
      'totalSections': totalSections,
      'sectionsDestroyed': sectionsDestroyed,
      'sectionsRevealed': sectionsRevealed,
      'firstAccessedAt': firstAccessedAt.toIso8601String(),
      'completedAt': completedAt?.toIso8601String(),
      'rewards': rewards,
    };
  }

  /// Create from JSON
  factory LayerProgress.fromJson(Map<String, dynamic> json) {
    return LayerProgress(
      layerNumber: json['layerNumber'],
      totalSections: json['totalSections'],
      sectionsDestroyed: json['sectionsDestroyed'] ?? 0,
      sectionsRevealed: json['sectionsRevealed'] ?? 0,
      firstAccessedAt: DateTime.parse(json['firstAccessedAt']),
      completedAt: json['completedAt'] != null 
          ? DateTime.parse(json['completedAt'])
          : null,
      rewards: Map<String, dynamic>.from(json['rewards'] ?? {}),
    );
  }
}

/// Breakfinity-specific power-ups and upgrades
class BreakfinityUpgrades {
  static const Map<String, Map<String, dynamic>> upgrades = {
    'damage_boost': {
      'name': 'Damage Boost',
      'description': 'Increases tap damage',
      'baseCost': 100,
      'costMultiplier': 1.5,
      'effect': 'damage_multiplier',
      'maxLevel': 50,
    },
    'auto_tapper': {
      'name': 'Auto Tapper',
      'description': 'Automatically taps sections',
      'baseCost': 500,
      'costMultiplier': 2.0,
      'effect': 'auto_tap_rate',
      'maxLevel': 20,
    },
    'layer_scanner': {
      'name': 'Layer Scanner',
      'description': 'Reveals hidden sections',
      'baseCost': 250,
      'costMultiplier': 1.8,
      'effect': 'reveal_radius',
      'maxLevel': 10,
    },
    'crystal_finder': {
      'name': 'Crystal Finder',
      'description': 'Increases crystal section spawn rate',
      'baseCost': 750,
      'costMultiplier': 2.2,
      'effect': 'crystal_spawn_rate',
      'maxLevel': 15,
    },
  };

  /// Calculate upgrade cost for a specific level
  static int getUpgradeCost(String upgradeId, int currentLevel) {
    final upgrade = upgrades[upgradeId];
    if (upgrade == null) return 0;
    
    final baseCost = upgrade['baseCost'] as int;
    final multiplier = upgrade['costMultiplier'] as double;
    
    return (baseCost * (multiplier * currentLevel)).round();
  }

  /// Get upgrade effect value for a specific level
  static double getUpgradeEffect(String upgradeId, int level) {
    switch (upgradeId) {
      case 'damage_boost':
        return 1.0 + (level * 0.2);
      case 'auto_tapper':
        return level * 0.5; // Taps per second
      case 'layer_scanner':
        return level * 10.0; // Reveal radius
      case 'crystal_finder':
        return level * 0.01; // Additional spawn chance
      default:
        return 0.0;
    }
  }
}
