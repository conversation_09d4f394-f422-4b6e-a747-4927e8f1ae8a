import 'package:flutter/material.dart';
import 'dart:math';

/// Types of rewards that can be discovered in Breakfinity
enum RewardType {
  tokens,
  powerUp,
  bonusKey,
  theme,
  secretGame,
  multiplier,
  autoTapper,
  specialEffect,
}

/// Rarity levels for rewards
enum RarityType {
  common,    // 70% - Basic tokens
  uncommon,  // 20% - Power-ups, small bonuses
  rare,      // 8% - Bonus keys, themes
  epic,      // 1.8% - Special effects, multipliers
  legendary, // 0.2% - Secret games, rare themes
}

/// Represents a reward discovered in Breakfinity
class BreakfinityReward {
  final String id;
  final String name;
  final String description;
  final RewardType type;
  final RarityType rarity;
  final IconData icon;
  final Color color;
  final Map<String, dynamic> properties;
  final int value;

  const BreakfinityReward({
    required this.id,
    required this.name,
    required this.description,
    required this.type,
    required this.rarity,
    required this.icon,
    required this.color,
    required this.properties,
    required this.value,
  });

  /// Get rarity color
  Color get rarityColor {
    switch (rarity) {
      case RarityType.common:
        return const Color(0xFF808080); // Gray
      case RarityType.uncommon:
        return const Color(0xFF00FF00); // Green
      case RarityType.rare:
        return const Color(0xFF0080FF); // Blue
      case RarityType.epic:
        return const Color(0xFF8000FF); // Purple
      case RarityType.legendary:
        return const Color(0xFFFFD700); // Gold
    }
  }

  /// Get rarity name
  String get rarityName {
    switch (rarity) {
      case RarityType.common:
        return 'Common';
      case RarityType.uncommon:
        return 'Uncommon';
      case RarityType.rare:
        return 'Rare';
      case RarityType.epic:
        return 'Epic';
      case RarityType.legendary:
        return 'Legendary';
    }
  }

  /// Convert to JSON for storage
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'type': type.name,
      'rarity': rarity.name,
      'properties': properties,
      'value': value,
    };
  }

  /// Create from JSON
  factory BreakfinityReward.fromJson(Map<String, dynamic> json) {
    return BreakfinityReward(
      id: json['id'],
      name: json['name'],
      description: json['description'],
      type: RewardType.values.firstWhere((e) => e.name == json['type']),
      rarity: RarityType.values.firstWhere((e) => e.name == json['rarity']),
      icon: _getIconForType(RewardType.values.firstWhere((e) => e.name == json['type'])),
      color: _getColorForType(RewardType.values.firstWhere((e) => e.name == json['type'])),
      properties: json['properties'] ?? {},
      value: json['value'] ?? 0,
    );
  }

  static IconData _getIconForType(RewardType type) {
    switch (type) {
      case RewardType.tokens:
        return Icons.monetization_on;
      case RewardType.powerUp:
        return Icons.flash_on;
      case RewardType.bonusKey:
        return Icons.vpn_key;
      case RewardType.theme:
        return Icons.palette;
      case RewardType.secretGame:
        return Icons.games;
      case RewardType.multiplier:
        return Icons.trending_up;
      case RewardType.autoTapper:
        return Icons.touch_app;
      case RewardType.specialEffect:
        return Icons.auto_awesome;
    }
  }

  static Color _getColorForType(RewardType type) {
    switch (type) {
      case RewardType.tokens:
        return const Color(0xFFFFD700);
      case RewardType.powerUp:
        return const Color(0xFF00FFFF);
      case RewardType.bonusKey:
        return const Color(0xFFFF8000);
      case RewardType.theme:
        return const Color(0xFFFF00FF);
      case RewardType.secretGame:
        return const Color(0xFF8000FF);
      case RewardType.multiplier:
        return const Color(0xFF00FF00);
      case RewardType.autoTapper:
        return const Color(0xFF0080FF);
      case RewardType.specialEffect:
        return const Color(0xFFFFFFFF);
    }
  }
}

/// Reward generation system with balanced drop rates
class BreakfinityRewardSystem {
  static final Random _random = Random();

  /// Generate a random reward based on layer and section type
  static BreakfinityReward? generateReward(int layer, String sectionType) {
    // Base drop chance increases with layer depth
    final baseDropChance = _calculateDropChance(layer, sectionType);
    
    if (_random.nextDouble() > baseDropChance) {
      return null; // No reward
    }

    final rarity = _rollRarity(layer, sectionType);
    final type = _rollRewardType(rarity, layer);
    
    return _createReward(type, rarity, layer);
  }

  static double _calculateDropChance(int layer, String sectionType) {
    double baseChance = 0.15; // 15% base chance
    
    // Increase chance with depth
    baseChance += (layer / 1000.0) * 0.1; // +10% per 1000 layers
    
    // Section type modifiers
    switch (sectionType) {
      case 'crystal':
        baseChance *= 2.0;
        break;
      case 'mystery':
        baseChance *= 3.0;
        break;
      case 'reinforced':
        baseChance *= 1.5;
        break;
    }
    
    return baseChance.clamp(0.0, 0.8); // Max 80% chance
  }

  static RarityType _rollRarity(int layer, String sectionType) {
    double roll = _random.nextDouble();
    
    // Base rarity chances
    double legendaryChance = 0.002; // 0.2%
    double epicChance = 0.018; // 1.8%
    double rareChance = 0.08; // 8%
    double uncommonChance = 0.2; // 20%
    // Common: 70%
    
    // Increase rare chances with depth
    final depthMultiplier = 1.0 + (layer / 500.0);
    legendaryChance *= depthMultiplier;
    epicChance *= depthMultiplier;
    rareChance *= depthMultiplier;
    
    // Section type bonuses
    if (sectionType == 'mystery') {
      legendaryChance *= 5.0;
      epicChance *= 3.0;
      rareChance *= 2.0;
    } else if (sectionType == 'crystal') {
      epicChance *= 2.0;
      rareChance *= 1.5;
    }
    
    if (roll < legendaryChance) return RarityType.legendary;
    if (roll < legendaryChance + epicChance) return RarityType.epic;
    if (roll < legendaryChance + epicChance + rareChance) return RarityType.rare;
    if (roll < legendaryChance + epicChance + rareChance + uncommonChance) return RarityType.uncommon;
    return RarityType.common;
  }

  static RewardType _rollRewardType(RarityType rarity, int layer) {
    switch (rarity) {
      case RarityType.common:
        return RewardType.tokens;
      
      case RarityType.uncommon:
        final types = [RewardType.powerUp, RewardType.tokens, RewardType.multiplier];
        return types[_random.nextInt(types.length)];
      
      case RarityType.rare:
        final types = [RewardType.bonusKey, RewardType.theme, RewardType.autoTapper];
        return types[_random.nextInt(types.length)];
      
      case RarityType.epic:
        final types = [RewardType.specialEffect, RewardType.multiplier, RewardType.theme];
        return types[_random.nextInt(types.length)];
      
      case RarityType.legendary:
        if (layer > 100 && _random.nextDouble() < 0.3) {
          return RewardType.secretGame;
        }
        final types = [RewardType.theme, RewardType.specialEffect, RewardType.autoTapper];
        return types[_random.nextInt(types.length)];
    }
  }

  static BreakfinityReward _createReward(RewardType type, RarityType rarity, int layer) {
    final id = '${type.name}_${rarity.name}_${DateTime.now().millisecondsSinceEpoch}';
    
    switch (type) {
      case RewardType.tokens:
        final baseAmount = 10 + (layer ~/ 10);
        final amount = (baseAmount * _getRarityMultiplier(rarity)).round();
        return BreakfinityReward(
          id: id,
          name: '$amount Tokens',
          description: 'Neon tokens for purchases',
          type: type,
          rarity: rarity,
          icon: Icons.monetization_on,
          color: const Color(0xFFFFD700),
          properties: {'amount': amount},
          value: amount,
        );
      
      case RewardType.bonusKey:
        final amount = rarity == RarityType.legendary ? 3 : rarity == RarityType.epic ? 2 : 1;
        return BreakfinityReward(
          id: id,
          name: '$amount Bonus Key${amount > 1 ? 's' : ''}',
          description: 'Unlock bonus games',
          type: type,
          rarity: rarity,
          icon: Icons.vpn_key,
          color: const Color(0xFFFF8000),
          properties: {'amount': amount},
          value: amount,
        );
      
      case RewardType.theme:
        return BreakfinityReward(
          id: id,
          name: 'Neon Theme',
          description: 'Unlock a new visual theme',
          type: type,
          rarity: rarity,
          icon: Icons.palette,
          color: const Color(0xFFFF00FF),
          properties: {'themeId': 'random'},
          value: 1,
        );
      
      case RewardType.secretGame:
        return BreakfinityReward(
          id: id,
          name: 'Secret Game',
          description: 'Unlock a hidden bonus game',
          type: type,
          rarity: rarity,
          icon: Icons.games,
          color: const Color(0xFF8000FF),
          properties: {'gameId': 'secret_${_random.nextInt(3) + 1}'},
          value: 1,
        );
      
      default:
        return BreakfinityReward(
          id: id,
          name: type.name,
          description: 'Special reward',
          type: type,
          rarity: rarity,
          icon: BreakfinityReward._getIconForType(type),
          color: BreakfinityReward._getColorForType(type),
          properties: {},
          value: 1,
        );
    }
  }

  static double _getRarityMultiplier(RarityType rarity) {
    switch (rarity) {
      case RarityType.common:
        return 1.0;
      case RarityType.uncommon:
        return 2.0;
      case RarityType.rare:
        return 5.0;
      case RarityType.epic:
        return 10.0;
      case RarityType.legendary:
        return 25.0;
    }
  }
}
