import 'dart:ui';

/// Represents a ball in the BrickBlast game with HP and physics properties
class BrickBlastBall {
  final String id;
  int hp;
  final int maxHp;
  Offset position;
  Offset velocity;
  final double radius;
  bool isActive;
  bool isLaunched;
  
  // Visual properties
  Color color;
  double glowIntensity;
  
  BrickBlastBall({
    required this.id,
    required this.hp,
    required this.maxHp,
    required this.position,
    this.velocity = Offset.zero,
    this.radius = 8.0,
    this.isActive = true,
    this.isLaunched = false,
    this.color = const Color(0xFF00FFFF), // Neon cyan
    this.glowIntensity = 1.0,
  });

  /// Create a copy of this ball with modified properties
  BrickBlastBall copyWith({
    String? id,
    int? hp,
    int? maxHp,
    Offset? position,
    Offset? velocity,
    double? radius,
    bool? isActive,
    bool? isLaunched,
    Color? color,
    double? glowIntensity,
  }) {
    return BrickBlastBall(
      id: id ?? this.id,
      hp: hp ?? this.hp,
      maxHp: maxHp ?? this.maxHp,
      position: position ?? this.position,
      velocity: velocity ?? this.velocity,
      radius: radius ?? this.radius,
      isActive: isActive ?? this.isActive,
      isLaunched: isLaunched ?? this.isLaunched,
      color: color ?? this.color,
      glowIntensity: glowIntensity ?? this.glowIntensity,
    );
  }

  /// Reduce ball HP by damage amount
  void takeDamage(int damage) {
    hp = (hp - damage).clamp(0, maxHp);
    if (hp <= 0) {
      isActive = false;
    }
    
    // Update visual properties based on HP
    _updateVisuals();
  }

  /// Update visual properties based on current HP
  void _updateVisuals() {
    final hpRatio = hp / maxHp;
    
    // Glow intensity decreases as HP decreases
    glowIntensity = hpRatio;
    
    // Color shifts from cyan to red as HP decreases
    if (hpRatio > 0.6) {
      color = Color.lerp(const Color(0xFF00FFFF), const Color(0xFF00FF00), (1 - hpRatio) * 2.5)!;
    } else if (hpRatio > 0.3) {
      color = Color.lerp(const Color(0xFF00FF00), const Color(0xFFFFFF00), (0.6 - hpRatio) * 3.33)!;
    } else {
      color = Color.lerp(const Color(0xFFFFFF00), const Color(0xFFFF0000), (0.3 - hpRatio) * 3.33)!;
    }
  }

  /// Get HP percentage (0.0 to 1.0)
  double get hpPercentage => hp / maxHp;

  /// Check if ball is depleted
  bool get isDepleted => hp <= 0;

  /// Launch the ball with given velocity
  void launch(Offset launchVelocity) {
    velocity = launchVelocity;
    isLaunched = true;
  }

  /// Update ball position based on velocity
  void updatePosition(double deltaTime) {
    if (isLaunched && isActive) {
      position = position + velocity * deltaTime;
    }
  }

  /// Convert to JSON for storage
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'hp': hp,
      'maxHp': maxHp,
      'position': {'x': position.dx, 'y': position.dy},
      'velocity': {'x': velocity.dx, 'y': velocity.dy},
      'radius': radius,
      'isActive': isActive,
      'isLaunched': isLaunched,
      'color': color.value,
      'glowIntensity': glowIntensity,
    };
  }

  /// Create from JSON
  factory BrickBlastBall.fromJson(Map<String, dynamic> json) {
    return BrickBlastBall(
      id: json['id'],
      hp: json['hp'],
      maxHp: json['maxHp'],
      position: Offset(json['position']['x'], json['position']['y']),
      velocity: Offset(json['velocity']['x'], json['velocity']['y']),
      radius: json['radius'],
      isActive: json['isActive'],
      isLaunched: json['isLaunched'],
      color: Color(json['color']),
      glowIntensity: json['glowIntensity'],
    );
  }
}
