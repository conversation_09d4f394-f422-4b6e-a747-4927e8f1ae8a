import 'package:flutter/material.dart';

/// Represents a challenge that can span across multiple games
class Challenge {
  final String id;
  final String title;
  final String description;
  final int reward;
  final int progress;
  final int target;
  final String icon;
  final bool completed;
  final ChallengeType type;
  final ChallengeDifficulty difficulty;
  final List<String> applicableGames; // Which games this challenge applies to
  final Map<String, dynamic> metadata; // Additional challenge-specific data
  final DateTime? completedAt;
  final bool isWeekly;

  const Challenge({
    required this.id,
    required this.title,
    required this.description,
    required this.reward,
    required this.progress,
    required this.target,
    required this.icon,
    required this.completed,
    required this.type,
    required this.difficulty,
    required this.applicableGames,
    this.metadata = const {},
    this.completedAt,
    this.isWeekly = true,
  });

  /// Create from JSON
  factory Challenge.fromJson(Map<String, dynamic> json) {
    return Challenge(
      id: json['id'],
      title: json['title'],
      description: json['description'],
      reward: json['reward'],
      progress: json['progress'],
      target: json['target'],
      icon: json['icon'],
      completed: json['completed'] ?? false,
      type: ChallengeType.values.firstWhere((t) => t.name == json['type']),
      difficulty: ChallengeDifficulty.values.firstWhere((d) => d.name == json['difficulty']),
      applicableGames: List<String>.from(json['applicableGames'] ?? []),
      metadata: Map<String, dynamic>.from(json['metadata'] ?? {}),
      completedAt: json['completedAt'] != null ? DateTime.parse(json['completedAt']) : null,
      isWeekly: json['isWeekly'] ?? true,
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'reward': reward,
      'progress': progress,
      'target': target,
      'icon': icon,
      'completed': completed,
      'type': type.name,
      'difficulty': difficulty.name,
      'applicableGames': applicableGames,
      'metadata': metadata,
      'completedAt': completedAt?.toIso8601String(),
      'isWeekly': isWeekly,
    };
  }

  /// Create a copy with updated values
  Challenge copyWith({
    String? id,
    String? title,
    String? description,
    int? reward,
    int? progress,
    int? target,
    String? icon,
    bool? completed,
    ChallengeType? type,
    ChallengeDifficulty? difficulty,
    List<String>? applicableGames,
    Map<String, dynamic>? metadata,
    DateTime? completedAt,
    bool? isWeekly,
  }) {
    return Challenge(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      reward: reward ?? this.reward,
      progress: progress ?? this.progress,
      target: target ?? this.target,
      icon: icon ?? this.icon,
      completed: completed ?? this.completed,
      type: type ?? this.type,
      difficulty: difficulty ?? this.difficulty,
      applicableGames: applicableGames ?? this.applicableGames,
      metadata: metadata ?? this.metadata,
      completedAt: completedAt ?? this.completedAt,
      isWeekly: isWeekly ?? this.isWeekly,
    );
  }

  /// Get progress percentage (0.0 to 1.0)
  double get progressPercentage => (progress / target).clamp(0.0, 1.0);

  /// Get the icon data for this challenge
  IconData get iconData {
    switch (icon) {
      case 'flag':
        return Icons.flag;
      case 'monetization_on':
        return Icons.monetization_on;
      case 'star':
        return Icons.star;
      case 'flash_on':
        return Icons.flash_on;
      case 'speed':
        return Icons.speed;
      case 'timer':
        return Icons.timer;
      case 'target':
        return Icons.gps_fixed;
      case 'combo':
        return Icons.trending_up;
      case 'perfect':
        return Icons.stars;
      case 'streak':
        return Icons.local_fire_department;
      case 'games':
        return Icons.games;
      case 'breakfinity':
        return Icons.grid_4x4;
      case 'neonrush':
        return Icons.flash_on;
      case 'bonus':
        return Icons.casino;
      case 'collection':
        return Icons.collections;
      case 'achievement':
        return Icons.emoji_events;
      default:
        return Icons.help;
    }
  }

  /// Get color based on difficulty
  Color get difficultyColor {
    switch (difficulty) {
      case ChallengeDifficulty.easy:
        return const Color(0xFF4CAF50); // Green
      case ChallengeDifficulty.medium:
        return const Color(0xFFFF9800); // Orange
      case ChallengeDifficulty.hard:
        return const Color(0xFFF44336); // Red
      case ChallengeDifficulty.expert:
        return const Color(0xFF9C27B0); // Purple
    }
  }
}

/// Types of challenges
enum ChallengeType {
  levelCompletion,
  tokenEarning,
  accuracy,
  speed,
  combo,
  streak,
  crossGame,
  breakfinity,
  neonrush,
  bonusGame,
  collection,
  survival,
  perfectRun,
}

/// Challenge difficulty levels
enum ChallengeDifficulty {
  easy,
  medium,
  hard,
  expert,
}

/// Game identifiers for challenges
class GameIdentifiers {
  static const String neonrush = 'neonrush';
  static const String breakfinity = 'breakfinity';
  static const String bonusGames = 'bonus_games';
  static const String luckyWheel = 'lucky_wheel';
  static const String all = 'all'; // For cross-game challenges
  
  static const List<String> allGames = [
    neonrush,
    breakfinity,
    bonusGames,
    luckyWheel,
  ];
}
