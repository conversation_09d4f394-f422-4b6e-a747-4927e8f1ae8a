import '../core/constants.dart';
import 'neon_theme.dart';

/// Configuration for a game level
class LevelConfig {
  final int levelNumber;
  final String title;
  final String description;
  final GameMode mode;
  final Duration duration;
  final int goal;
  final double speed;
  final NeonTheme theme;
  final DifficultyLevel difficulty;
  final int xpReward;
  final int tokenReward;
  final List<String> instructions;
  final Map<String, dynamic> modeSpecificConfig;

  const LevelConfig({
    required this.levelNumber,
    required this.title,
    required this.description,
    required this.mode,
    required this.duration,
    required this.goal,
    required this.speed,
    required this.theme,
    required this.difficulty,
    required this.xpReward,
    required this.tokenReward,
    required this.instructions,
    this.modeSpecificConfig = const {},
  });

  /// Creates a copy of this config with modified properties
  LevelConfig copyWith({
    int? levelNumber,
    String? title,
    String? description,
    GameMode? mode,
    Duration? duration,
    int? goal,
    double? speed,
    NeonTheme? theme,
    DifficultyLevel? difficulty,
    int? xpReward,
    int? tokenReward,
    List<String>? instructions,
    Map<String, dynamic>? modeSpecificConfig,
  }) {
    return LevelConfig(
      levelNumber: levelNumber ?? this.levelNumber,
      title: title ?? this.title,
      description: description ?? this.description,
      mode: mode ?? this.mode,
      duration: duration ?? this.duration,
      goal: goal ?? this.goal,
      speed: speed ?? this.speed,
      theme: theme ?? this.theme,
      difficulty: difficulty ?? this.difficulty,
      xpReward: xpReward ?? this.xpReward,
      tokenReward: tokenReward ?? this.tokenReward,
      instructions: instructions ?? this.instructions,
      modeSpecificConfig: modeSpecificConfig ?? this.modeSpecificConfig,
    );
  }

  /// Converts config to JSON
  Map<String, dynamic> toJson() {
    return {
      'levelNumber': levelNumber,
      'title': title,
      'description': description,
      'mode': mode.name,
      'duration': duration.inMilliseconds,
      'goal': goal,
      'speed': speed,
      'theme': theme.toJson(),
      'difficulty': difficulty.name,
      'xpReward': xpReward,
      'tokenReward': tokenReward,
      'instructions': instructions,
      'modeSpecificConfig': modeSpecificConfig,
    };
  }

  /// Creates config from JSON
  factory LevelConfig.fromJson(Map<String, dynamic> json) {
    return LevelConfig(
      levelNumber: json['levelNumber'],
      title: json['title'],
      description: json['description'],
      mode: GameMode.values.firstWhere((m) => m.name == json['mode']),
      duration: Duration(milliseconds: json['duration']),
      goal: json['goal'],
      speed: json['speed'].toDouble(),
      theme: NeonTheme.fromJson(json['theme']),
      difficulty: DifficultyLevel.values.firstWhere((d) => d.name == json['difficulty']),
      xpReward: json['xpReward'],
      tokenReward: json['tokenReward'],
      instructions: List<String>.from(json['instructions']),
      modeSpecificConfig: Map<String, dynamic>.from(json['modeSpecificConfig'] ?? {}),
    );
  }
}

/// Predefined level configurations
class LevelConfigs {
  static final List<LevelConfig> allLevels = [
    // Level 1: Tap
    LevelConfig(
      levelNumber: 1,
      title: 'Neon Touch',
      description: 'Tap the glowing dots to score points',
      mode: GameMode.tap,
      duration: const Duration(seconds: 30),
      goal: 50,
      speed: 1.0,
      theme: NeonThemes.cyberBlue,
      difficulty: DifficultyLevel.easy,
      xpReward: 100,
      tokenReward: 25,
      instructions: [
        'Tap the glowing neon dots',
        'Score 50 points to complete',
        'Don\'t let them disappear!'
      ],
      modeSpecificConfig: {
        'dotSize': 60.0,
        'spawnRate': 1.5,
        'dotLifetime': 3.0,
      },
    ),

    // Level 2: Tap (changed from Swipe - swipe now requires purchase)
    LevelConfig(
      levelNumber: 2,
      title: 'Neon Precision',
      description: 'Tap the moving targets with precision',
      mode: GameMode.tap,
      duration: const Duration(seconds: 45),
      goal: 75,
      speed: 1.2,
      theme: NeonThemes.neonPink,
      difficulty: DifficultyLevel.easy,
      xpReward: 120,
      tokenReward: 30,
      instructions: [
        'Tap the moving targets',
        'Score 75 points to complete',
        'Quick reflexes needed!'
      ],
      modeSpecificConfig: {
        'spawnRate': 2.0,
        'dotLifetime': 4.0,
      },
    ),

    // Level 3: Tap (changed from Hold)
    LevelConfig(
      levelNumber: 3,
      title: 'Neon Focus',
      description: 'Tap targets that appear and disappear quickly',
      mode: GameMode.tap,
      duration: const Duration(seconds: 60),
      goal: 100,
      speed: 1.0,
      theme: NeonThemes.electricGreen,
      difficulty: DifficultyLevel.medium,
      xpReward: 150,
      tokenReward: 35,
      instructions: [
        'Tap targets before they disappear',
        'Score 100 points to complete',
        'Stay focused!'
      ],
      modeSpecificConfig: {
        'spawnRate': 1.8,
        'dotLifetime': 2.5,
      },
    ),

    // Level 4: Avoid
    LevelConfig(
      levelNumber: 4,
      title: 'Neon Dodge',
      description: 'Dodge incoming neon enemies',
      mode: GameMode.avoid,
      duration: const Duration(seconds: 60),
      goal: 150,
      speed: 1.5,
      theme: NeonThemes.fireOrange,
      difficulty: DifficultyLevel.medium,
      xpReward: 180,
      tokenReward: 40,
      instructions: [
        'Drag your orb to avoid enemies',
        'Score 150 points to complete',
        'Don\'t get hit!'
      ],
      modeSpecificConfig: {
        'enemySpeed': 150.0,
        'enemySpawnRate': 1.0,
        'playerSize': 40.0,
        'enemySize': 30.0,
      },
    ),

    // Level 5: Memory (Boss Level - Easy)
    LevelConfig(
      levelNumber: 5,
      title: 'Neon Memory Boss',
      description: 'Defeat the Memory Boss',
      mode: GameMode.memory,
      duration: const Duration(seconds: 90),
      goal: 200,
      speed: 1.0,
      theme: NeonThemes.purpleHaze,
      difficulty: DifficultyLevel.medium,
      xpReward: 300,
      tokenReward: 75,
      instructions: [
        'Watch the sequence of colors',
        'Repeat it back correctly',
        'Score 200 points to defeat the boss'
      ],
      modeSpecificConfig: {
        'sequenceLength': 3,
        'showTime': 1.0,
        'maxSequenceLength': 6,
        'isBoss': true,
        'bossType': 'easy',
      },
    ),

    // Level 6: Reaction
    LevelConfig(
      levelNumber: 6,
      title: 'Neon Reaction',
      description: 'Tap only when the signal is green',
      mode: GameMode.reaction,
      duration: const Duration(seconds: 60),
      goal: 250,
      speed: 1.3,
      theme: NeonThemes.electricGreen,
      difficulty: DifficultyLevel.medium,
      xpReward: 220,
      tokenReward: 50,
      instructions: [
        'Wait for the green signal',
        'Tap immediately when green',
        'Score 250 points to complete'
      ],
      modeSpecificConfig: {
        'minWaitTime': 1.0,
        'maxWaitTime': 4.0,
        'reactionWindow': 0.5,
      },
    ),

    // Level 7: Tap (changed from Drag)
    LevelConfig(
      levelNumber: 7,
      title: 'Neon Speed',
      description: 'Tap fast-moving targets',
      mode: GameMode.tap,
      duration: const Duration(seconds: 75),
      goal: 300,
      speed: 1.4,
      theme: NeonThemes.neonPink,
      difficulty: DifficultyLevel.hard,
      xpReward: 250,
      tokenReward: 55,
      instructions: [
        'Tap fast-moving targets',
        'Score 300 points to complete',
        'Speed matters!'
      ],
      modeSpecificConfig: {
        'spawnRate': 3.0,
        'dotLifetime': 2.0,
      },
    ),

    // Level 8: Spin
    LevelConfig(
      levelNumber: 8,
      title: 'Neon Spin',
      description: 'Rotate rings to match gaps',
      mode: GameMode.spin,
      duration: const Duration(seconds: 90),
      goal: 350,
      speed: 1.6,
      theme: NeonThemes.goldRush,
      difficulty: DifficultyLevel.hard,
      xpReward: 280,
      tokenReward: 60,
      instructions: [
        'Rotate the rings',
        'Align gaps to let orbs pass',
        'Score 350 points to complete'
      ],
      modeSpecificConfig: {
        'ringCount': 3,
        'rotationSpeed': 90.0,
        'gapSize': 60.0,
      },
    ),

    // Level 9: Tap (changed from Deflect)
    LevelConfig(
      levelNumber: 9,
      title: 'Neon Precision',
      description: 'Tap small, fast targets with precision',
      mode: GameMode.tap,
      duration: const Duration(seconds: 75),
      goal: 400,
      speed: 1.8,
      theme: NeonThemes.iceBlue,
      difficulty: DifficultyLevel.hard,
      xpReward: 320,
      tokenReward: 65,
      instructions: [
        'Tap small, fast targets',
        'Score 400 points to complete',
        'Precision is key!'
      ],
      modeSpecificConfig: {
        'spawnRate': 3.5,
        'dotLifetime': 1.5,
      },
    ),

    // Level 10: Boss Fight (Hard)
    LevelConfig(
      levelNumber: 10,
      title: 'Neon Chaos Boss',
      description: 'Defeat the Chaos Boss',
      mode: GameMode.survive,
      duration: const Duration(seconds: 90),
      goal: 500,
      speed: 2.0,
      theme: NeonThemes.rainbow,
      difficulty: DifficultyLevel.expert,
      xpReward: 750,
      tokenReward: 150,
      instructions: [
        'Survive the chaos boss',
        'Score 500 points to defeat the boss',
        'Use all your skills!'
      ],
      modeSpecificConfig: {
        'chaosIntensity': 1.0,
        'effectCount': 5,
        'survivalBonus': 10,
        'isBoss': true,
        'bossType': 'hard',
      },
    ),
  ];

  /// Get level config by number
  static LevelConfig? getLevelConfig(int levelNumber) {
    try {
      return allLevels.firstWhere((level) => level.levelNumber == levelNumber);
    } catch (e) {
      return null;
    }
  }

  /// Get all unlocked levels for a player level
  static List<LevelConfig> getUnlockedLevels(int playerLevel) {
    return allLevels.where((level) => level.levelNumber <= playerLevel).toList();
  }
}
