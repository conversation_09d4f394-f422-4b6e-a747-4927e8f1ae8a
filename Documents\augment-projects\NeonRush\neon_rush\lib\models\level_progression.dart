import '../core/constants.dart';
import 'neon_theme.dart';

/// Progressive level difficulty configurations for all game modes
class LevelProgression {
  
  /// Get level configuration for Tap Target game
  static TapTargetLevelConfig getTapTargetConfig(int level) {
    final configs = [
      TapTargetLevelConfig(level: 1, duration: 30, targetsPerSec: 0.8, pointRange: [5, 10]),
      TapTargetLevelConfig(level: 2, duration: 30, targetsPerSec: 1.0, pointRange: [5, 15]),
      TapTargetLevelConfig(level: 3, duration: 28, targetsPerSec: 1.2, pointRange: [5, 15], multiplierUnlock: MultiplierUnlock(multiplier: 2, duration: 20)),
      TapTargetLevelConfig(level: 4, duration: 28, targetsPerSec: 1.5, pointRange: [5, 15], multiplierUnlock: MultiplierUnlock(multiplier: 2, duration: 25)),
      TapTargetLevelConfig(level: 5, duration: 26, targetsPerSec: 1.7, pointRange: [10, 20], multiplierUnlock: MultiplierUnlock(multiplier: 2, duration: 30)),
      TapTargetLevelConfig(level: 6, duration: 26, targetsPerSec: 2.0, pointRange: [10, 20], multiplierUnlock: MultiplierUnlock(multiplier: 3, duration: 15)),
      TapTargetLevelConfig(level: 7, duration: 24, targetsPerSec: 2.2, pointRange: [10, 25], multiplierUnlock: MultiplierUnlock(multiplier: 3, duration: 20)),
      TapTargetLevelConfig(level: 8, duration: 24, targetsPerSec: 2.5, pointRange: [15, 25], multiplierUnlock: MultiplierUnlock(multiplier: 3, duration: 25)),
      TapTargetLevelConfig(level: 9, duration: 22, targetsPerSec: 2.7, pointRange: [15, 30], multiplierUnlock: MultiplierUnlock(multiplier: 4, duration: 15)),
      TapTargetLevelConfig(level: 10, duration: 20, targetsPerSec: 3.0, pointRange: [15, 35], multiplierUnlock: MultiplierUnlock(multiplier: 4, duration: 20)),
    ];
    
    return configs[level - 1];
  }
  
  /// Get level configuration for Swipe to Avoid game
  static SwipeAvoidLevelConfig getSwipeAvoidConfig(int level) {
    final configs = [
      SwipeAvoidLevelConfig(level: 1, duration: 45, obstaclesPerSec: 0.5, speedMultiplier: 1.0),
      SwipeAvoidLevelConfig(level: 2, duration: 45, obstaclesPerSec: 0.7, speedMultiplier: 1.1),
      SwipeAvoidLevelConfig(level: 3, duration: 43, obstaclesPerSec: 0.9, speedMultiplier: 1.2),
      SwipeAvoidLevelConfig(level: 4, duration: 43, obstaclesPerSec: 1.1, speedMultiplier: 1.3, shieldUnlock: ShieldUnlock(duration: 5)),
      SwipeAvoidLevelConfig(level: 5, duration: 41, obstaclesPerSec: 1.3, speedMultiplier: 1.4, shieldUnlock: ShieldUnlock(duration: 6)),
      SwipeAvoidLevelConfig(level: 6, duration: 41, obstaclesPerSec: 1.5, speedMultiplier: 1.5, shieldUnlock: ShieldUnlock(duration: 7)),
      SwipeAvoidLevelConfig(level: 7, duration: 39, obstaclesPerSec: 1.7, speedMultiplier: 1.6),
      SwipeAvoidLevelConfig(level: 8, duration: 39, obstaclesPerSec: 1.9, speedMultiplier: 1.7),
      SwipeAvoidLevelConfig(level: 9, duration: 37, obstaclesPerSec: 2.1, speedMultiplier: 1.8),
      SwipeAvoidLevelConfig(level: 10, duration: 35, obstaclesPerSec: 2.3, speedMultiplier: 2.0, shieldUnlock: ShieldUnlock(duration: 8)),
    ];
    
    return configs[level - 1];
  }
  
  /// Get level configuration for Hold the Glow game
  static HoldGlowLevelConfig getHoldGlowConfig(int level) {
    final configs = [
      HoldGlowLevelConfig(level: 1, duration: 60, holdRate: 1.0, decayRate: 0.5),
      HoldGlowLevelConfig(level: 2, duration: 58, holdRate: 1.1, decayRate: 0.6),
      HoldGlowLevelConfig(level: 3, duration: 56, holdRate: 1.2, decayRate: 0.6, timeExtension: TimeExtension(seconds: 5, triggerPercent: 50)),
      HoldGlowLevelConfig(level: 4, duration: 56, holdRate: 1.3, decayRate: 0.7, timeExtension: TimeExtension(seconds: 5, triggerPercent: 60)),
      HoldGlowLevelConfig(level: 5, duration: 54, holdRate: 1.4, decayRate: 0.8, timeExtension: TimeExtension(seconds: 7, triggerPercent: 60)),
      HoldGlowLevelConfig(level: 6, duration: 54, holdRate: 1.5, decayRate: 0.9, timeExtension: TimeExtension(seconds: 7, triggerPercent: 65)),
      HoldGlowLevelConfig(level: 7, duration: 52, holdRate: 1.6, decayRate: 1.0, timeExtension: TimeExtension(seconds: 10, triggerPercent: 70)),
      HoldGlowLevelConfig(level: 8, duration: 52, holdRate: 1.7, decayRate: 1.1, timeExtension: TimeExtension(seconds: 10, triggerPercent: 75)),
      HoldGlowLevelConfig(level: 9, duration: 50, holdRate: 1.8, decayRate: 1.2),
      HoldGlowLevelConfig(level: 10, duration: 50, holdRate: 2.0, decayRate: 1.3),
    ];
    
    return configs[level - 1];
  }
  
  /// Get level configuration for Avoid Neon Spikes game
  static AvoidSpikesLevelConfig getAvoidSpikesConfig(int level) {
    final configs = [
      AvoidSpikesLevelConfig(level: 1, duration: 60, spikesPerSec: 0.4, speedMultiplier: 1.0),
      AvoidSpikesLevelConfig(level: 2, duration: 60, spikesPerSec: 0.6, speedMultiplier: 1.1),
      AvoidSpikesLevelConfig(level: 3, duration: 58, spikesPerSec: 0.8, speedMultiplier: 1.2),
      AvoidSpikesLevelConfig(level: 4, duration: 58, spikesPerSec: 1.0, speedMultiplier: 1.3, freezeUnlock: FreezeUnlock(type: FreezeType.time)),
      AvoidSpikesLevelConfig(level: 5, duration: 56, spikesPerSec: 1.2, speedMultiplier: 1.4),
      AvoidSpikesLevelConfig(level: 6, duration: 56, spikesPerSec: 1.4, speedMultiplier: 1.5),
      AvoidSpikesLevelConfig(level: 7, duration: 54, spikesPerSec: 1.6, speedMultiplier: 1.6),
      AvoidSpikesLevelConfig(level: 8, duration: 54, spikesPerSec: 1.8, speedMultiplier: 1.7),
      AvoidSpikesLevelConfig(level: 9, duration: 52, spikesPerSec: 2.0, speedMultiplier: 1.8),
      AvoidSpikesLevelConfig(level: 10, duration: 50, spikesPerSec: 2.2, speedMultiplier: 2.0, freezeUnlock: FreezeUnlock(type: FreezeType.spike)),
    ];
    
    return configs[level - 1];
  }
  
  /// Get reward configuration based on difficulty tier
  static RewardConfig getRewardConfig(int level) {
    if (level <= 3) {
      return const RewardConfig(
        tier: DifficultyTier.easy,
        baseXP: 100,
        baseTokens: 25,
        timeBonusXP: 20,
        accuracyBonusTokens: 5,
      );
    } else if (level <= 6) {
      return const RewardConfig(
        tier: DifficultyTier.medium,
        baseXP: 150,
        baseTokens: 35,
        timeBonusXP: 30,
        accuracyBonusTokens: 7,
      );
    } else {
      return const RewardConfig(
        tier: DifficultyTier.hard,
        baseXP: 200,
        baseTokens: 45,
        timeBonusXP: 40,
        accuracyBonusTokens: 10,
      );
    }
  }
}

/// Configuration for Tap Target game levels
class TapTargetLevelConfig {
  final int level;
  final int duration; // seconds
  final double targetsPerSec;
  final List<int> pointRange; // [min, max]
  final MultiplierUnlock? multiplierUnlock;
  
  const TapTargetLevelConfig({
    required this.level,
    required this.duration,
    required this.targetsPerSec,
    required this.pointRange,
    this.multiplierUnlock,
  });
  
  int get goal => (duration * targetsPerSec * (pointRange[0] + pointRange[1]) / 2 * 0.7).round();
  Duration get spawnInterval => Duration(milliseconds: (1000 / targetsPerSec).round());
}

/// Configuration for Swipe to Avoid game levels
class SwipeAvoidLevelConfig {
  final int level;
  final int duration; // seconds
  final double obstaclesPerSec;
  final double speedMultiplier;
  final ShieldUnlock? shieldUnlock;
  
  const SwipeAvoidLevelConfig({
    required this.level,
    required this.duration,
    required this.obstaclesPerSec,
    required this.speedMultiplier,
    this.shieldUnlock,
  });
  
  Duration get spawnInterval => Duration(milliseconds: (1000 / obstaclesPerSec).round());
}

/// Configuration for Hold the Glow game levels
class HoldGlowLevelConfig {
  final int level;
  final int duration; // seconds
  final double holdRate; // progress per second when holding
  final double decayRate; // progress lost per second when not holding
  final TimeExtension? timeExtension;
  
  const HoldGlowLevelConfig({
    required this.level,
    required this.duration,
    required this.holdRate,
    required this.decayRate,
    this.timeExtension,
  });
}

/// Configuration for Avoid Neon Spikes game levels
class AvoidSpikesLevelConfig {
  final int level;
  final int duration; // seconds
  final double spikesPerSec;
  final double speedMultiplier;
  final FreezeUnlock? freezeUnlock;
  
  const AvoidSpikesLevelConfig({
    required this.level,
    required this.duration,
    required this.spikesPerSec,
    required this.speedMultiplier,
    this.freezeUnlock,
  });
  
  Duration get spawnInterval => Duration(milliseconds: (1000 / spikesPerSec).round());
}

/// Reward configuration for different difficulty tiers
class RewardConfig {
  final DifficultyTier tier;
  final int baseXP;
  final int baseTokens;
  final int timeBonusXP; // Bonus for >90% time efficiency
  final int accuracyBonusTokens; // Bonus for >80% accuracy
  
  const RewardConfig({
    required this.tier,
    required this.baseXP,
    required this.baseTokens,
    required this.timeBonusXP,
    required this.accuracyBonusTokens,
  });
}

enum DifficultyTier { easy, medium, hard }

/// Special unlock configurations
class MultiplierUnlock {
  final int multiplier;
  final int duration; // seconds
  
  const MultiplierUnlock({
    required this.multiplier,
    required this.duration,
  });
}

class ShieldUnlock {
  final int duration; // seconds
  
  const ShieldUnlock({
    required this.duration,
  });
}

class TimeExtension {
  final int seconds;
  final int triggerPercent; // Progress percentage that triggers extension
  
  const TimeExtension({
    required this.seconds,
    required this.triggerPercent,
  });
}

class FreezeUnlock {
  final FreezeType type;
  
  const FreezeUnlock({
    required this.type,
  });
}

enum FreezeType { time, spike }
