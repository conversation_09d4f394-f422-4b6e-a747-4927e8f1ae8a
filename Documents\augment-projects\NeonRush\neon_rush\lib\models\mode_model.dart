import '../constants.dart';

/// Represents a game mode that can be unlocked with tokens
class Mode {
  final String id;
  final String name;
  final String description;
  final String detailedDescription;
  final int cost;
  final bool unlocked;
  final GameMode gameMode;
  final String iconPath;
  final String bannerPath;
  final List<String> features;
  final Map<String, dynamic> config;

  const Mode({
    required this.id,
    required this.name,
    required this.description,
    required this.detailedDescription,
    required this.cost,
    required this.unlocked,
    required this.gameMode,
    required this.iconPath,
    required this.bannerPath,
    required this.features,
    this.config = const {},
  });

  /// Creates a copy with modified properties
  Mode copyWith({
    String? id,
    String? name,
    String? description,
    String? detailedDescription,
    int? cost,
    bool? unlocked,
    GameMode? gameMode,
    String? iconPath,
    String? bannerPath,
    List<String>? features,
    Map<String, dynamic>? config,
  }) {
    return Mode(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      detailedDescription: detailedDescription ?? this.detailedDescription,
      cost: cost ?? this.cost,
      unlocked: unlocked ?? this.unlocked,
      gameMode: gameMode ?? this.gameMode,
      iconPath: iconPath ?? this.iconPath,
      bannerPath: bannerPath ?? this.bannerPath,
      features: features ?? this.features,
      config: config ?? this.config,
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'detailedDescription': detailedDescription,
      'cost': cost,
      'unlocked': unlocked,
      'gameMode': gameMode.name,
      'iconPath': iconPath,
      'bannerPath': bannerPath,
      'features': features,
      'config': config,
    };
  }

  /// Create from JSON
  factory Mode.fromJson(Map<String, dynamic> json) {
    return Mode(
      id: json['id'],
      name: json['name'],
      description: json['description'],
      detailedDescription: json['detailedDescription'],
      cost: json['cost'],
      unlocked: json['unlocked'],
      gameMode: GameMode.values.firstWhere((m) => m.name == json['gameMode']),
      iconPath: json['iconPath'],
      bannerPath: json['bannerPath'],
      features: List<String>.from(json['features']),
      config: Map<String, dynamic>.from(json['config'] ?? {}),
    );
  }
}

/// Predefined game modes available for unlock
class GameModes {
  static final List<Mode> allModes = [
    Mode(
      id: 'endless',
      name: 'Endless Mode',
      description: 'Infinite neon targets, endless fun',
      detailedDescription: 'Test your endurance in this never-ending challenge. Targets spawn continuously with increasing difficulty. How long can you survive the neon onslaught?',
      cost: GameConstants.endlessModeCost,
      unlocked: false,
      gameMode: GameMode.endless,
      iconPath: 'assets/icons/endless_mode.svg',
      bannerPath: 'assets/banners/endless_banner.rive',
      features: [
        'Infinite gameplay',
        'Progressive difficulty',
        'Global leaderboards',
        'Power-up spawns'
      ],
      config: {
        'difficultyScaling': 1.05,
        'powerUpChance': 0.1,
        'scoreMultiplier': 2.0,
      },
    ),
    
    Mode(
      id: 'time_attack',
      name: 'Time Attack',
      description: 'Race against the clock',
      detailedDescription: 'Fast-paced action where every second counts. Hit as many targets as possible before time runs out. Perfect for quick gaming sessions!',
      cost: GameConstants.timeAttackCost,
      unlocked: false,
      gameMode: GameMode.timeAttack,
      iconPath: 'assets/icons/time_attack.svg',
      bannerPath: 'assets/banners/time_attack_banner.rive',
      features: [
        '60-second rounds',
        'Rapid target spawning',
        'Time bonus multipliers',
        'Speed achievements'
      ],
      config: {
        'duration': 60,
        'spawnRate': 0.5,
        'timeBonus': true,
      },
    ),
    
    Mode(
      id: 'puzzle',
      name: 'Puzzle Mode',
      description: 'Strategic neon challenges',
      detailedDescription: 'Think before you tap! Solve intricate puzzles using logic and pattern recognition. Each level presents a unique challenge that tests your mind.',
      cost: GameConstants.puzzleModeCost,
      unlocked: false,
      gameMode: GameMode.puzzle,
      iconPath: 'assets/icons/puzzle_mode.svg',
      bannerPath: 'assets/banners/puzzle_banner.rive',
      features: [
        'Logic-based challenges',
        'Pattern recognition',
        'Multiple solutions',
        'Brain training'
      ],
      config: {
        'puzzleTypes': ['sequence', 'pattern', 'logic'],
        'hintSystem': true,
        'timeLimit': false,
      },
    ),
    
    Mode(
      id: 'mirror',
      name: 'Mirror Mode',
      description: 'Inverted controls challenge',
      detailedDescription: 'Everything is backwards! Your controls are inverted, creating a mind-bending challenge that will test your adaptability and reflexes.',
      cost: GameConstants.mirrorModeCost,
      unlocked: false,
      gameMode: GameMode.mirror,
      iconPath: 'assets/icons/mirror_mode.svg',
      bannerPath: 'assets/banners/mirror_banner.rive',
      features: [
        'Inverted controls',
        'Mind-bending gameplay',
        'Adaptation challenge',
        'Unique mechanics'
      ],
      config: {
        'invertX': true,
        'invertY': true,
        'adaptationTime': 5.0,
      },
    ),
    
    Mode(
      id: 'hardcore',
      name: 'Hardcore Mode',
      description: 'Ultimate neon challenge',
      detailedDescription: 'The most challenging mode in NeonRush. One mistake and it\'s over. Only the most skilled players can master this brutal test of precision and endurance.',
      cost: GameConstants.hardcoreModeCost,
      unlocked: false,
      gameMode: GameMode.hardcore,
      iconPath: 'assets/icons/hardcore_mode.svg',
      bannerPath: 'assets/banners/hardcore_banner.rive',
      features: [
        'One life only',
        'Maximum difficulty',
        'Elite leaderboards',
        'Prestige rewards'
      ],
      config: {
        'lives': 1,
        'difficultyMultiplier': 2.0,
        'noSecondChances': true,
      },
    ),
    
    Mode(
      id: 'seasonal',
      name: 'Seasonal Event',
      description: 'Limited-time special events',
      detailedDescription: 'Exclusive seasonal content with unique themes, special rewards, and limited-time challenges. Don\'t miss out on these rare opportunities!',
      cost: GameConstants.seasonalModeCost,
      unlocked: false,
      gameMode: GameMode.seasonal,
      iconPath: 'assets/icons/seasonal_mode.svg',
      bannerPath: 'assets/banners/seasonal_banner.rive',
      features: [
        'Limited-time events',
        'Exclusive rewards',
        'Themed content',
        'Special achievements'
      ],
      config: {
        'eventDuration': 7, // days
        'exclusiveRewards': true,
        'themeRotation': true,
      },
    ),
  ];

  /// Get mode by ID
  static Mode? getModeById(String id) {
    try {
      return allModes.firstWhere((mode) => mode.id == id);
    } catch (e) {
      return null;
    }
  }

  /// Get all unlocked modes
  static List<Mode> getUnlockedModes(List<String> unlockedIds) {
    return allModes.where((mode) => unlockedIds.contains(mode.id)).toList();
  }

  /// Get all locked modes
  static List<Mode> getLockedModes(List<String> unlockedIds) {
    return allModes.where((mode) => !unlockedIds.contains(mode.id)).toList();
  }

  /// Calculate bundle price with discount
  static int getBundlePrice(List<String> modeIds) {
    final totalCost = modeIds
        .map((id) => getModeById(id)?.cost ?? 0)
        .fold(0, (sum, cost) => sum + cost);
    
    final discount = (totalCost * GameConstants.bundleDiscount / 100).round();
    return totalCost - discount;
  }
}
