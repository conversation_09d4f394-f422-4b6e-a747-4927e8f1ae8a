import 'dart:math';
import 'package:flutter/material.dart';
import '../core/constants.dart';

/// Target types as specified in v2.0 documentation
enum TargetType {
  easyBall,     // Stationary, 5-10 points, 3s lifetime
  mediumBall,   // Falling, 15 points, 5s lifetime
  hardBall,     // Stationary, splits into 4 pieces (20 points each)
  redEnemy,     // Random movement, -15 penalty, explodes
  yellowEnemy,  // Stationary, -15s timer penalty
}

/// Target movement patterns
enum TargetMovement {
  stationary,   // Stays in place
  falling,      // Falls downward
  random,       // Random movement pattern
  exploding,    // Explosion animation
}

/// Represents a game target with v2.0 specifications
class GameTarget {
  final String id;
  final TargetType type;
  TargetMovement movement;
  
  // Position and physics
  Offset position;
  Offset velocity;
  double rotation;
  double scale;
  
  // Visual properties
  final Color color;
  final double baseSize;
  final IconData? icon;
  
  // Game properties
  final int baseScore;
  final int maxScore;
  final int penalty;
  final Duration lifetime;
  final bool explodes;
  final bool affectsTimer;
  final int splitCount;
  final int splitScore;
  
  // State
  final DateTime createdAt;
  bool isActive;
  bool isHit;
  bool hasExploded;
  double opacity;
  List<GameTarget>? splitTargets;
  
  // Animation properties
  double pulsePhase;
  double glowIntensity;

  GameTarget({
    required this.id,
    required this.type,
    required this.position,
    this.movement = TargetMovement.stationary,
    this.velocity = const Offset(0, 0),
    this.rotation = 0.0,
    this.scale = 1.0,
    this.opacity = 1.0,
    this.pulsePhase = 0.0,
    this.glowIntensity = 1.0,
    this.splitTargets,
  }) : 
    // Set properties based on target type
    color = _getColorForType(type),
    baseSize = _getSizeForType(type),
    icon = _getIconForType(type),
    baseScore = _getBaseScoreForType(type),
    maxScore = _getMaxScoreForType(type),
    penalty = _getPenaltyForType(type),
    lifetime = _getLifetimeForType(type),
    explodes = _getExplodesForType(type),
    affectsTimer = _getAffectsTimerForType(type),
    splitCount = _getSplitCountForType(type),
    splitScore = _getSplitScoreForType(type),
    createdAt = DateTime.now(),
    isActive = true,
    isHit = false,
    hasExploded = false;

  /// Update target state
  void update(double deltaTime) {
    if (!isActive) return;

    // Update animation properties
    pulsePhase += deltaTime * 2.0; // 2 Hz pulse
    
    // Update position based on movement
    switch (movement) {
      case TargetMovement.falling:
        position = Offset(
          position.dx,
          position.dy + velocity.dy * deltaTime,
        );
        break;
      case TargetMovement.random:
        _updateRandomMovement(deltaTime);
        break;
      case TargetMovement.exploding:
        _updateExplosion(deltaTime);
        break;
      default:
        break;
    }

    // Check lifetime
    final age = DateTime.now().difference(createdAt);
    if (age > lifetime) {
      if (type == TargetType.redEnemy && !hasExploded) {
        _triggerExplosion();
      } else {
        isActive = false;
      }
    }

    // Update visual effects based on age
    final ageRatio = age.inMilliseconds / lifetime.inMilliseconds;
    if (ageRatio > 0.7) {
      // Start fading/warning in last 30% of lifetime
      opacity = 1.0 - ((ageRatio - 0.7) / 0.3) * 0.5;
      glowIntensity = 1.0 + sin(pulsePhase * 4) * 0.5; // Faster pulse when about to expire
    }
  }

  void _updateRandomMovement(double deltaTime) {
    final random = Random();
    
    // Change direction occasionally
    if (random.nextDouble() < 0.02) {
      final speed = 50.0 + random.nextDouble() * 100.0;
      final angle = random.nextDouble() * 2 * pi;
      velocity = Offset(cos(angle) * speed, sin(angle) * speed);
    }

    // Update position
    position = Offset(
      position.dx + velocity.dx * deltaTime,
      position.dy + velocity.dy * deltaTime,
    );

    // Bounce off screen edges
    if (position.dx < 0 || position.dx > GameConstants.gameWidth) {
      velocity = Offset(-velocity.dx, velocity.dy);
    }
    if (position.dy < 100 || position.dy > GameConstants.gameHeight - 100) {
      velocity = Offset(velocity.dx, -velocity.dy);
    }
  }

  void _updateExplosion(double deltaTime) {
    scale += deltaTime * 3.0; // Expand quickly
    opacity -= deltaTime * 2.0; // Fade out
    
    if (opacity <= 0) {
      isActive = false;
    }
  }

  void _triggerExplosion() {
    hasExploded = true;
    movement = TargetMovement.exploding;
    scale = 1.0;
    opacity = 1.0;
  }

  /// Mark target as hit and handle special behaviors
  void hit() {
    isHit = true;
    
    switch (type) {
      case TargetType.hardBall:
        _splitIntoFragments();
        break;
      case TargetType.redEnemy:
        _triggerExplosion();
        break;
      default:
        isActive = false;
        break;
    }
  }

  void _splitIntoFragments() {
    if (splitTargets != null) return; // Already split
    
    splitTargets = [];
    final random = Random();
    
    for (int i = 0; i < splitCount; i++) {
      final angle = (i / splitCount) * 2 * pi + random.nextDouble() * 0.5;
      final speed = 100.0 + random.nextDouble() * 50.0;
      final offset = Offset(cos(angle) * 30, sin(angle) * 30);
      
      final fragment = GameTarget(
        id: '${id}_fragment_$i',
        type: TargetType.easyBall, // Fragments are like easy balls
        position: position + offset,
        velocity: Offset(cos(angle) * speed, sin(angle) * speed),
        movement: TargetMovement.falling,
        scale: 0.6, // Smaller fragments
      );
      
      splitTargets!.add(fragment);
    }
    
    isActive = false; // Original target is destroyed
  }

  /// Check if point is within target bounds
  bool containsPoint(Offset point) {
    final distance = (point - position).distance;
    final hitRadius = (baseSize * scale) / 2;
    return distance <= hitRadius;
  }

  /// Get current display color with effects
  Color getDisplayColor() {
    if (!isActive) return color.withValues(alpha: 0.3);
    if (isHit && type != TargetType.redEnemy) return Colors.white;
    
    // Apply opacity and glow effects
    final effectiveOpacity = opacity.clamp(0.0, 1.0);
    return color.withValues(alpha: effectiveOpacity);
  }

  /// Get current size with scale effects
  double getCurrentSize() {
    return baseSize * scale;
  }

  /// Get score for hitting this target
  int getScore() {
    switch (type) {
      case TargetType.easyBall:
        // Score varies from baseScore to maxScore based on timing
        final age = DateTime.now().difference(createdAt);
        final ageRatio = age.inMilliseconds / lifetime.inMilliseconds;
        final scoreRange = maxScore - baseScore;
        return baseScore + (scoreRange * (1.0 - ageRatio)).round();
      case TargetType.hardBall:
        return splitScore; // Score for each fragment
      default:
        return baseScore;
    }
  }

  // Static helper methods for target type properties
  static Color _getColorForType(TargetType type) {
    switch (type) {
      case TargetType.easyBall:
        return const Color(0xFF00FFFF); // Cyan
      case TargetType.mediumBall:
        return const Color(0xFF00FF00); // Green
      case TargetType.hardBall:
        return const Color(0xFF0080FF); // Blue
      case TargetType.redEnemy:
        return const Color(0xFFFF0000); // Red
      case TargetType.yellowEnemy:
        return const Color(0xFFFFFF00); // Yellow
    }
  }

  static double _getSizeForType(TargetType type) {
    final config = GameConstants.targetTypes;
    switch (type) {
      case TargetType.easyBall:
        return 60.0;
      case TargetType.mediumBall:
        return 70.0;
      case TargetType.hardBall:
        return 80.0;
      case TargetType.redEnemy:
        return 65.0;
      case TargetType.yellowEnemy:
        return 65.0;
    }
  }

  static IconData? _getIconForType(TargetType type) {
    switch (type) {
      case TargetType.redEnemy:
        return Icons.warning;
      case TargetType.yellowEnemy:
        return Icons.timer;
      default:
        return null; // Balls don't need icons
    }
  }

  static int _getBaseScoreForType(TargetType type) {
    final config = GameConstants.targetTypes;
    switch (type) {
      case TargetType.easyBall:
        return config['easy_ball']!['baseScore'] as int;
      case TargetType.mediumBall:
        return config['medium_ball']!['baseScore'] as int;
      case TargetType.hardBall:
        return config['hard_ball']!['baseScore'] as int;
      case TargetType.redEnemy:
        return config['red_enemy']!['baseScore'] as int;
      case TargetType.yellowEnemy:
        return config['yellow_enemy']!['baseScore'] as int;
    }
  }

  static int _getMaxScoreForType(TargetType type) {
    final config = GameConstants.targetTypes;
    switch (type) {
      case TargetType.easyBall:
        return config['easy_ball']!['maxScore'] as int;
      default:
        return _getBaseScoreForType(type);
    }
  }

  static int _getPenaltyForType(TargetType type) {
    final config = GameConstants.targetTypes;
    switch (type) {
      case TargetType.redEnemy:
        return config['red_enemy']!['penalty'] as int;
      case TargetType.yellowEnemy:
        return config['yellow_enemy']!['penalty'] as int;
      default:
        return 0;
    }
  }

  static Duration _getLifetimeForType(TargetType type) {
    final config = GameConstants.targetTypes;
    switch (type) {
      case TargetType.easyBall:
        return Duration(seconds: (config['easy_ball']!['lifetime'] as double).round());
      case TargetType.mediumBall:
        return Duration(seconds: (config['medium_ball']!['lifetime'] as double).round());
      case TargetType.hardBall:
        return Duration(seconds: (config['hard_ball']!['lifetime'] as double).round());
      case TargetType.redEnemy:
        return Duration(seconds: (config['red_enemy']!['lifetime'] as double).round());
      case TargetType.yellowEnemy:
        return Duration(seconds: (config['yellow_enemy']!['lifetime'] as double).round());
    }
  }

  static bool _getExplodesForType(TargetType type) {
    return type == TargetType.redEnemy;
  }

  static bool _getAffectsTimerForType(TargetType type) {
    return type == TargetType.yellowEnemy;
  }

  static int _getSplitCountForType(TargetType type) {
    final config = GameConstants.targetTypes;
    if (type == TargetType.hardBall) {
      return config['hard_ball']!['splitCount'] as int;
    }
    return 0;
  }

  static int _getSplitScoreForType(TargetType type) {
    final config = GameConstants.targetTypes;
    if (type == TargetType.hardBall) {
      return config['hard_ball']!['splitScore'] as int;
    }
    return 0;
  }
}
