/// Represents a token transaction in the game
class TokenTransaction {
  final String id;
  final TokenTransactionType type;
  final int amount;
  final DateTime timestamp;
  final String description;
  final Map<String, dynamic> metadata;

  const TokenTransaction({
    required this.id,
    required this.type,
    required this.amount,
    required this.timestamp,
    required this.description,
    this.metadata = const {},
  });

  /// Creates a copy with modified properties
  TokenTransaction copyWith({
    String? id,
    TokenTransactionType? type,
    int? amount,
    DateTime? timestamp,
    String? description,
    Map<String, dynamic>? metadata,
  }) {
    return TokenTransaction(
      id: id ?? this.id,
      type: type ?? this.type,
      amount: amount ?? this.amount,
      timestamp: timestamp ?? this.timestamp,
      description: description ?? this.description,
      metadata: metadata ?? this.metadata,
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type.name,
      'amount': amount,
      'timestamp': timestamp.millisecondsSinceEpoch,
      'description': description,
      'metadata': metadata,
    };
  }

  /// Create from JSON
  factory TokenTransaction.fromJson(Map<String, dynamic> json) {
    return TokenTransaction(
      id: json['id'],
      type: TokenTransactionType.values.firstWhere((t) => t.name == json['type']),
      amount: json['amount'],
      timestamp: DateTime.fromMillisecondsSinceEpoch(json['timestamp']),
      description: json['description'],
      metadata: Map<String, dynamic>.from(json['metadata'] ?? {}),
    );
  }

  /// Check if this is an earning transaction
  bool get isEarning => type.isEarning;

  /// Check if this is a spending transaction
  bool get isSpending => type.isSpending;

  /// Get formatted amount with sign
  String get formattedAmount {
    final sign = isEarning ? '+' : '-';
    return '$sign$amount';
  }

  /// Get transaction icon based on type
  String get iconPath {
    switch (type) {
      case TokenTransactionType.levelCompletion:
        return 'assets/icons/level_complete.svg';
      case TokenTransactionType.combo:
        return 'assets/icons/combo.svg';
      case TokenTransactionType.objective:
        return 'assets/icons/objective.svg';
      case TokenTransactionType.dailyChallenge:
        return 'assets/icons/daily_challenge.svg';
      case TokenTransactionType.weeklyChallenge:
        return 'assets/icons/weekly_challenge.svg';
      case TokenTransactionType.achievement:
        return 'assets/icons/achievement.svg';
      case TokenTransactionType.spinWheel:
        return 'assets/icons/spin_wheel.svg';
      case TokenTransactionType.dailyBonus:
        return 'assets/icons/daily_bonus.svg';
      case TokenTransactionType.mysteryBox:
        return 'assets/icons/mystery_box.svg';
      case TokenTransactionType.modeUnlock:
        return 'assets/icons/mode_unlock.svg';
      case TokenTransactionType.powerUpPurchase:
        return 'assets/icons/power_up.svg';
      case TokenTransactionType.spinWheelPurchase:
        return 'assets/icons/spin_purchase.svg';
      case TokenTransactionType.modeRental:
        return 'assets/icons/rental.svg';
      case TokenTransactionType.bonusGameUnlock:
        return 'assets/icons/bonus_game.svg';
      case TokenTransactionType.powerUpUpgrade:
        return 'assets/icons/upgrade.svg';
      case TokenTransactionType.bonusGameKey:
        return 'assets/icons/key.svg';
    }
  }
}

/// Types of token transactions
enum TokenTransactionType {
  // Earning transactions
  levelCompletion,
  combo,
  objective,
  dailyChallenge,
  weeklyChallenge,
  achievement,
  spinWheel,
  dailyBonus,
  mysteryBox, // New: mystery box rewards

  // Spending transactions
  modeUnlock,
  powerUpPurchase,
  spinWheelPurchase,
  modeRental,
  bonusGameUnlock, // New: bonus game purchases
  powerUpUpgrade, // New: power-up tier upgrades
  bonusGameKey; // New: bonus game key purchases

  /// Check if this transaction type represents earning tokens
  bool get isEarning {
    switch (this) {
      case TokenTransactionType.levelCompletion:
      case TokenTransactionType.combo:
      case TokenTransactionType.objective:
      case TokenTransactionType.dailyChallenge:
      case TokenTransactionType.weeklyChallenge:
      case TokenTransactionType.achievement:
      case TokenTransactionType.spinWheel:
      case TokenTransactionType.dailyBonus:
      case TokenTransactionType.mysteryBox:
        return true;
      case TokenTransactionType.modeUnlock:
      case TokenTransactionType.powerUpPurchase:
      case TokenTransactionType.spinWheelPurchase:
      case TokenTransactionType.modeRental:
      case TokenTransactionType.bonusGameUnlock:
      case TokenTransactionType.powerUpUpgrade:
      case TokenTransactionType.bonusGameKey:
        return false;
    }
  }

  /// Check if this transaction type represents spending tokens
  bool get isSpending => !isEarning;

  /// Get display name for the transaction type
  String get displayName {
    switch (this) {
      case TokenTransactionType.levelCompletion:
        return 'Level Completed';
      case TokenTransactionType.combo:
        return 'Combo Bonus';
      case TokenTransactionType.objective:
        return 'Objective Completed';
      case TokenTransactionType.dailyChallenge:
        return 'Daily Challenge';
      case TokenTransactionType.weeklyChallenge:
        return 'Weekly Challenge';
      case TokenTransactionType.achievement:
        return 'Achievement Unlocked';
      case TokenTransactionType.spinWheel:
        return 'Spin Wheel Reward';
      case TokenTransactionType.dailyBonus:
        return 'Daily Login Bonus';
      case TokenTransactionType.mysteryBox:
        return 'Mystery Box Reward';
      case TokenTransactionType.modeUnlock:
        return 'Mode Unlocked';
      case TokenTransactionType.powerUpPurchase:
        return 'Power-up Purchased';
      case TokenTransactionType.spinWheelPurchase:
        return 'Spin Wheel Purchase';
      case TokenTransactionType.modeRental:
        return 'Mode Rental';
      case TokenTransactionType.bonusGameUnlock:
        return 'Bonus Game Unlocked';
      case TokenTransactionType.powerUpUpgrade:
        return 'Power-up Upgraded';
      case TokenTransactionType.bonusGameKey:
        return 'Bonus Game Key';
    }
  }
}

/// Factory class for creating token transactions
class TokenTransactionFactory {
  /// Create a level completion transaction
  static TokenTransaction levelCompletion({
    required int tokens,
    required int level,
    int? score,
    int? timeBonus,
  }) {
    return TokenTransaction(
      id: _generateId(),
      type: TokenTransactionType.levelCompletion,
      amount: tokens,
      timestamp: DateTime.now(),
      description: 'Completed Level $level',
      metadata: {
        'level': level,
        'score': score,
        'timeBonus': timeBonus,
      },
    );
  }

  /// Create a combo bonus transaction
  static TokenTransaction combo({
    required int tokens,
    required int comboCount,
  }) {
    return TokenTransaction(
      id: _generateId(),
      type: TokenTransactionType.combo,
      amount: tokens,
      timestamp: DateTime.now(),
      description: '${comboCount}x Combo Bonus',
      metadata: {
        'comboCount': comboCount,
      },
    );
  }

  /// Create an objective completion transaction
  static TokenTransaction objective({
    required int tokens,
    required String objectiveName,
  }) {
    return TokenTransaction(
      id: _generateId(),
      type: TokenTransactionType.objective,
      amount: tokens,
      timestamp: DateTime.now(),
      description: 'Objective: $objectiveName',
      metadata: {
        'objectiveName': objectiveName,
      },
    );
  }

  /// Create a spin wheel reward transaction
  static TokenTransaction spinWheelReward({
    required int tokens,
    required bool wasFree,
  }) {
    return TokenTransaction(
      id: _generateId(),
      type: TokenTransactionType.spinWheel,
      amount: tokens,
      timestamp: DateTime.now(),
      description: wasFree ? 'Free Spin Reward' : 'Paid Spin Reward',
      metadata: {
        'wasFree': wasFree,
      },
    );
  }

  /// Create a mode unlock transaction
  static TokenTransaction modeUnlock({
    required int tokens,
    required String modeName,
    required String modeId,
  }) {
    return TokenTransaction(
      id: _generateId(),
      type: TokenTransactionType.modeUnlock,
      amount: tokens,
      timestamp: DateTime.now(),
      description: 'Unlocked $modeName',
      metadata: {
        'modeName': modeName,
        'modeId': modeId,
      },
    );
  }

  /// Create a spin wheel purchase transaction
  static TokenTransaction spinWheelPurchase({
    required int tokens,
  }) {
    return TokenTransaction(
      id: _generateId(),
      type: TokenTransactionType.spinWheelPurchase,
      amount: tokens,
      timestamp: DateTime.now(),
      description: 'Purchased Spin',
      metadata: {},
    );
  }

  /// Generate a unique transaction ID
  static String _generateId() {
    return 'txn_${DateTime.now().millisecondsSinceEpoch}_${DateTime.now().microsecond}';
  }
}
