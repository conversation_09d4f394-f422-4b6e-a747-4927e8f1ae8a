import 'dart:async';
import 'dart:math';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';

import '../models/breakfinity_section.dart';
import '../models/breakfinity_progress.dart';
import '../models/breakfinity_reward.dart';
import '../models/breakfinity_layer_theme.dart';
import '../constants/breakfinity_constants.dart';
import '../services/token_service.dart';
import '../models/token_transaction.dart';
import '../services/challenges_service.dart';
import '../models/challenge.dart';

/// Provider for managing Breakfinity game state and logic
class BreakfinityProvider extends ChangeNotifier {
  BreakfinityProgress _progress = BreakfinityProgress(lastPlayedAt: DateTime.now());
  final Map<String, BreakfinitySection> _currentLayerSections = {};
  final List<Timer> _activeTimers = [];
  bool _isLoading = true;
  String _currentTheme = 'default';
  final Map<String, DateTime> _activePowerUps = {};

  // Callback for particle effects
  Function(Offset position, Color color)? onParticleEffect;

  // Callback for reward discovery
  Function(String message, Map<String, dynamic> rewards)? onRewardDiscovered;

  // Callback for enhanced reward display
  Function(BreakfinityReward reward)? onEnhancedRewardDiscovered;

  // Getters
  BreakfinityProgress get progress => _progress;
  Map<String, BreakfinitySection> get currentLayerSections => _currentLayerSections;
  bool get isLoading => _isLoading;
  String get currentTheme => _currentTheme;
  Map<String, DateTime> get activePowerUps => _activePowerUps;

  /// Get the current layer theme
  BreakfinityLayerTheme get currentLayerTheme =>
      BreakfinityLayerThemes.getThemeForLayer(_progress.currentLayer);

  /// Initialize the provider and load saved progress
  Future<void> initialize() async {
    _isLoading = true;
    notifyListeners();

    try {
      _progress = await BreakfinityProgress.load();
      await _generateCurrentLayer();
      _startAutoSave();
      _startAutoTappers();
      _processOfflineProgress();
    } catch (e) {
      debugPrint('Error initializing Breakfinity: $e');
    }

    _isLoading = false;
    notifyListeners();
  }

  /// Generate sections for the current layer
  Future<void> _generateCurrentLayer() async {
    _currentLayerSections.clear();

    final layer = _progress.currentLayer;
    final sectionsPerRow = BreakfinityConstants.sectionsPerRow;
    final sectionSize = BreakfinityConstants.sectionSize;
    final spacing = BreakfinityConstants.sectionSpacing;

    // Check if we have saved section states for this layer
    final savedStates = _progress.getSectionStates(layer);

    for (int i = 0; i < BreakfinityConstants.sectionsPerLayer; i++) {
      final row = i ~/ sectionsPerRow;
      final col = i % sectionsPerRow;

      final position = Offset(
        col * (sectionSize + spacing),
        row * (sectionSize + spacing),
      );

      final section = SectionFactory.generateSection(
        layer: layer,
        sectionIndex: i,
        position: position,
        size: Size(sectionSize, sectionSize),
      );

      // Restore saved state if available
      final sectionId = section.id;
      if (savedStates.containsKey(sectionId)) {
        final savedSection = BreakfinitySection.fromJson(savedStates[sectionId]);
        _currentLayerSections[sectionId] = savedSection;
      } else {
        _currentLayerSections[sectionId] = section;
      }
    }

    // Initialize layer progress if not exists
    if (!_progress.layerProgress.containsKey(layer)) {
      final layerProgress = LayerProgress(
        layerNumber: layer,
        totalSections: BreakfinityConstants.sectionsPerLayer,
        firstAccessedAt: DateTime.now(),
      );
      
      _progress = _progress.copyWith(
        layerProgress: {..._progress.layerProgress, layer: layerProgress},
      );
    }
  }

  /// Handle tap on a section
  Future<void> tapSection(String sectionId) async {
    try {
      final section = _currentLayerSections[sectionId];
      if (section == null || section.isDestroyed) {
        debugPrint('Breakfinity: Cannot tap section $sectionId - ${section == null ? 'not found' : 'already destroyed'}');
        return;
      }

      debugPrint('Breakfinity: Tapping section $sectionId (health: ${section.currentHealth}/${section.maxHealth})');

      // Calculate damage
      final damage = _calculateTapDamage();
      final newHealth = (section.currentHealth - damage).clamp(0, section.maxHealth);
      final crackProgress = 1.0 - (newHealth / section.maxHealth);

      debugPrint('Breakfinity: Dealing $damage damage, new health: $newHealth');

      // Generate crack lines for visual feedback
      final crackLines = newHealth > 0 ? section.generateCrackLines() : <Offset>[];

      // Check for reward discovery
      BreakfinityReward? discoveredReward;
      if (newHealth <= 0) {
        discoveredReward = BreakfinityRewardSystem.generateReward(
          section.layer,
          section.type.name,
        );
      }

      // Update section
      final updatedSection = section.copyWith(
        currentHealth: newHealth,
        crackProgress: crackProgress,
        lastTappedAt: DateTime.now(),
        isDestroyed: newHealth <= 0,
        crackLines: crackLines,
        discoveredReward: discoveredReward,
        hasParticleEffect: true,
      );

      _currentLayerSections[sectionId] = updatedSection;

      // Update progress
      _progress = _progress.copyWith(
        totalTaps: _progress.totalTaps + 1,
      );

      // Handle section destruction
      if (newHealth <= 0) {
        debugPrint('Breakfinity: Section $sectionId destroyed!');
        await _handleSectionDestroyed(updatedSection);
      }

      // Trigger enhanced particle effects
      if (onParticleEffect != null) {
        final centerPosition = Offset(
          section.position.dx + section.size.width / 2,
          section.position.dy + section.size.height / 2,
        );
        final particleColor = updatedSection.particleColor;
        onParticleEffect!(centerPosition, particleColor);
      }

      // Trigger reward discovery notification
      if (discoveredReward != null && onEnhancedRewardDiscovered != null) {
        onEnhancedRewardDiscovered!(discoveredReward);
      }

      // Haptic feedback
      if (BreakfinityConstants.enableHapticFeedback) {
        HapticFeedback.lightImpact();
      }

      notifyListeners();
      debugPrint('Breakfinity: Tap completed successfully');
    } catch (e) {
      debugPrint('Breakfinity: Error in tapSection: $e');
      // Ensure we still notify listeners even if there's an error
      notifyListeners();
    }
  }

  /// Calculate damage for a tap
  int _calculateTapDamage() {
    int damage = _progress.getEffectiveDamage();

    // Apply active power-up effects
    if (_isPowerUpActive('mega_tap')) {
      damage *= 5;
    }

    return damage;
  }

  /// Handle when a section is destroyed
  Future<void> _handleSectionDestroyed(BreakfinitySection section) async {
    // Award tokens
    final tokenReward = section.rewards['tokens'] as int? ?? 0;
    if (tokenReward > 0) {
      await TokenService.earnTokens(
        amount: tokenReward,
        type: TokenTransactionType.levelCompletion,
        description: 'Breakfinity section destroyed',
        metadata: {
          'layer': section.layer,
          'sectionType': section.type.name,
        },
      );
    }

    // Show reward discovery popup for special sections
    if (section.rewards.isNotEmpty && onRewardDiscovered != null) {
      String message = 'Hidden reward found!';

      if (section.type == SectionType.crystal) {
        message = 'Crystal section destroyed! Bonus tokens earned!';
      } else if (section.type == SectionType.mystery) {
        message = 'Mystery section revealed!';
        if (section.rewards.containsKey('powerUp')) {
          message += ' Power-up discovered!';
        }
      } else if (tokenReward > 10) {
        message = 'Valuable section destroyed!';
      }

      onRewardDiscovered!(message, section.rewards);
    }

    // Update challenge progress for section destruction
    ChallengesService.updateProgress(
      gameId: GameIdentifiers.breakfinity,
      type: ChallengeType.breakfinity,
      amount: 1,
      metadata: {
        'sectionType': section.type.name,
        'layer': section.layer,
        'action': 'sectionDestroyed',
      },
    );

    // Update token earning challenges if tokens were earned
    if (tokenReward > 0) {
      ChallengesService.updateProgress(
        gameId: GameIdentifiers.breakfinity,
        type: ChallengeType.tokenEarning,
        amount: tokenReward,
      );
    }

    // Update layer progress
    final currentLayerProgress = _progress.layerProgress[_progress.currentLayer];
    if (currentLayerProgress != null) {
      final updatedProgress = currentLayerProgress.copyWith(
        sectionsDestroyed: currentLayerProgress.sectionsDestroyed + 1,
      );

      _progress = _progress.copyWith(
        layerProgress: {
          ..._progress.layerProgress,
          _progress.currentLayer: updatedProgress,
        },
        totalSectionsBroken: _progress.totalSectionsBroken + 1,
        totalTokensEarned: _progress.totalTokensEarned + tokenReward,
      );

      // Check if layer is complete
      if (updatedProgress.isComplete) {
        await _handleLayerComplete();
      }
    }

    // Handle special section effects
    await _handleSpecialSectionEffects(section);
  }

  /// Handle special effects when certain section types are destroyed
  Future<void> _handleSpecialSectionEffects(BreakfinitySection section) async {
    switch (section.type) {
      case SectionType.explosive:
        // Damage nearby sections
        await _damageNearbySections(section.position, 100.0, section.maxHealth ~/ 2);
        break;
      case SectionType.crystal:
        // Bonus token reward
        await TokenService.earnTokens(
          amount: 50,
          type: TokenTransactionType.levelCompletion,
          description: 'Crystal section bonus',
        );
        break;
      case SectionType.mystery:
        // Random power-up or upgrade
        await _awardRandomReward();
        break;
      default:
        break;
    }
  }

  /// Damage sections near a position
  Future<void> _damageNearbySections(Offset center, double radius, int damage) async {
    for (final section in _currentLayerSections.values) {
      if (section.isDestroyed) continue;
      
      final distance = (section.position - center).distance;
      if (distance <= radius) {
        final newHealth = (section.currentHealth - damage).clamp(0, section.maxHealth);
        final updatedSection = section.copyWith(
          currentHealth: newHealth,
          crackProgress: 1.0 - (newHealth / section.maxHealth),
          isDestroyed: newHealth <= 0,
        );
        
        _currentLayerSections[section.id] = updatedSection;
        
        if (newHealth <= 0) {
          await _handleSectionDestroyed(updatedSection);
        }
      }
    }
  }

  /// Award a random reward
  Future<void> _awardRandomReward() async {
    final random = Random();
    final rewardType = random.nextInt(3);
    
    switch (rewardType) {
      case 0:
        // Bonus tokens
        await TokenService.earnTokens(
          amount: 100 + random.nextInt(200),
          type: TokenTransactionType.mysteryBox,
          description: 'Mystery section reward',
        );
        break;
      case 1:
        // Temporary power-up
        await activatePowerUp('mega_tap');
        break;
      case 2:
        // Damage boost
        _progress = _progress.copyWith(
          tapMultiplier: _progress.tapMultiplier + 0.1,
        );
        break;
    }
  }

  /// Handle layer completion
  Future<void> _handleLayerComplete() async {
    final layer = _progress.currentLayer;

    // Award completion bonus
    await TokenService.earnTokens(
      amount: BreakfinityConstants.layerCompletionBonus,
      type: TokenTransactionType.levelCompletion,
      description: 'Layer $layer completion bonus',
      metadata: {'layer': layer},
    );

    // Update challenge progress for layer completion
    ChallengesService.updateProgress(
      gameId: GameIdentifiers.breakfinity,
      type: ChallengeType.levelCompletion,
      amount: 1,
      metadata: {
        'layer': layer,
        'action': 'layerCompleted',
      },
    );

    // Update token earning challenges for completion bonus
    ChallengesService.updateProgress(
      gameId: GameIdentifiers.breakfinity,
      type: ChallengeType.tokenEarning,
      amount: BreakfinityConstants.layerCompletionBonus,
    );

    // Update progress
    _progress = _progress.copyWith(
      currentLayer: layer + 1,
      totalLayersBroken: _progress.totalLayersBroken + 1,
    );

    // Check for milestone rewards
    await _checkMilestoneRewards(layer);

    // Generate next layer
    await _generateCurrentLayer();
  }

  /// Check and award milestone rewards
  Future<void> _checkMilestoneRewards(int completedLayer) async {
    final rewards = BreakfinityConstants.milestoneRewards[completedLayer];
    if (rewards != null) {
      if (rewards.containsKey('tokens')) {
        await TokenService.earnTokens(
          amount: rewards['tokens'] as int,
          type: TokenTransactionType.achievement,
          description: 'Layer $completedLayer milestone reward',
        );
      }
      
      if (rewards.containsKey('powerUp')) {
        await activatePowerUp(rewards['powerUp'] as String);
      }
      
      if (rewards.containsKey('theme')) {
        _unlockTheme(rewards['theme'] as String);
      }
    }
  }

  /// Activate a power-up
  Future<void> activatePowerUp(String powerUpId) async {
    final powerUp = BreakfinityPowerUps.getPowerUp(powerUpId);
    if (powerUp == null) return;

    final duration = powerUp['duration'] as int;
    if (duration > 0) {
      _activePowerUps[powerUpId] = DateTime.now().add(Duration(milliseconds: duration));
      
      // Set up timer to remove power-up
      Timer(Duration(milliseconds: duration), () {
        _activePowerUps.remove(powerUpId);
        notifyListeners();
      });
    } else {
      // Instant effect power-ups
      await _applyInstantPowerUp(powerUpId, powerUp);
    }

    notifyListeners();
  }

  /// Apply instant power-up effects
  Future<void> _applyInstantPowerUp(String powerUpId, Map<String, dynamic> powerUp) async {
    final effect = powerUp['effect'] as Map<String, dynamic>;
    
    if (effect.containsKey('reveal_all')) {
      _revealAllSections();
    }
    
    if (effect.containsKey('destroy_percentage')) {
      final percentage = effect['destroy_percentage'] as double;
      await _destroyPercentageOfSections(percentage);
    }
  }

  /// Reveal all sections in current layer
  void _revealAllSections() {
    for (final sectionId in _currentLayerSections.keys) {
      final section = _currentLayerSections[sectionId]!;
      _currentLayerSections[sectionId] = section.copyWith(isRevealed: true);
    }
  }

  /// Destroy a percentage of remaining sections
  Future<void> _destroyPercentageOfSections(double percentage) async {
    final remainingSections = _currentLayerSections.values
        .where((s) => !s.isDestroyed)
        .toList();
    
    final sectionsToDestroy = (remainingSections.length * percentage).round();
    remainingSections.shuffle();
    
    for (int i = 0; i < sectionsToDestroy && i < remainingSections.length; i++) {
      final section = remainingSections[i];
      final destroyedSection = section.copyWith(
        currentHealth: 0,
        isDestroyed: true,
        crackProgress: 1.0,
      );
      
      _currentLayerSections[section.id] = destroyedSection;
      await _handleSectionDestroyed(destroyedSection);
    }
  }

  /// Check if a power-up is currently active
  bool _isPowerUpActive(String powerUpId) {
    final expiry = _activePowerUps[powerUpId];
    if (expiry == null) return false;
    
    if (DateTime.now().isAfter(expiry)) {
      _activePowerUps.remove(powerUpId);
      return false;
    }
    
    return true;
  }

  /// Unlock a theme
  void _unlockTheme(String themeId) {
    if (!_progress.unlockedThemes.contains(themeId)) {
      _progress = _progress.copyWith(
        unlockedThemes: [..._progress.unlockedThemes, themeId],
      );
    }
  }

  /// Change the current theme
  void changeTheme(String themeId) {
    if (_progress.unlockedThemes.contains(themeId) || themeId == 'default') {
      _currentTheme = themeId;
      notifyListeners();
    }
  }

  /// Purchase an upgrade
  void purchaseUpgrade(String upgradeId) {
    final currentLevel = _progress.upgrades[upgradeId] as int? ?? 0;
    final newUpgrades = Map<String, dynamic>.from(_progress.upgrades);
    newUpgrades[upgradeId] = currentLevel + 1;

    _progress = _progress.copyWith(upgrades: newUpgrades);
    notifyListeners();

    // Apply upgrade effects immediately
    _applyUpgradeEffects(upgradeId, currentLevel + 1);
  }

  /// Apply upgrade effects
  void _applyUpgradeEffects(String upgradeId, int level) {
    switch (upgradeId) {
      case 'damage_boost':
        // Damage boost upgrade increases tap damage
        // Effect is already applied in getEffectiveDamage()
        break;
      case 'auto_tapper':
        // Auto tapper upgrade adds more auto tappers
        // Effect is already applied in _getActiveAutoTapperCount()
        break;
      case 'layer_scanner':
        // Layer scanner upgrade reveals hidden sections
        // Effect is applied when generating sections
        break;
      case 'crystal_finder':
        // Crystal finder upgrade increases crystal spawn rate
        // Effect is applied when generating sections
        break;
    }
  }

  /// Start auto-save timer
  void _startAutoSave() {
    _activeTimers.add(
      Timer.periodic(BreakfinityConstants.autoSaveInterval, (_) {
        _saveSectionStates();
        _progress.save();
      }),
    );
  }

  /// Save current section states to progress
  void _saveSectionStates() {
    final sectionStates = <String, dynamic>{};
    for (final entry in _currentLayerSections.entries) {
      sectionStates[entry.key] = entry.value.toJson();
    }

    _progress = _progress.saveSectionStates(_progress.currentLayer, sectionStates);
  }

  /// Start auto-tapper timers
  void _startAutoTappers() {
    _activeTimers.add(
      Timer.periodic(BreakfinityConstants.autoTapInterval, (_) {
        _performAutoTaps();
      }),
    );
  }

  /// Perform automatic taps
  void _performAutoTaps() {
    final autoTapperCount = _getActiveAutoTapperCount();
    if (autoTapperCount <= 0) return;

    final availableSections = _currentLayerSections.values
        .where((s) => !s.isDestroyed)
        .toList();
    
    if (availableSections.isEmpty) return;

    for (int i = 0; i < autoTapperCount && i < availableSections.length; i++) {
      final section = availableSections[i];
      tapSection(section.id);
    }
  }

  /// Get the number of active auto-tappers
  int _getActiveAutoTapperCount() {
    int count = 0;
    
    // From upgrades
    final autoTapperLevel = _progress.upgrades['auto_tapper'] as int? ?? 0;
    count += autoTapperLevel;
    
    // From active power-ups
    if (_isPowerUpActive('auto_tapper')) {
      count += 1;
    }
    
    return count.clamp(0, BreakfinityConstants.maxAutoTappers);
  }

  /// Process offline progress
  void _processOfflineProgress() {
    final now = DateTime.now();
    final offlineTime = now.difference(_progress.lastPlayedAt);
    
    if (offlineTime > Duration(minutes: 5)) {
      final offlineHours = offlineTime.inHours.clamp(0, 24);
      final offlineReward = (offlineHours * 100 * BreakfinityConstants.offlineProgressMultiplier)
          .round()
          .clamp(0, BreakfinityConstants.maxOfflineReward);
      
      if (offlineReward > 0) {
        // Show offline reward dialog would go here
        TokenService.earnTokens(
          amount: offlineReward,
          type: TokenTransactionType.dailyBonus,
          description: 'Offline progress reward',
          metadata: {'offlineHours': offlineHours},
        );
      }
    }
    
    _progress = _progress.copyWith(lastPlayedAt: now);
  }

  @override
  void dispose() {
    for (final timer in _activeTimers) {
      timer.cancel();
    }
    _activeTimers.clear();
    _saveSectionStates(); // Save section states before disposing
    _progress.save();
    super.dispose();
  }
}
