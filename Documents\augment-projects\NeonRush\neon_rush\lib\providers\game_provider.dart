import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../constants.dart';
import '../models/mode_model.dart';
import '../models/level_config.dart';
import '../models/token_transaction.dart';
import '../services/token_service.dart';
import '../services/spin_wheel_service.dart';
import '../services/level_service.dart';

/// Main game provider for token-driven NeonRush game
class GameProvider extends ChangeNotifier {
  // Core game state
  bool _isInitialized = false;
  int _tokens = 0;
  int _currentLevel = 1;
  bool _dailySpinUsed = false;
  int _loginStreak = 0;
  List<String> _unlockedModes = [];
  List<String> _unlockedBonusGames = [];
  Map<String, dynamic> _achievements = {};
  Map<String, dynamic> _statistics = {};

  // Game session state
  GameState _currentState = GameState.menu;
  LevelConfig? _currentLevelConfig;
  int _currentScore = 0;
  int _comboCount = 0;
  Duration _sessionTime = Duration.zero;

  // Getters
  bool get isInitialized => _isInitialized;
  int get tokens => _tokens;
  int get currentLevel => _currentLevel;
  bool get dailySpinUsed => _dailySpinUsed;
  int get loginStreak => _loginStreak;
  List<String> get unlockedModes => List.unmodifiable(_unlockedModes);
  List<String> get unlockedBonusGames => List.unmodifiable(_unlockedBonusGames);
  Map<String, dynamic> get achievements => Map.unmodifiable(_achievements);
  Map<String, dynamic> get statistics => Map.unmodifiable(_statistics);
  
  GameState get currentState => _currentState;
  LevelConfig? get currentLevelConfig => _currentLevelConfig;
  int get currentScore => _currentScore;
  int get comboCount => _comboCount;
  Duration get sessionTime => _sessionTime;

  /// Initialize the game provider
  Future<void> initialize() async {
    await _loadGameData();
    await _checkDailyBonus();
    _isInitialized = true;
    notifyListeners();
  }

  /// Load all game data from storage
  Future<void> _loadGameData() async {
    _tokens = await TokenService.getTokenBalance();
    _loginStreak = await TokenService.getLoginStreak();
    _dailySpinUsed = !(await SpinWheelService.isDailySpinAvailable());
    
    final prefs = await SharedPreferences.getInstance();
    
    // Load unlocked modes
    final unlockedModesJson = prefs.getString(StorageKeys.unlockedModes);
    if (unlockedModesJson != null) {
      _unlockedModes = List<String>.from(jsonDecode(unlockedModesJson));
    }

    // Load unlocked bonus games
    final unlockedBonusGamesJson = prefs.getString(StorageKeys.unlockedBonusGames);
    if (unlockedBonusGamesJson != null) {
      _unlockedBonusGames = List<String>.from(jsonDecode(unlockedBonusGamesJson));
    }

    // Load current level
    _currentLevel = prefs.getInt('current_level') ?? 1;
    
    // Load achievements
    final achievementsJson = prefs.getString(StorageKeys.achievements);
    if (achievementsJson != null) {
      _achievements = Map<String, dynamic>.from(jsonDecode(achievementsJson));
    }
    
    // Load statistics
    final statisticsJson = prefs.getString(StorageKeys.statistics);
    if (statisticsJson != null) {
      _statistics = Map<String, dynamic>.from(jsonDecode(statisticsJson));
    }
  }

  /// Check and claim daily bonus if available
  Future<void> _checkDailyBonus() async {
    final bonusAmount = await TokenService.claimDailyBonus();
    if (bonusAmount > 0) {
      _tokens += bonusAmount;
      await _saveGameData();
    }
  }

  /// Save game data to storage
  Future<void> _saveGameData() async {
    final prefs = await SharedPreferences.getInstance();
    
    await prefs.setString(StorageKeys.unlockedModes, jsonEncode(_unlockedModes));
    await prefs.setString(StorageKeys.unlockedBonusGames, jsonEncode(_unlockedBonusGames));
    await prefs.setInt('current_level', _currentLevel);
    await prefs.setString(StorageKeys.achievements, jsonEncode(_achievements));
    await prefs.setString(StorageKeys.statistics, jsonEncode(_statistics));
  }

  /// Earn tokens and update balance
  Future<void> earnTokens(int amount, TokenTransactionType type, String description, {Map<String, dynamic>? metadata}) async {
    await TokenService.earnTokens(
      amount: amount,
      type: type,
      description: description,
      metadata: metadata ?? {},
    );
    _tokens = await TokenService.getTokenBalance();
    notifyListeners();
  }

  /// Check if player has enough tokens
  Future<bool> hasEnoughTokens(int amount) async {
    return await TokenService.hasEnoughTokens(amount);
  }

  /// Spend tokens and update balance
  Future<bool> spendTokens(int amount, TokenTransactionType type, String description, {Map<String, dynamic>? metadata}) async {
    final success = await TokenService.spendTokens(
      amount: amount,
      type: type,
      description: description,
      metadata: metadata ?? {},
    );

    if (success) {
      _tokens = await TokenService.getTokenBalance();
      notifyListeners();
    }

    return success;
  }

  /// Unlock a mode with tokens
  Future<bool> unlockMode(String modeId) async {
    final mode = GameModes.getModeById(modeId);
    if (mode == null || _unlockedModes.contains(modeId)) {
      return false;
    }

    final success = await spendTokens(
      mode.cost,
      TokenTransactionType.modeUnlock,
      'Unlocked ${mode.name}',
      metadata: {'modeId': modeId, 'modeName': mode.name},
    );

    if (success) {
      _unlockedModes.add(modeId);
      await _saveGameData();
      await _checkAchievement('mode_unlocker', 1);
      notifyListeners();
    }

    return success;
  }

  /// Check if mode is unlocked
  bool isModeUnlocked(String modeId) {
    return _unlockedModes.contains(modeId);
  }

  /// Get all available modes with unlock status
  List<Mode> getAllModes() {
    return GameModes.allModes.map((mode) {
      return mode.copyWith(unlocked: _unlockedModes.contains(mode.id));
    }).toList();
  }

  /// Start a level
  void startLevel(int levelNumber) {
    _currentLevelConfig = LevelService.getLevelConfig(levelNumber);
    if (_currentLevelConfig != null) {
      _currentState = GameState.playing;
      _currentScore = 0;
      _comboCount = 0;
      _sessionTime = Duration.zero;
      notifyListeners();
    }
  }

  /// Complete a level
  Future<void> completeLevel({
    required int finalScore,
    required Duration completionTime,
    int? timeBonus,
    bool perfectAccuracy = false,
  }) async {
    if (_currentLevelConfig == null) return;

    _currentScore = finalScore;
    _currentState = GameState.levelComplete;

    // Calculate token rewards
    int tokensEarned = GameConstants.levelCompletionTokens;
    
    // Time bonus
    if (timeBonus != null && timeBonus > 0) {
      tokensEarned += (timeBonus * 0.1).round();
    }

    // Perfect accuracy bonus
    if (perfectAccuracy) {
      tokensEarned += 10;
      await _checkAchievement('perfectionist', 100);
    }

    // Award tokens
    await earnTokens(
      tokensEarned,
      TokenTransactionType.levelCompletion,
      'Completed Level ${_currentLevelConfig!.levelNumber}',
      metadata: {
        'level': _currentLevelConfig!.levelNumber,
        'score': finalScore,
        'timeBonus': timeBonus,
        'perfectAccuracy': perfectAccuracy,
      },
    );

    // Update level progression
    if (_currentLevelConfig!.levelNumber >= _currentLevel) {
      _currentLevel = _currentLevelConfig!.levelNumber + 1;
      await _saveGameData();
    }

    // Check achievements
    await _checkAchievement('first_level', 1);
    if (completionTime.inSeconds < 30) {
      await _checkAchievement('speed_demon', 30);
    }

    // Update statistics
    _updateStatistics('levels_completed', 1);
    _updateStatistics('total_score', finalScore);
    _updateStatistics('total_time_played', completionTime.inSeconds);

    notifyListeners();
  }

  /// Add combo points
  void addCombo() {
    _comboCount++;
    if (_comboCount % GameConstants.comboThreshold == 0) {
      // Award combo tokens
      earnTokens(
        GameConstants.comboTokensPerStreak,
        TokenTransactionType.combo,
        '${_comboCount}x Combo Bonus',
        metadata: {'comboCount': _comboCount},
      );
      
      if (_comboCount >= 10) {
        _checkAchievement('combo_master', 10);
      }
    }
    notifyListeners();
  }

  /// Reset combo
  void resetCombo() {
    _comboCount = 0;
    notifyListeners();
  }

  /// Spin the wheel
  Future<SpinResult> spinWheel({required bool useFree}) async {
    final result = await SpinWheelService.spin(useFree: useFree);
    
    if (!useFree) {
      _tokens = await TokenService.getTokenBalance();
    }
    
    _dailySpinUsed = !(await SpinWheelService.isDailySpinAvailable());
    notifyListeners();
    
    return result;
  }

  /// Update game state
  void updateGameState(GameState newState) {
    _currentState = newState;
    notifyListeners();
  }

  /// Check and update achievements
  Future<void> _checkAchievement(String achievementId, dynamic value) async {
    if (_achievements.containsKey(achievementId)) return;

    final achievement = Achievements.definitions[achievementId];
    if (achievement == null) return;

    bool unlocked = false;
    
    switch (achievementId) {
      case 'first_level':
      case 'mode_unlocker':
        unlocked = true;
        break;
      case 'combo_master':
        unlocked = _comboCount >= achievement['requirement'];
        break;
      case 'speed_demon':
        unlocked = value <= achievement['requirement'];
        break;
      case 'perfectionist':
        unlocked = value >= achievement['requirement'];
        break;
      case 'token_collector':
        unlocked = _tokens >= achievement['requirement'];
        break;
      case 'streak_keeper':
        unlocked = _loginStreak >= achievement['requirement'];
        break;
    }

    if (unlocked) {
      _achievements[achievementId] = DateTime.now().millisecondsSinceEpoch;
      await earnTokens(
        achievement['tokens'],
        TokenTransactionType.achievement,
        'Achievement: ${achievement['name']}',
        metadata: {'achievementId': achievementId},
      );
      await _saveGameData();
    }
  }

  /// Update statistics
  void _updateStatistics(String key, dynamic value) {
    if (_statistics.containsKey(key)) {
      _statistics[key] = _statistics[key] + value;
    } else {
      _statistics[key] = value;
    }
  }

  /// Get next level to play
  int getNextLevel() {
    return _currentLevel;
  }

  /// Check if level is unlocked
  bool isLevelUnlocked(int levelNumber) {
    return levelNumber <= _currentLevel;
  }

  /// Get level completion data for UI
  Map<String, dynamic> getLevelCompletionData() {
    if (_currentLevelConfig == null) return {};

    return {
      'level': _currentLevelConfig!.levelNumber,
      'score': _currentScore,
      'tokensEarned': GameConstants.levelCompletionTokens,
      'hasNextLevel': _currentLevelConfig!.levelNumber < GameConstants.totalLevels,
      'nextLevelUnlocked': isLevelUnlocked(_currentLevelConfig!.levelNumber + 1),
      'canSpin': !_dailySpinUsed || _tokens >= SpinWheelService.getSpinCost(),
    };
  }

  /// Check if a bonus game is unlocked
  bool isGameUnlocked(String gameId) {
    return _unlockedBonusGames.contains(gameId);
  }

  /// Unlock a bonus game with bonus game keys
  Future<bool> unlockBonusGame(String gameId) async {
    if (_unlockedBonusGames.contains(gameId)) return true;

    final keysRequired = GameConstants.bonusGameKeysRequired;
    final currentKeys = await SpinWheelService.getBonusGameKeyCount();

    if (currentKeys < keysRequired) return false;

    // Spend the keys
    await SpinWheelService.spendBonusGameKeys(keysRequired);
    _unlockedBonusGames.add(gameId);
    await _saveGameData();
    notifyListeners();
    return true;
  }

  /// Get bonus game high score
  int getBonusGameHighScore(String gameId) {
    return _statistics['bonus_${gameId}_high_score'] ?? 0;
  }

  /// Set bonus game high score
  Future<void> setBonusGameHighScore(String gameId, int score) async {
    final currentHighScore = getBonusGameHighScore(gameId);
    if (score > currentHighScore) {
      _statistics['bonus_${gameId}_high_score'] = score;
      await _saveGameData();
      notifyListeners();
    }
  }
}
