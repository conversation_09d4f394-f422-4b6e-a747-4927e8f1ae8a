import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../models/neon_theme.dart';
import '../game/game_state.dart';

/// Provider for managing theme switching and persistence
class ThemeProvider extends ChangeNotifier {
  NeonTheme _currentTheme = NeonThemes.cyberBlue;
  List<String> _unlockedThemes = ['cyber_blue'];
  final GameStateManager _gameStateManager;

  ThemeProvider(this._gameStateManager) {
    _loadThemeFromProfile();
  }

  // Getters
  NeonTheme get currentTheme => _currentTheme;
  List<String> get unlockedThemes => _unlockedThemes;
  List<NeonTheme> get availableThemes => NeonThemes.allThemes
      .where((theme) => _unlockedThemes.contains(theme.id))
      .toList();
  List<NeonTheme> get lockedThemes => NeonThemes.allThemes
      .where((theme) => !_unlockedThemes.contains(theme.id))
      .toList();

  /// Load theme from player profile
  void _loadThemeFromProfile() {
    final profile = _gameStateManager.playerProfile;
    if (profile != null) {
      _unlockedThemes = List.from(profile.unlockedThemes);
      final theme = NeonThemes.getThemeById(profile.currentThemeId);
      if (theme != null && _unlockedThemes.contains(theme.id)) {
        _currentTheme = theme;
      }
    }
    notifyListeners();
  }

  /// Change the current theme
  Future<void> changeTheme(String themeId) async {
    if (!_unlockedThemes.contains(themeId)) {
      throw Exception('Theme $themeId is not unlocked');
    }

    final theme = NeonThemes.getThemeById(themeId);
    if (theme == null) {
      throw Exception('Theme $themeId not found');
    }

    _currentTheme = theme;

    // Update player profile
    final profile = _gameStateManager.playerProfile;
    if (profile != null) {
      final updatedProfile = profile.copyWith(currentThemeId: themeId);
      _gameStateManager.updatePlayerProfile(updatedProfile);
      await _gameStateManager.savePlayerProfile();
    }

    notifyListeners();
  }

  /// Unlock a new theme
  Future<void> unlockTheme(String themeId) async {
    if (_unlockedThemes.contains(themeId)) {
      return; // Already unlocked
    }

    final theme = NeonThemes.getThemeById(themeId);
    if (theme == null) {
      throw Exception('Theme $themeId not found');
    }

    _unlockedThemes.add(themeId);

    // Update player profile
    final profile = _gameStateManager.playerProfile;
    if (profile != null) {
      final updatedProfile = profile.copyWith(unlockedThemes: _unlockedThemes);
      _gameStateManager.updatePlayerProfile(updatedProfile);
      await _gameStateManager.savePlayerProfile();
    }

    notifyListeners();
  }

  /// Check if a theme is unlocked
  bool isThemeUnlocked(String themeId) {
    return _unlockedThemes.contains(themeId);
  }

  /// Get theme unlock requirements (could be level, tokens, etc.)
  Map<String, dynamic> getThemeUnlockRequirements(String themeId) {
    switch (themeId) {
      case 'cyber_blue':
        return {'type': 'default', 'requirement': 'Starting theme'};
      case 'neon_pink':
        return {'type': 'level', 'requirement': 'Reach level 3'};
      case 'electric_green':
        return {'type': 'level', 'requirement': 'Reach level 5'};
      case 'fire_orange':
        return {'type': 'tokens', 'requirement': '500 tokens'};
      case 'purple_haze':
        return {'type': 'level', 'requirement': 'Reach level 8'};
      case 'gold_rush':
        return {'type': 'tokens', 'requirement': '1000 tokens'};
      case 'ice_blue':
        return {'type': 'level', 'requirement': 'Complete level 10'};
      case 'crimson_red':
        return {'type': 'tokens', 'requirement': '1500 tokens'};
      case 'silver_storm':
        return {'type': 'achievement', 'requirement': 'Complete all bonus games'};
      case 'rainbow':
        return {'type': 'special', 'requirement': 'Complete all challenges'};
      default:
        return {'type': 'unknown', 'requirement': 'Unknown'};
    }
  }

  /// Check if theme can be unlocked based on current progress
  bool canUnlockTheme(String themeId) {
    if (isThemeUnlocked(themeId)) return true;

    final requirements = getThemeUnlockRequirements(themeId);
    final profile = _gameStateManager.playerProfile;
    if (profile == null) return false;

    switch (requirements['type']) {
      case 'default':
        return true;
      case 'level':
        // Extract level number from requirement string
        final levelMatch = RegExp(r'(\d+)').firstMatch(requirements['requirement']);
        if (levelMatch != null) {
          final requiredLevel = int.parse(levelMatch.group(1)!);
          // Check if any game mode has reached the required level
          return profile.highestLevelUnlocked.values.any((level) => level >= requiredLevel);
        }
        return false;
      case 'tokens':
        // Extract token amount from requirement string
        final tokenMatch = RegExp(r'(\d+)').firstMatch(requirements['requirement']);
        if (tokenMatch != null) {
          final requiredTokens = int.parse(tokenMatch.group(1)!);
          return profile.tokens >= requiredTokens;
        }
        return false;
      case 'achievement':
      case 'special':
        // These would need more complex logic based on achievements
        return false;
      default:
        return false;
    }
  }

  /// Purchase a theme with tokens
  Future<bool> purchaseTheme(String themeId) async {
    if (isThemeUnlocked(themeId)) return true;

    final requirements = getThemeUnlockRequirements(themeId);
    if (requirements['type'] != 'tokens') {
      throw Exception('Theme $themeId cannot be purchased with tokens');
    }

    final profile = _gameStateManager.playerProfile;
    if (profile == null) return false;

    final tokenMatch = RegExp(r'(\d+)').firstMatch(requirements['requirement']);
    if (tokenMatch == null) return false;

    final requiredTokens = int.parse(tokenMatch.group(1)!);
    if (profile.tokens < requiredTokens) {
      throw Exception('Not enough tokens to purchase theme');
    }

    // Deduct tokens and unlock theme
    final updatedProfile = profile.copyWith(
      tokens: profile.tokens - requiredTokens,
      unlockedThemes: [..._unlockedThemes, themeId],
    );
    
    _gameStateManager.updatePlayerProfile(updatedProfile);
    await _gameStateManager.savePlayerProfile();
    _unlockedThemes.add(themeId);
    
    notifyListeners();
    return true;
  }

  /// Auto-unlock themes based on progress
  Future<void> checkAndUnlockThemes() async {
    final profile = _gameStateManager.playerProfile;
    if (profile == null) return;

    bool hasNewUnlocks = false;

    for (final theme in NeonThemes.allThemes) {
      if (!isThemeUnlocked(theme.id) && canUnlockTheme(theme.id)) {
        final requirements = getThemeUnlockRequirements(theme.id);
        if (requirements['type'] == 'level') {
          _unlockedThemes.add(theme.id);
          hasNewUnlocks = true;
        }
      }
    }

    if (hasNewUnlocks) {
      final updatedProfile = profile.copyWith(unlockedThemes: _unlockedThemes);
      _gameStateManager.updatePlayerProfile(updatedProfile);
      await _gameStateManager.savePlayerProfile();
      notifyListeners();
    }
  }

  /// Get Flutter ThemeData based on current neon theme
  ThemeData getFlutterTheme() {
    return ThemeData(
      scaffoldBackgroundColor: _currentTheme.background,
      fontFamily: 'Roboto',
      textTheme: TextTheme(
        headlineLarge: TextStyle(
          color: _currentTheme.primary,
          letterSpacing: 2,
          shadows: [
            Shadow(
              color: _currentTheme.primary.withValues(alpha: 0.6),
              blurRadius: 12,
            ),
          ],
        ),
        bodyLarge: TextStyle(color: _currentTheme.secondary),
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.transparent,
          foregroundColor: _currentTheme.primary,
          side: BorderSide(color: _currentTheme.primary, width: 2),
          shadowColor: _currentTheme.primary.withValues(alpha: 0.4),
          elevation: 8,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
      ),
      cardColor: Colors.white.withValues(alpha: 0.03),
      colorScheme: ColorScheme.dark(
        primary: _currentTheme.primary,
        secondary: _currentTheme.secondary,
        surface: _currentTheme.background,
      ),
    );
  }
}
