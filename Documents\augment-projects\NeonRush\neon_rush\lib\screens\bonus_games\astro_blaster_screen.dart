import 'dart:async';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';

import '../../constants.dart';
import '../../providers/game_provider.dart';

/// Astro Blaster bonus game screen
class AstroBlasterScreen extends StatefulWidget {
  const AstroBlasterScreen({super.key});

  @override
  State<AstroBlasterScreen> createState() => _AstroBlasterScreenState();
}

class _AstroBlasterScreenState extends State<AstroBlasterScreen> {
  static const double gameWidth = 400.0;
  static const double gameHeight = 600.0;
  static const double playerSize = 40.0;
  static const double enemySize = 30.0;
  static const double bulletSize = 6.0;
  
  // Game state
  Timer? gameTimer;
  bool isGameRunning = false;
  bool isGameOver = false;
  int score = 0;
  int highScore = 0;
  int lives = 3;
  int wave = 1;
  
  // Player state
  double playerX = gameWidth / 2 - playerSize / 2;
  double playerY = gameHeight - playerSize - 20;
  
  // Game objects
  List<Enemy> enemies = [];
  List<Bullet> bullets = [];
  List<PowerUp> powerUps = [];
  
  // Timers
  Timer? enemySpawnTimer;
  Timer? powerUpSpawnTimer;
  
  final Random random = Random();

  @override
  void initState() {
    super.initState();
    _loadHighScore();
  }

  @override
  void dispose() {
    gameTimer?.cancel();
    enemySpawnTimer?.cancel();
    powerUpSpawnTimer?.cancel();
    super.dispose();
  }

  void _loadHighScore() {
    final gameProvider = Provider.of<GameProvider>(context, listen: false);
    highScore = gameProvider.getBonusGameHighScore('astro_blaster');
  }

  void _startGame() {
    setState(() {
      score = 0;
      lives = 3;
      wave = 1;
      isGameRunning = true;
      isGameOver = false;
      playerX = gameWidth / 2 - playerSize / 2;
      enemies.clear();
      bullets.clear();
      powerUps.clear();
    });
    
    gameTimer = Timer.periodic(const Duration(milliseconds: 16), (timer) {
      _updateGame();
    });
    
    enemySpawnTimer = Timer.periodic(Duration(milliseconds: (2000 - (wave * 100)).clamp(500, 2000)), (timer) {
      _spawnEnemy();
    });
    
    powerUpSpawnTimer = Timer.periodic(const Duration(seconds: 15), (timer) {
      _spawnPowerUp();
    });
  }

  void _pauseGame() {
    setState(() {
      isGameRunning = !isGameRunning;
    });
    
    if (isGameRunning) {
      gameTimer = Timer.periodic(const Duration(milliseconds: 16), (timer) {
        _updateGame();
      });
    } else {
      gameTimer?.cancel();
    }
  }

  void _updateGame() {
    if (!isGameRunning || isGameOver) return;

    setState(() {
      // Update bullets
      bullets.removeWhere((bullet) {
        bullet.y -= bullet.speed;
        return bullet.y < 0;
      });

      // Update enemies
      enemies.removeWhere((enemy) {
        enemy.y += enemy.speed;
        enemy.x += enemy.velocityX;
        
        // Bounce off walls
        if (enemy.x <= 0 || enemy.x >= gameWidth - enemySize) {
          enemy.velocityX = -enemy.velocityX;
        }
        
        // Check if enemy reached bottom
        if (enemy.y > gameHeight) {
          lives--;
          HapticFeedback.heavyImpact();
          if (lives <= 0) {
            _gameOver();
          }
          return true;
        }
        return false;
      });

      // Update power-ups
      powerUps.removeWhere((powerUp) {
        powerUp.y += 2.0;
        return powerUp.y > gameHeight;
      });

      // Check bullet-enemy collisions
      for (int i = bullets.length - 1; i >= 0; i--) {
        for (int j = enemies.length - 1; j >= 0; j--) {
          if (_checkCollision(
            bullets[i].x, bullets[i].y, bulletSize, bulletSize,
            enemies[j].x, enemies[j].y, enemySize, enemySize,
          )) {
            score += enemies[j].points;
            bullets.removeAt(i);
            enemies.removeAt(j);
            HapticFeedback.lightImpact();
            break;
          }
        }
      }

      // Check player-enemy collisions
      for (int i = enemies.length - 1; i >= 0; i--) {
        if (_checkCollision(
          playerX, playerY, playerSize, playerSize,
          enemies[i].x, enemies[i].y, enemySize, enemySize,
        )) {
          lives--;
          enemies.removeAt(i);
          HapticFeedback.heavyImpact();
          if (lives <= 0) {
            _gameOver();
            return;
          }
        }
      }

      // Check player-powerup collisions
      for (int i = powerUps.length - 1; i >= 0; i--) {
        if (_checkCollision(
          playerX, playerY, playerSize, playerSize,
          powerUps[i].x, powerUps[i].y, 20, 20,
        )) {
          _applyPowerUp(powerUps[i]);
          powerUps.removeAt(i);
          HapticFeedback.selectionClick();
        }
      }

      // Check for wave completion
      if (enemies.isEmpty && score > 0) {
        wave++;
        // Spawn new wave
        for (int i = 0; i < wave + 2; i++) {
          _spawnEnemy();
        }
      }
    });
  }

  bool _checkCollision(double x1, double y1, double w1, double h1,
                      double x2, double y2, double w2, double h2) {
    return x1 < x2 + w2 && x1 + w1 > x2 && y1 < y2 + h2 && y1 + h1 > y2;
  }

  void _spawnEnemy() {
    if (!isGameRunning || isGameOver) return;
    
    enemies.add(Enemy(
      x: random.nextDouble() * (gameWidth - enemySize),
      y: -enemySize,
      speed: 1.0 + (wave * 0.2),
      velocityX: (random.nextDouble() - 0.5) * 2.0,
      points: 10 + (wave * 5),
    ));
  }

  void _spawnPowerUp() {
    if (!isGameRunning || isGameOver) return;
    
    powerUps.add(PowerUp(
      x: random.nextDouble() * (gameWidth - 20),
      y: -20,
      type: PowerUpType.values[random.nextInt(PowerUpType.values.length)],
    ));
  }

  void _applyPowerUp(PowerUp powerUp) {
    switch (powerUp.type) {
      case PowerUpType.extraLife:
        lives++;
        break;
      case PowerUpType.rapidFire:
        // Implement rapid fire logic
        break;
      case PowerUpType.shield:
        // Implement shield logic
        break;
    }
  }

  void _shoot() {
    if (!isGameRunning || isGameOver) return;
    
    bullets.add(Bullet(
      x: playerX + playerSize / 2 - bulletSize / 2,
      y: playerY,
      speed: 8.0,
    ));
  }

  void _gameOver() {
    gameTimer?.cancel();
    enemySpawnTimer?.cancel();
    powerUpSpawnTimer?.cancel();
    
    setState(() {
      isGameOver = true;
      isGameRunning = false;
    });

    // Update high score
    if (score > highScore) {
      highScore = score;
      final gameProvider = Provider.of<GameProvider>(context, listen: false);
      gameProvider.setBonusGameHighScore('astro_blaster', score);
    }

    HapticFeedback.heavyImpact();
  }

  void _updatePlayerPosition(double localX) {
    if (!isGameRunning || isGameOver) return;
    
    setState(() {
      playerX = (localX - playerSize / 2).clamp(0, gameWidth - playerSize);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: NeonColors.background,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        title: Text(
          'Astro Blaster',
          style: GoogleFonts.orbitron(
            color: NeonColors.primaryAccent,
            fontWeight: FontWeight.bold,
            fontSize: 24,
          ),
        ),
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: NeonColors.primaryAccent,
          ),
          onPressed: () => Navigator.of(context).pop(),
        ),
        actions: [
          IconButton(
            icon: Icon(
              isGameRunning ? Icons.pause : Icons.play_arrow,
              color: NeonColors.primaryAccent,
            ),
            onPressed: isGameOver ? null : _pauseGame,
          ),
        ],
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: NeonColors.backgroundGradient,
        ),
        child: Column(
          children: [
            // Score display
            Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  _buildScoreCard('Score', score),
                  _buildScoreCard('High Score', highScore),
                  _buildScoreCard('Lives', lives),
                  _buildScoreCard('Wave', wave),
                ],
              ),
            ),
            
            // Game area
            Expanded(
              child: Center(
                child: Container(
                  width: gameWidth,
                  height: gameHeight,
                  decoration: BoxDecoration(
                    color: NeonColors.surface.withValues(alpha: 0.8),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: NeonColors.primaryAccent.withValues(alpha: 0.5),
                      width: 2,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: NeonColors.primaryAccent.withValues(alpha: 0.3),
                        blurRadius: 20,
                        spreadRadius: 2,
                      ),
                    ],
                  ),
                  child: GestureDetector(
                    onPanUpdate: (details) {
                      _updatePlayerPosition(details.localPosition.dx);
                    },
                    onTapDown: (details) {
                      _updatePlayerPosition(details.localPosition.dx);
                      _shoot();
                    },
                    child: Stack(
                      children: [
                        // Player ship
                        Positioned(
                          left: playerX,
                          top: playerY,
                          child: Container(
                            width: playerSize,
                            height: playerSize,
                            decoration: BoxDecoration(
                              color: NeonColors.primaryAccent,
                              shape: BoxShape.circle,
                              boxShadow: [
                                BoxShadow(
                                  color: NeonColors.primaryAccent.withValues(alpha: 0.5),
                                  blurRadius: 8,
                                  spreadRadius: 1,
                                ),
                              ],
                            ),
                            child: Icon(
                              Icons.rocket_launch,
                              color: NeonColors.background,
                              size: 24,
                            ),
                          ),
                        ),
                        
                        // Enemies
                        ...enemies.map((enemy) => Positioned(
                          left: enemy.x,
                          top: enemy.y,
                          child: Container(
                            width: enemySize,
                            height: enemySize,
                            decoration: BoxDecoration(
                              color: NeonColors.error,
                              shape: BoxShape.circle,
                              boxShadow: [
                                BoxShadow(
                                  color: NeonColors.error.withValues(alpha: 0.5),
                                  blurRadius: 6,
                                  spreadRadius: 1,
                                ),
                              ],
                            ),
                            child: Icon(
                              Icons.bug_report,
                              color: NeonColors.background,
                              size: 16,
                            ),
                          ),
                        )),
                        
                        // Bullets
                        ...bullets.map((bullet) => Positioned(
                          left: bullet.x,
                          top: bullet.y,
                          child: Container(
                            width: bulletSize,
                            height: bulletSize,
                            decoration: BoxDecoration(
                              color: NeonColors.highlights,
                              shape: BoxShape.circle,
                              boxShadow: [
                                BoxShadow(
                                  color: NeonColors.highlights.withValues(alpha: 0.7),
                                  blurRadius: 4,
                                  spreadRadius: 1,
                                ),
                              ],
                            ),
                          ),
                        )),
                        
                        // Power-ups
                        ...powerUps.map((powerUp) => Positioned(
                          left: powerUp.x,
                          top: powerUp.y,
                          child: Container(
                            width: 20,
                            height: 20,
                            decoration: BoxDecoration(
                              color: NeonColors.success,
                              shape: BoxShape.circle,
                              boxShadow: [
                                BoxShadow(
                                  color: NeonColors.success.withValues(alpha: 0.5),
                                  blurRadius: 6,
                                  spreadRadius: 1,
                                ),
                              ],
                            ),
                            child: Icon(
                              _getPowerUpIcon(powerUp.type),
                              color: NeonColors.background,
                              size: 12,
                            ),
                          ),
                        )),
                        
                        // Game over overlay
                        if (isGameOver)
                          Container(
                            color: Colors.black.withValues(alpha: 0.7),
                            child: Center(
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Text(
                                    'Game Over',
                                    style: GoogleFonts.orbitron(
                                      color: NeonColors.error,
                                      fontSize: 24,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                  const SizedBox(height: 16),
                                  Text(
                                    'Final Score: $score',
                                    style: GoogleFonts.orbitron(
                                      color: NeonColors.textPrimary,
                                      fontSize: 18,
                                    ),
                                  ),
                                  Text(
                                    'Wave Reached: $wave',
                                    style: GoogleFonts.orbitron(
                                      color: NeonColors.textPrimary,
                                      fontSize: 16,
                                    ),
                                  ),
                                  if (score == highScore) ...[
                                    const SizedBox(height: 8),
                                    Text(
                                      'New High Score!',
                                      style: GoogleFonts.orbitron(
                                        color: NeonColors.success,
                                        fontSize: 16,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ],
                                  const SizedBox(height: 24),
                                  ElevatedButton(
                                    onPressed: _startGame,
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: NeonColors.primaryAccent,
                                      foregroundColor: NeonColors.background,
                                    ),
                                    child: Text(
                                      'Play Again',
                                      style: GoogleFonts.orbitron(
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
            
            // Controls info
            if (!isGameOver)
              Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  children: [
                    if (!isGameRunning && score == 0)
                      ElevatedButton(
                        onPressed: _startGame,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: NeonColors.primaryAccent,
                          foregroundColor: NeonColors.background,
                          padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
                        ),
                        child: Text(
                          'Start Game',
                          style: GoogleFonts.orbitron(
                            fontWeight: FontWeight.bold,
                            fontSize: 18,
                          ),
                        ),
                      ),
                    const SizedBox(height: 8),
                    Text(
                      'Drag to move • Tap to shoot',
                      style: GoogleFonts.orbitron(
                        color: NeonColors.textSecondary,
                        fontSize: 14,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    Text(
                      'Destroy enemies and collect power-ups!',
                      style: GoogleFonts.orbitron(
                        color: NeonColors.textSecondary,
                        fontSize: 12,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildScoreCard(String label, int value) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: NeonColors.surface.withValues(alpha: 0.8),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: NeonColors.primaryAccent.withValues(alpha: 0.3),
        ),
      ),
      child: Column(
        children: [
          Text(
            label,
            style: GoogleFonts.orbitron(
              color: NeonColors.textSecondary,
              fontSize: 10,
            ),
          ),
          Text(
            value.toString(),
            style: GoogleFonts.orbitron(
              color: NeonColors.primaryAccent,
              fontSize: 14,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  IconData _getPowerUpIcon(PowerUpType type) {
    switch (type) {
      case PowerUpType.extraLife:
        return Icons.favorite;
      case PowerUpType.rapidFire:
        return Icons.speed;
      case PowerUpType.shield:
        return Icons.shield;
    }
  }
}

/// Game object classes
class Enemy {
  double x, y, speed, velocityX;
  int points;
  
  Enemy({required this.x, required this.y, required this.speed, required this.velocityX, required this.points});
}

class Bullet {
  double x, y, speed;
  
  Bullet({required this.x, required this.y, required this.speed});
}

class PowerUp {
  double x, y;
  PowerUpType type;
  
  PowerUp({required this.x, required this.y, required this.type});
}

enum PowerUpType { extraLife, rapidFire, shield }
