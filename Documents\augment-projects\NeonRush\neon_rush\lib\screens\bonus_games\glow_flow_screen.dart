import 'dart:async';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';

import '../../constants.dart';
import '../../providers/game_provider.dart';

/// Glow Flow bonus game screen - connect the flowing lights
class GlowFlowScreen extends StatefulWidget {
  const GlowFlowScreen({super.key});

  @override
  State<GlowFlowScreen> createState() => _GlowFlowScreenState();
}

class _GlowFlowScreenState extends State<GlowFlowScreen> {
  static const int gridSize = 8;
  static const double cellSize = 40.0;
  
  // Game state
  Timer? gameTimer;
  bool isGameRunning = false;
  bool isGameOver = false;
  int score = 0;
  int highScore = 0;
  int level = 1;
  int movesLeft = 20;
  
  // Grid state
  List<List<FlowNode>> grid = [];
  List<Point<int>> path = [];
  Point<int>? startNode;
  Point<int>? endNode;
  bool isDrawing = false;
  
  final Random random = Random();

  @override
  void initState() {
    super.initState();
    _loadHighScore();
    _initializeGrid();
  }

  @override
  void dispose() {
    gameTimer?.cancel();
    super.dispose();
  }

  void _loadHighScore() {
    final gameProvider = Provider.of<GameProvider>(context, listen: false);
    highScore = gameProvider.getBonusGameHighScore('glow_flow');
  }

  void _initializeGrid() {
    grid = List.generate(gridSize, (i) => 
      List.generate(gridSize, (j) => FlowNode(
        x: j,
        y: i,
        type: NodeType.empty,
        isConnected: false,
      ))
    );
  }

  void _startGame() {
    setState(() {
      score = 0;
      level = 1;
      movesLeft = 20;
      isGameRunning = true;
      isGameOver = false;
      path.clear();
    });
    
    _generateLevel();
  }

  void _generateLevel() {
    _initializeGrid();
    
    // Place start and end nodes
    startNode = Point(0, random.nextInt(gridSize));
    endNode = Point(gridSize - 1, random.nextInt(gridSize));
    
    grid[startNode!.y][startNode!.x].type = NodeType.start;
    grid[endNode!.y][endNode!.x].type = NodeType.end;
    
    // Place some obstacles
    int obstacles = (level * 2).clamp(2, 10);
    for (int i = 0; i < obstacles; i++) {
      Point<int> pos;
      do {
        pos = Point(random.nextInt(gridSize), random.nextInt(gridSize));
      } while (pos == startNode || pos == endNode || grid[pos.y][pos.x].type != NodeType.empty);
      
      grid[pos.y][pos.x].type = NodeType.obstacle;
    }
    
    // Place some power nodes
    int powerNodes = (level / 2).floor().clamp(1, 3);
    for (int i = 0; i < powerNodes; i++) {
      Point<int> pos;
      do {
        pos = Point(random.nextInt(gridSize), random.nextInt(gridSize));
      } while (pos == startNode || pos == endNode || grid[pos.y][pos.x].type != NodeType.empty);
      
      grid[pos.y][pos.x].type = NodeType.power;
    }
    
    setState(() {});
  }

  void _onPanStart(DragStartDetails details) {
    if (!isGameRunning || isGameOver || movesLeft <= 0) return;
    
    final localPosition = details.localPosition;
    final gridX = (localPosition.dx / cellSize).floor();
    final gridY = (localPosition.dy / cellSize).floor();
    
    if (gridX >= 0 && gridX < gridSize && gridY >= 0 && gridY < gridSize) {
      final node = grid[gridY][gridX];
      if (node.type == NodeType.start) {
        setState(() {
          isDrawing = true;
          path.clear();
          path.add(Point(gridX, gridY));
          _clearConnections();
          grid[gridY][gridX].isConnected = true;
        });
      }
    }
  }

  void _onPanUpdate(DragUpdateDetails details) {
    if (!isDrawing || !isGameRunning || isGameOver) return;
    
    final localPosition = details.localPosition;
    final gridX = (localPosition.dx / cellSize).floor();
    final gridY = (localPosition.dy / cellSize).floor();
    
    if (gridX >= 0 && gridX < gridSize && gridY >= 0 && gridY < gridSize) {
      final currentPos = Point(gridX, gridY);
      
      if (path.isEmpty || path.last != currentPos) {
        final node = grid[gridY][gridX];
        
        // Check if this is a valid move (adjacent to last position)
        if (path.isNotEmpty) {
          final lastPos = path.last;
          final distance = (currentPos.x - lastPos.x).abs() + (currentPos.y - lastPos.y).abs();
          
          if (distance == 1 && node.type != NodeType.obstacle) {
            setState(() {
              path.add(currentPos);
              grid[gridY][gridX].isConnected = true;
              
              if (node.type == NodeType.power) {
                score += 50;
                HapticFeedback.lightImpact();
              }
            });
          }
        }
      }
    }
  }

  void _onPanEnd(DragEndDetails details) {
    if (!isDrawing) return;
    
    setState(() {
      isDrawing = false;
      
      // Check if path reaches the end
      if (path.isNotEmpty && path.last == endNode) {
        // Level completed!
        score += 100 + (movesLeft * 10);
        level++;
        movesLeft = 20 + (level * 2);
        HapticFeedback.heavyImpact();
        
        // Generate next level after a short delay
        Timer(const Duration(milliseconds: 500), () {
          _generateLevel();
        });
      } else {
        // Move used but didn't complete
        movesLeft--;
        if (movesLeft <= 0) {
          _gameOver();
        }
      }
    });
  }

  void _clearConnections() {
    for (int i = 0; i < gridSize; i++) {
      for (int j = 0; j < gridSize; j++) {
        if (grid[i][j].type == NodeType.empty || grid[i][j].type == NodeType.power) {
          grid[i][j].isConnected = false;
        }
      }
    }
  }

  void _gameOver() {
    gameTimer?.cancel();
    setState(() {
      isGameOver = true;
      isGameRunning = false;
    });

    // Update high score
    if (score > highScore) {
      highScore = score;
      final gameProvider = Provider.of<GameProvider>(context, listen: false);
      gameProvider.setBonusGameHighScore('glow_flow', score);
    }

    HapticFeedback.heavyImpact();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: NeonColors.background,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        title: Text(
          'Glow Flow',
          style: GoogleFonts.orbitron(
            color: NeonColors.primaryAccent,
            fontWeight: FontWeight.bold,
            fontSize: 24,
          ),
        ),
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: NeonColors.primaryAccent,
          ),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: NeonColors.backgroundGradient,
        ),
        child: Column(
          children: [
            // Score display
            Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  _buildScoreCard('Score', score),
                  _buildScoreCard('High Score', highScore),
                  _buildScoreCard('Level', level),
                  _buildScoreCard('Moves', movesLeft),
                ],
              ),
            ),
            
            // Game area
            Expanded(
              child: Center(
                child: Container(
                  width: gridSize * cellSize,
                  height: gridSize * cellSize,
                  decoration: BoxDecoration(
                    color: NeonColors.surface.withValues(alpha: 0.8),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: NeonColors.primaryAccent.withValues(alpha: 0.5),
                      width: 2,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: NeonColors.primaryAccent.withValues(alpha: 0.3),
                        blurRadius: 20,
                        spreadRadius: 2,
                      ),
                    ],
                  ),
                  child: GestureDetector(
                    onPanStart: _onPanStart,
                    onPanUpdate: _onPanUpdate,
                    onPanEnd: _onPanEnd,
                    child: Stack(
                      children: [
                        // Grid
                        CustomPaint(
                          size: Size(gridSize * cellSize, gridSize * cellSize),
                          painter: GlowFlowPainter(
                            grid: grid,
                            path: path,
                            cellSize: cellSize,
                          ),
                        ),
                        
                        // Game over overlay
                        if (isGameOver)
                          Container(
                            color: Colors.black.withValues(alpha: 0.7),
                            child: Center(
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Text(
                                    'Game Over',
                                    style: GoogleFonts.orbitron(
                                      color: NeonColors.error,
                                      fontSize: 24,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                  const SizedBox(height: 16),
                                  Text(
                                    'Final Score: $score',
                                    style: GoogleFonts.orbitron(
                                      color: NeonColors.textPrimary,
                                      fontSize: 18,
                                    ),
                                  ),
                                  Text(
                                    'Levels Completed: ${level - 1}',
                                    style: GoogleFonts.orbitron(
                                      color: NeonColors.textPrimary,
                                      fontSize: 16,
                                    ),
                                  ),
                                  if (score == highScore) ...[
                                    const SizedBox(height: 8),
                                    Text(
                                      'New High Score!',
                                      style: GoogleFonts.orbitron(
                                        color: NeonColors.success,
                                        fontSize: 16,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ],
                                  const SizedBox(height: 24),
                                  ElevatedButton(
                                    onPressed: _startGame,
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: NeonColors.primaryAccent,
                                      foregroundColor: NeonColors.background,
                                    ),
                                    child: Text(
                                      'Play Again',
                                      style: GoogleFonts.orbitron(
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
            
            // Instructions and controls
            if (!isGameOver)
              Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  children: [
                    if (!isGameRunning)
                      ElevatedButton(
                        onPressed: _startGame,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: NeonColors.primaryAccent,
                          foregroundColor: NeonColors.background,
                          padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
                        ),
                        child: Text(
                          'Start Game',
                          style: GoogleFonts.orbitron(
                            fontWeight: FontWeight.bold,
                            fontSize: 18,
                          ),
                        ),
                      ),
                    const SizedBox(height: 8),
                    Text(
                      'Draw a path from start to end',
                      style: GoogleFonts.orbitron(
                        color: NeonColors.textSecondary,
                        fontSize: 14,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    Text(
                      'Collect power nodes for bonus points!',
                      style: GoogleFonts.orbitron(
                        color: NeonColors.textSecondary,
                        fontSize: 12,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildScoreCard(String label, int value) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: NeonColors.surface.withValues(alpha: 0.8),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: NeonColors.primaryAccent.withValues(alpha: 0.3),
        ),
      ),
      child: Column(
        children: [
          Text(
            label,
            style: GoogleFonts.orbitron(
              color: NeonColors.textSecondary,
              fontSize: 10,
            ),
          ),
          Text(
            value.toString(),
            style: GoogleFonts.orbitron(
              color: NeonColors.primaryAccent,
              fontSize: 14,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }
}

/// Game object classes
class FlowNode {
  final int x, y;
  NodeType type;
  bool isConnected;
  
  FlowNode({required this.x, required this.y, required this.type, this.isConnected = false});
}

enum NodeType { empty, start, end, obstacle, power }

/// Custom painter for the glow flow game
class GlowFlowPainter extends CustomPainter {
  final List<List<FlowNode>> grid;
  final List<Point<int>> path;
  final double cellSize;

  GlowFlowPainter({
    required this.grid,
    required this.path,
    required this.cellSize,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final emptyPaint = Paint()
      ..color = NeonColors.surface.withValues(alpha: 0.3)
      ..style = PaintingStyle.fill;
    
    final startPaint = Paint()
      ..color = NeonColors.success
      ..style = PaintingStyle.fill;
    
    final endPaint = Paint()
      ..color = NeonColors.error
      ..style = PaintingStyle.fill;
    
    final obstaclePaint = Paint()
      ..color = NeonColors.textSecondary
      ..style = PaintingStyle.fill;
    
    final powerPaint = Paint()
      ..color = NeonColors.highlights
      ..style = PaintingStyle.fill;
    
    final pathPaint = Paint()
      ..color = NeonColors.primaryAccent
      ..style = PaintingStyle.stroke
      ..strokeWidth = 4.0;

    // Draw grid cells
    for (int i = 0; i < grid.length; i++) {
      for (int j = 0; j < grid[i].length; j++) {
        final node = grid[i][j];
        final rect = Rect.fromLTWH(
          j * cellSize + 2,
          i * cellSize + 2,
          cellSize - 4,
          cellSize - 4,
        );
        
        Paint paint;
        switch (node.type) {
          case NodeType.start:
            paint = startPaint;
            break;
          case NodeType.end:
            paint = endPaint;
            break;
          case NodeType.obstacle:
            paint = obstaclePaint;
            break;
          case NodeType.power:
            paint = node.isConnected ? emptyPaint : powerPaint;
            break;
          case NodeType.empty:
            paint = emptyPaint;
            break;
        }
        
        if (node.isConnected && node.type != NodeType.start) {
          paint = Paint()
            ..color = NeonColors.primaryAccent.withValues(alpha: 0.5)
            ..style = PaintingStyle.fill;
        }
        
        canvas.drawRRect(
          RRect.fromRectAndRadius(rect, const Radius.circular(4)),
          paint,
        );
      }
    }

    // Draw path
    if (path.length > 1) {
      final pathPoints = path.map((p) => Offset(
        p.x * cellSize + cellSize / 2,
        p.y * cellSize + cellSize / 2,
      )).toList();
      
      for (int i = 0; i < pathPoints.length - 1; i++) {
        canvas.drawLine(pathPoints[i], pathPoints[i + 1], pathPaint);
      }
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
