import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../providers/theme_provider.dart';
import '../../games/bonus/neon_stack_game.dart';
import '../../core/sound_manager.dart';

/// Screen wrapper for Neon Stack bonus game
class NeonStackScreen extends StatefulWidget {
  const NeonStackScreen({super.key});

  @override
  State<NeonStackScreen> createState() => _NeonStackScreenState();
}

class _NeonStackScreenState extends State<NeonStackScreen> {
  @override
  void initState() {
    super.initState();
    SoundManager().playGameMusic();
  }

  @override
  void dispose() {
    SoundManager().playMenuMusic();
    super.dispose();
  }

  void _onGameComplete(int score, int tokens) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.black.withValues(alpha: 0.9),
        title: Text(
          'Game Complete!',
          style: TextStyle(
            color: Theme.of(context).primaryColor,
            fontSize: 24,
            fontWeight: FontWeight.bold,
          ),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Score: $score',
              style: const TextStyle(color: Colors.white, fontSize: 18),
            ),
            Text(
              'Tokens Earned: $tokens',
              style: TextStyle(
                color: Theme.of(context).primaryColor,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              Navigator.of(context).pop();
            },
            child: Text(
              'Continue',
              style: TextStyle(color: Theme.of(context).primaryColor),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        return Scaffold(
          appBar: AppBar(
            title: const Text('Neon Stack'),
            backgroundColor: Colors.black,
            foregroundColor: themeProvider.currentTheme.primaryColor,
          ),
          body: NeonStackGame(
            theme: themeProvider.currentTheme,
            onGameComplete: _onGameComplete,
          ),
        );
      },
    );
  }
}
