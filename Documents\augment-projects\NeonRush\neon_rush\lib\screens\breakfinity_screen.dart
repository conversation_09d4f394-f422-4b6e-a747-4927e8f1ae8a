import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'dart:math';
import 'dart:async';

import '../providers/breakfinity_provider.dart';
import '../constants/breakfinity_constants.dart';
import '../core/constants.dart' as core_constants;
import '../ui/neon_button.dart';
import '../ui/neon_text.dart';
import '../models/breakfinity_section.dart';
import '../models/breakfinity_reward.dart' as reward_models;
import '../providers/game_provider.dart';
import '../services/token_service.dart';
import '../models/token_transaction.dart';
import '../ui/particle_system.dart';
import '../widgets/breakfinity_particle_system.dart';
import '../widgets/breakfinity_section_renderer.dart';
import '../widgets/breakfinity_reward_display.dart';
import '../models/breakfinity_layer_theme.dart';

/// Represents floating text for reward notifications
class FloatingText {
  String text;
  Offset position;
  Offset velocity;
  Color color;
  double fontSize;
  double opacity;
  DateTime createdAt;

  FloatingText({
    required this.text,
    required this.position,
    required this.velocity,
    required this.color,
    required this.fontSize,
    this.opacity = 1.0,
    required this.createdAt,
  });
}

/// Main screen for the Breakfinity game
class BreakfinityScreen extends StatefulWidget {
  const BreakfinityScreen({super.key});

  @override
  State<BreakfinityScreen> createState() => _BreakfinityScreenState();
}

class _BreakfinityScreenState extends State<BreakfinityScreen>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late AnimationController _glowController;
  late AnimationController _backgroundController;
  final List<Particle> _particles = [];
  final Random _random = Random();
  final GlobalKey _rewardOverlayKey = GlobalKey();
  final List<FloatingText> _floatingTexts = [];
  Timer? _floatingTextTimer;

  @override
  void initState() {
    super.initState();
    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    )..repeat(reverse: true);

    _glowController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    )..repeat();

    _backgroundController = AnimationController(
      duration: const Duration(seconds: 10),
      vsync: this,
    )..repeat();

    // Initialize the provider
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final provider = context.read<BreakfinityProvider>();
      // Disabled particle effect to fix strange dots issue
      // provider.onParticleEffect = _createParticleEffect;
      // Enable reward callback with floating text implementation
      provider.onRewardDiscovered = _showFloatingReward;
      // Removed enhanced reward popup to eliminate annoying popups
      // provider.onEnhancedRewardDiscovered = _showEnhancedReward;
      provider.initialize();
    });
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _glowController.dispose();
    _backgroundController.dispose();
    _floatingTextTimer?.cancel();
    super.dispose();
  }

  void _createParticleEffect(Offset position, Color color) {
    for (int i = 0; i < 10; i++) {
      final angle = _random.nextDouble() * 2 * pi;
      final speed = 50 + _random.nextDouble() * 100;
      final velocity = Offset(
        cos(angle) * speed,
        sin(angle) * speed,
      );

      _particles.add(Particle(
        position: position,
        velocity: velocity,
        color: color,
        size: 2 + _random.nextDouble() * 4,
        life: const Duration(milliseconds: 1000),
        type: ParticleType.fragment,
      ));
    }
    setState(() {});
  }

  void _showRewardPopup(String message, Map<String, dynamic> rewards) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        backgroundColor: BreakfinityConstants.structureColor,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
          side: BorderSide(
            color: BreakfinityConstants.glowColor,
            width: 2,
          ),
        ),
        title: NeonText(
          'Reward Found!',
          fontSize: 20,
          textColor: BreakfinityConstants.glowColor,
          glowColor: BreakfinityConstants.glowColor,
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            NeonText(
              message,
              fontSize: 16,
              textColor: Colors.white,
              glowColor: Colors.white.withOpacity(0.5),
            ),
            const SizedBox(height: 16),
            if (rewards.containsKey('tokens'))
              NeonText(
                '+${rewards['tokens']} Tokens',
                fontSize: 18,
                textColor: Colors.yellow,
                glowColor: Colors.yellow.withOpacity(0.5),
              ),
            if (rewards.containsKey('powerUp'))
              NeonText(
                'Power-up: ${rewards['powerUp']}',
                fontSize: 16,
                textColor: Colors.purple,
                glowColor: Colors.purple.withOpacity(0.5),
              ),
          ],
        ),
        actions: [
          NeonButton(
            text: 'Collect',
            onPressed: () => Navigator.of(context).pop(),
            glowColor: BreakfinityConstants.glowColor,
          ),
        ],
      ),
    );
  }

  void _showEnhancedReward(reward_models.BreakfinityReward reward) {
    // Show the enhanced reward display
    final overlay = _rewardOverlayKey.currentState;
    if (overlay != null) {
      (overlay as dynamic).showReward(reward);
    }
  }

  @override
  Widget build(BuildContext context) {
    return BreakfinityRewardOverlay(
      key: _rewardOverlayKey,
      child: Scaffold(
      backgroundColor: BreakfinityConstants.backgroundColor,
      body: Consumer<BreakfinityProvider>(
        builder: (context, provider, child) {
          if (provider.isLoading) {
            return _buildLoadingScreen();
          }

          return Stack(
            children: [
              // Background gradient
              _buildBackground(),
              
              // Main game area
              SafeArea(
                child: Column(
                  children: [
                    // Header with stats
                    _buildHeader(provider),
                    
                    // Game viewport
                    Expanded(
                      child: _buildGameViewport(provider),
                    ),
                    
                    // Bottom controls
                    _buildBottomControls(provider),

                    // Bottom padding to prevent clipping under system nav buttons
                    SizedBox(height: core_constants.GameConstants.bottomSystemNavPadding),
                  ],
                ),
              ),
              
              // Active power-ups overlay
              _buildPowerUpsOverlay(provider),

              // Floating text overlay
              _buildFloatingTextOverlay(),
            ],
          );
        },
      ),
    ),
    );
  }

  Widget _buildLoadingScreen() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(
                color: BreakfinityConstants.glowColor,
                width: 3,
              ),
              boxShadow: [
                BoxShadow(
                  color: BreakfinityConstants.glowColor.withOpacity(0.5),
                  blurRadius: 20,
                  spreadRadius: 5,
                ),
              ],
            ),
            child: const CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation(BreakfinityConstants.glowColor),
              strokeWidth: 3,
            ),
          ).animate(onPlay: (controller) => controller.repeat())
              .scale(duration: 1.seconds, curve: Curves.easeInOut)
              .then()
              .scale(begin: const Offset(1.2, 1.2), end: const Offset(1.0, 1.0)),
          
          const SizedBox(height: 24),
          
          NeonText(
            'LOADING BREAKFINITY',
            fontSize: 24,
            textColor: BreakfinityConstants.glowColor,
            glowColor: BreakfinityConstants.glowColor,
          ),
        ],
      ),
    );
  }

  Widget _buildBackground() {
    return AnimatedBuilder(
      animation: _backgroundController,
      builder: (context, child) {
        return Container(
          decoration: BoxDecoration(
            gradient: RadialGradient(
              center: Alignment(
                sin(_backgroundController.value * 2 * pi) * 0.3,
                cos(_backgroundController.value * 2 * pi) * 0.3,
              ),
              radius: 1.5 + sin(_backgroundController.value * 4 * pi) * 0.2,
              colors: [
                BreakfinityConstants.backgroundColor.withOpacity(0.8),
                BreakfinityConstants.backgroundColor,
                Colors.black,
              ],
            ),
          ),
          child: CustomPaint(
            painter: ParticlePainter(_particles),
            size: Size.infinite,
          ),
        );
      },
    );
  }

  Widget _buildHeader(BreakfinityProvider provider) {
    final progress = provider.progress;
    
    return Container(
      height: BreakfinityConstants.headerHeight,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: BreakfinityConstants.structureColor.withOpacity(0.3),
        border: Border(
          bottom: BorderSide(
            color: BreakfinityConstants.glowColor.withOpacity(0.3),
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          // Back button
          IconButton(
            onPressed: () => Navigator.pop(context),
            icon: Icon(
              Icons.arrow_back,
              color: BreakfinityConstants.glowColor,
            ),
          ),
          
          const SizedBox(width: 16),
          
          // Layer info
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                NeonText(
                  'LAYER ${progress.currentLayer}',
                  fontSize: 20,
                  textColor: BreakfinityConstants.glowColor,
                  glowColor: BreakfinityConstants.glowColor,
                ),
                
                const SizedBox(height: 4),
                
                // Progress bar
                Container(
                  height: 6,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(3),
                    color: BreakfinityConstants.structureColor,
                  ),
                  child: FractionallySizedBox(
                    alignment: Alignment.centerLeft,
                    widthFactor: provider.progress.getCurrentLayerProgress(),
                    child: Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(3),
                        color: BreakfinityConstants.glowColor,
                        boxShadow: [
                          BoxShadow(
                            color: BreakfinityConstants.glowColor.withOpacity(0.5),
                            blurRadius: 6,
                            spreadRadius: 1,
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          
          const SizedBox(width: 16),
          
          // Stats
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              NeonText(
                'DMG: ${progress.getEffectiveDamage()}',
                fontSize: 14,
                textColor: BreakfinityConstants.destroyColor,
                glowColor: BreakfinityConstants.destroyColor,
              ),
              
              const SizedBox(height: 4),
              
              NeonText(
                'TAPS: ${progress.totalTaps}',
                fontSize: 12,
                textColor: Colors.white.withOpacity(0.7),
                glowColor: Colors.white.withOpacity(0.3),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildGameViewport(BreakfinityProvider provider) {
    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            provider.currentLayerTheme.primaryColor.withOpacity(0.1),
            provider.currentLayerTheme.secondaryColor.withOpacity(0.1),
          ],
        ),
        border: Border.all(
          color: provider.currentLayerTheme.primaryColor.withOpacity(0.3),
          width: 2,
        ),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(14),
        child: Stack(
          children: [
            // Background animation
            AnimatedBuilder(
              animation: _backgroundController,
              builder: (context, child) {
                return CustomPaint(
                  size: Size.infinite,
                  painter: _BackgroundPainter(
                    theme: provider.currentLayerTheme,
                    animationValue: _backgroundController.value,
                  ),
                );
              },
            ),

            // Game sections
            _buildSections(provider),

            // Particle overlay (ignore pointer events so taps can reach sections below)
            IgnorePointer(
              child: CustomPaint(
                size: Size.infinite,
                painter: _ParticlePainter(_particles),
              ),
            ),

            // Reward overlay (commented out for now)
            // if (provider.showRewardOverlay)
            //   _buildRewardOverlay(provider),
          ],
        ),
      ),
    );
  }

  Widget _buildSections(BreakfinityProvider provider) {
    return _buildSectionGrid(provider);
  }

  Widget _buildRewardOverlay(BreakfinityProvider provider) {
    return Container(
      color: Colors.black54,
      child: Center(
        child: Container(
          padding: const EdgeInsets.all(24),
          margin: const EdgeInsets.all(32),
          decoration: BoxDecoration(
            color: BreakfinityConstants.structureColor,
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: provider.currentLayerTheme.primaryColor,
              width: 2,
            ),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              NeonText(
                'Reward Found!',
                fontSize: 24,
                glowColor: provider.currentLayerTheme.primaryColor,
              ),
              const SizedBox(height: 16),
              // Add reward display logic here
              NeonButton(
                text: 'Collect',
                onPressed: () {
                  // Handle reward collection
                },
                glowColor: provider.currentLayerTheme.primaryColor,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSectionGrid(BreakfinityProvider provider) {
    final sections = provider.currentLayerSections.values.toList();

    return GridView.builder(
      padding: EdgeInsets.zero,
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: BreakfinityConstants.sectionsPerRow,
        crossAxisSpacing: 0, // No spacing between sections
        mainAxisSpacing: 0,  // No spacing between sections
        childAspectRatio: 1.0,
      ),
      itemCount: sections.length,
      itemBuilder: (context, index) {
        final section = sections[index];
        return _buildSection(section, provider);
      },
    );
  }

  Widget _buildSection(BreakfinitySection section, BreakfinityProvider provider) {
    return BreakfinitySectionRenderer(
      section: section,
      onTap: section.isDestroyed ? null : () => provider.tapSection(section.id),
      showCracks: true,
      showGlow: true,
      animationProgress: _pulseController.value,
    );
  }



  /// Get color for a specific layer
  Color _getLayerColor(int layer) {
    final colors = [
      Colors.cyan,
      Colors.purple,
      Colors.orange,
      Colors.green,
      Colors.pink,
      Colors.yellow,
      Colors.red,
      Colors.blue,
    ];
    return colors[layer % colors.length];
  }

  Color _getSectionTypeColor(SectionType type) {
    switch (type) {
      case SectionType.weak:
        return Colors.lightBlue;
      case SectionType.reinforced:
        return Colors.grey;
      case SectionType.crystal:
        return Colors.yellow;
      case SectionType.mystery:
        return Colors.purple;
      case SectionType.explosive:
        return Colors.red;
      case SectionType.regenerating:
        return Colors.green;
      default:
        return Colors.white;
    }
  }

  Widget _buildBottomControls(BreakfinityProvider provider) {
    return Container(
      height: BreakfinityConstants.bottomPanelHeight,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: BreakfinityConstants.structureColor.withOpacity(0.3),
        border: Border(
          top: BorderSide(
            color: BreakfinityConstants.glowColor.withOpacity(0.3),
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          // Power-ups button
          Expanded(
            child: NeonButton(
              text: 'POWER-UPS',
              onPressed: () => _showPowerUpsDialog(provider),
              glowColor: BreakfinityConstants.glowColor,
            ),
          ),

          const SizedBox(width: 16),

          // Upgrades button
          Expanded(
            child: NeonButton(
              text: 'UPGRADES',
              onPressed: () => _showUpgradesDialog(provider),
              glowColor: BreakfinityConstants.destroyColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPowerUpsOverlay(BreakfinityProvider provider) {
    if (provider.activePowerUps.isEmpty) return const SizedBox.shrink();
    
    return Positioned(
      top: 100,
      right: 16,
      child: Column(
        children: provider.activePowerUps.entries.map((entry) {
          return Container(
            margin: const EdgeInsets.only(bottom: 8),
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(20),
              color: BreakfinityConstants.glowColor.withOpacity(0.2),
              border: Border.all(
                color: BreakfinityConstants.glowColor,
                width: 1,
              ),
            ),
            child: NeonText(
              entry.key.toUpperCase(),
              fontSize: 12,
              textColor: BreakfinityConstants.glowColor,
              glowColor: BreakfinityConstants.glowColor,
            ),
          );
        }).toList(),
      ),
    );
  }

  void _showPowerUpsDialog(BreakfinityProvider provider) {
    final gameProvider = Provider.of<GameProvider>(context, listen: false);
    final currentTokens = gameProvider.tokens;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: BreakfinityConstants.backgroundColor,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
          side: BorderSide(
            color: BreakfinityConstants.glowColor,
            width: 2,
          ),
        ),
        title: NeonText(
          'POWER-UPS',
          fontSize: 24,
          textColor: BreakfinityConstants.glowColor,
          glowColor: BreakfinityConstants.glowColor,
        ),
        content: SizedBox(
          width: double.maxFinite,
          height: 400,
          child: ListView(
            children: [
              _buildPowerUpCard(
                'Mega Tap',
                'Increases tap damage by 5x for 30 seconds',
                Icons.flash_on,
                const Color(0xFFFFD93D),
                100,
                currentTokens,
                provider.activePowerUps.containsKey('mega_tap'),
                () => _activatePowerUp(provider, 'mega_tap', 100),
              ),
              const SizedBox(height: 12),
              _buildPowerUpCard(
                'Speed Boost',
                'Increases auto-tapper speed by 3x for 60 seconds',
                Icons.speed,
                const Color(0xFF3498DB),
                150,
                currentTokens,
                provider.activePowerUps.containsKey('speed_boost'),
                () => _activatePowerUp(provider, 'speed_boost', 150),
              ),
              const SizedBox(height: 12),
              _buildPowerUpCard(
                'Layer Bomb',
                'Destroys 25% of remaining sections in current layer',
                Icons.flash_on,
                const Color(0xFFE74C3C),
                500,
                currentTokens,
                false, // Instant effect
                () => _activatePowerUp(provider, 'layer_bomb', 500),
              ),
              const SizedBox(height: 12),
              _buildPowerUpCard(
                'Crystal Vision',
                'Reveals all crystal sections for 120 seconds',
                Icons.visibility,
                const Color(0xFF9B59B6),
                200,
                currentTokens,
                provider.activePowerUps.containsKey('crystal_vision'),
                () => _activatePowerUp(provider, 'crystal_vision', 200),
              ),
            ],
          ),
        ),
        actions: [
          NeonButton(
            text: 'CLOSE',
            onPressed: () => Navigator.of(context).pop(),
            glowColor: BreakfinityConstants.glowColor,
            height: 40,
          ),
        ],
      ),
    );
  }

  void _showUpgradesDialog(BreakfinityProvider provider) {
    final gameProvider = Provider.of<GameProvider>(context, listen: false);
    final currentTokens = gameProvider.tokens;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: BreakfinityConstants.backgroundColor,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
          side: BorderSide(
            color: BreakfinityConstants.destroyColor,
            width: 2,
          ),
        ),
        title: NeonText(
          'UPGRADES',
          fontSize: 24,
          textColor: BreakfinityConstants.destroyColor,
          glowColor: BreakfinityConstants.destroyColor,
        ),
        content: SizedBox(
          width: double.maxFinite,
          height: 400,
          child: ListView(
            children: [
              _buildUpgradeCard(
                'Damage Boost',
                'Increase base damage permanently',
                Icons.flash_on,
                BreakfinityConstants.destroyColor,
                _getUpgradeLevel(provider, 'damage_boost'),
                _calculateUpgradeCost(_getUpgradeLevel(provider, 'damage_boost'), 100),
                currentTokens,
                () => _purchaseUpgrade(provider, 'damage_boost', 100),
              ),
              const SizedBox(height: 16),
              _buildUpgradeCard(
                'Auto-Tapper',
                'Increase auto-tapper frequency',
                Icons.touch_app,
                const Color(0xFF9B59B6),
                _getUpgradeLevel(provider, 'auto_tapper'),
                _calculateUpgradeCost(_getUpgradeLevel(provider, 'auto_tapper'), 200),
                currentTokens,
                () => _purchaseUpgrade(provider, 'auto_tapper', 200),
              ),
              const SizedBox(height: 16),
              _buildUpgradeCard(
                'Section Scanner',
                'Reveals hidden sections',
                Icons.search,
                const Color(0xFFE67E22),
                _getUpgradeLevel(provider, 'section_scanner'),
                _calculateUpgradeCost(_getUpgradeLevel(provider, 'section_scanner'), 150),
                currentTokens,
                () => _purchaseUpgrade(provider, 'section_scanner', 150),
              ),
              const SizedBox(height: 16),
              _buildUpgradeCard(
                'Crystal Finder',
                'Increase crystal section spawn rate',
                Icons.diamond,
                const Color(0xFFF1C40F),
                _getUpgradeLevel(provider, 'crystal_finder'),
                _calculateUpgradeCost(_getUpgradeLevel(provider, 'crystal_finder'), 300),
                currentTokens,
                () => _purchaseUpgrade(provider, 'crystal_finder', 300),
              ),
            ],
          ),
        ),
        actions: [
          NeonButton(
            text: 'CLOSE',
            onPressed: () => Navigator.of(context).pop(),
            glowColor: BreakfinityConstants.destroyColor,
            height: 40,
          ),
        ],
      ),
    );
  }

  Widget _buildPowerUpCard(
    String name,
    String description,
    IconData icon,
    Color color,
    int cost,
    int currentTokens,
    bool isActive,
    VoidCallback onActivate,
  ) {
    final canAfford = currentTokens >= cost;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        color: isActive
            ? color.withOpacity(0.3)
            : BreakfinityConstants.structureColor.withOpacity(0.2),
        border: Border.all(
          color: isActive
              ? color
              : BreakfinityConstants.glowColor.withOpacity(0.3),
          width: 2,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: color, size: 24),
              const SizedBox(width: 12),
              Expanded(
                child: NeonText(
                  name,
                  fontSize: 16,
                  textColor: color,
                  glowColor: color,
                ),
              ),
              if (isActive)
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12),
                    color: Colors.green.withOpacity(0.3),
                    border: Border.all(color: Colors.green),
                  ),
                  child: NeonText(
                    'ACTIVE',
                    fontSize: 10,
                    textColor: Colors.green,
                    glowColor: Colors.green,
                  ),
                ),
            ],
          ),
          const SizedBox(height: 8),
          NeonText(
            description,
            fontSize: 12,
            textColor: Colors.white.withOpacity(0.8),
            glowColor: Colors.white.withOpacity(0.3),
          ),
          const SizedBox(height: 12),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              NeonText(
                'Cost: $cost tokens',
                fontSize: 12,
                textColor: canAfford ? Colors.yellow : Colors.red,
                glowColor: canAfford ? Colors.yellow.withOpacity(0.5) : Colors.red.withOpacity(0.5),
              ),
              SizedBox(
                width: 100,
                child: NeonButton(
                  text: isActive ? 'ACTIVE' : 'ACTIVATE',
                  onPressed: isActive || !canAfford ? null : onActivate,
                  glowColor: color,
                  height: 32,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildUpgradeCard(
    String name,
    String description,
    IconData icon,
    Color color,
    double currentLevel,
    int cost,
    int currentTokens,
    VoidCallback onUpgrade,
  ) {
    final canAfford = currentTokens >= cost;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        color: BreakfinityConstants.structureColor.withOpacity(0.2),
        border: Border.all(
          color: color.withOpacity(0.5),
          width: 2,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: color, size: 24),
              const SizedBox(width: 12),
              Expanded(
                child: NeonText(
                  name,
                  fontSize: 16,
                  textColor: color,
                  glowColor: color,
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                  color: color.withOpacity(0.3),
                  border: Border.all(color: color),
                ),
                child: NeonText(
                  'LV ${currentLevel.toInt()}',
                  fontSize: 10,
                  textColor: color,
                  glowColor: color,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          NeonText(
            description,
            fontSize: 12,
            textColor: Colors.white.withOpacity(0.8),
            glowColor: Colors.white.withOpacity(0.3),
          ),
          const SizedBox(height: 12),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              NeonText(
                'Cost: $cost tokens',
                fontSize: 12,
                textColor: canAfford ? Colors.yellow : Colors.red,
                glowColor: canAfford ? Colors.yellow.withOpacity(0.5) : Colors.red.withOpacity(0.5),
              ),
              SizedBox(
                width: 100,
                child: NeonButton(
                  text: 'UPGRADE',
                  onPressed: canAfford ? onUpgrade : null,
                  glowColor: color,
                  height: 32,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Future<void> _activatePowerUp(BreakfinityProvider provider, String powerUpId, int cost) async {
    final gameProvider = Provider.of<GameProvider>(context, listen: false);

    if (gameProvider.tokens >= cost) {
      final success = await TokenService.spendTokens(
        amount: cost,
        type: TokenTransactionType.powerUpPurchase,
        description: 'Breakfinity power-up: $powerUpId',
      );

      if (success) {
        await provider.activatePowerUp(powerUpId);
        if (mounted) {
          Navigator.of(context).pop();
        }
      }
    }
  }

  double _getUpgradeLevel(BreakfinityProvider provider, String upgradeId) {
    return (provider.progress.upgrades[upgradeId] as num?)?.toDouble() ?? 0.0;
  }

  int _calculateUpgradeCost(double currentLevel, int baseCost) {
    return (baseCost * (1.5 * (currentLevel + 1))).round();
  }

  Future<void> _purchaseUpgrade(BreakfinityProvider provider, String upgradeId, int baseCost) async {
    final gameProvider = Provider.of<GameProvider>(context, listen: false);
    final currentLevel = _getUpgradeLevel(provider, upgradeId);
    final cost = _calculateUpgradeCost(currentLevel, baseCost);

    if (gameProvider.tokens >= cost) {
      final success = await TokenService.spendTokens(
        amount: cost,
        type: TokenTransactionType.powerUpUpgrade,
        description: 'Breakfinity upgrade: $upgradeId',
      );

      if (success) {
        // Apply the upgrade
        provider.purchaseUpgrade(upgradeId);

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Upgrade purchased successfully!'),
              backgroundColor: BreakfinityConstants.glowColor,
            ),
          );
          Navigator.of(context).pop();
        }
      }
    }
  }
  /// Show floating reward text
  void _showFloatingReward(String message, Map<String, dynamic> rewards) {
    // Check if there are token rewards of 20 or less
    final tokenReward = rewards['tokens'] as int? ?? 0;
    if (tokenReward > 0 && tokenReward <= 20) {
      final renderBox = context.findRenderObject() as RenderBox?;
      if (renderBox != null) {
        final size = renderBox.size;
        final randomX = _random.nextDouble() * (size.width - 100) + 50;
        final randomY = _random.nextDouble() * (size.height - 200) + 100;

        final floatingText = FloatingText(
          text: '+$tokenReward',
          position: Offset(randomX, randomY),
          velocity: Offset(0, -50), // Float upward
          color: Colors.yellow,
          fontSize: 24,
          createdAt: DateTime.now(),
        );

        setState(() {
          _floatingTexts.add(floatingText);
        });

        // Start animation timer if not already running
        _startFloatingTextAnimation();
      }
    }
  }

  /// Start floating text animation
  void _startFloatingTextAnimation() {
    _floatingTextTimer?.cancel();
    _floatingTextTimer = Timer.periodic(const Duration(milliseconds: 16), (timer) {
      setState(() {
        final now = DateTime.now();

        // Update floating texts
        for (final text in _floatingTexts) {
          final elapsed = now.difference(text.createdAt).inMilliseconds / 1000.0;
          text.position += text.velocity * 0.016; // 16ms frame time
          text.opacity = (1.0 - elapsed / 2.0).clamp(0.0, 1.0); // Fade over 2 seconds
        }

        // Remove expired texts
        _floatingTexts.removeWhere((text) => text.opacity <= 0);

        // Stop timer if no more texts
        if (_floatingTexts.isEmpty) {
          timer.cancel();
        }
      });
    });
  }

  /// Build floating text overlay
  Widget _buildFloatingTextOverlay() {
    return IgnorePointer(
      child: Stack(
        children: _floatingTexts.map((text) {
          return Positioned(
            left: text.position.dx,
            top: text.position.dy,
            child: Opacity(
              opacity: text.opacity,
              child: Text(
                text.text,
                style: TextStyle(
                  color: text.color,
                  fontSize: text.fontSize,
                  fontWeight: FontWeight.bold,
                  shadows: [
                    Shadow(
                      color: text.color.withOpacity(0.8),
                      blurRadius: 4,
                    ),
                  ],
                ),
              ),
            ),
          );
        }).toList(),
      ),
    );
  }
}

/// Custom painter for drawing cracks on sections
class CrackPainter extends CustomPainter {
  final List<Offset> cracks;
  final double progress;

  CrackPainter({required this.cracks, required this.progress});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = BreakfinityConstants.crackColor.withOpacity(progress)
      ..strokeWidth = 1.5
      ..style = PaintingStyle.stroke;

    for (int i = 0; i < cracks.length; i++) {
      if (i / cracks.length <= progress) {
        final start = cracks[i];
        final end = i < cracks.length - 1 
            ? cracks[i + 1] 
            : Offset(start.dx + 10, start.dy + 10);
        
        canvas.drawLine(start, end, paint);
      }
    }
  }

  @override
  bool shouldRepaint(CrackPainter oldDelegate) {
    return oldDelegate.progress != progress || oldDelegate.cracks != cracks;
  }
}

/// Custom painter for background effects
class _BackgroundPainter extends CustomPainter {
  final BreakfinityLayerTheme theme;
  final double animationValue;

  _BackgroundPainter({
    required this.theme,
    required this.animationValue,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..shader = LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: [
          theme.primaryColor.withOpacity(0.1),
          theme.secondaryColor.withOpacity(0.1),
        ],
      ).createShader(Rect.fromLTWH(0, 0, size.width, size.height));

    canvas.drawRect(Rect.fromLTWH(0, 0, size.width, size.height), paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

/// Custom painter for particle effects
class _ParticlePainter extends CustomPainter {
  final List<Particle> particles;

  _ParticlePainter(this.particles);

  @override
  void paint(Canvas canvas, Size size) {
    for (final particle in particles) {
      final paint = Paint()
        ..color = particle.color.withOpacity(particle.alpha)
        ..style = PaintingStyle.fill;

      canvas.drawCircle(
        particle.position,
        particle.size,
        paint,
      );
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
