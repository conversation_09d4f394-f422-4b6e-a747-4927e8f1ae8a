import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

import '../constants.dart';
import '../services/challenges_service.dart';
import '../models/challenge.dart';
import '../widgets/token_balance.dart';

/// Screen for the comprehensive challenges system
class ChallengesScreen extends StatefulWidget {
  const ChallengesScreen({super.key});

  @override
  State<ChallengesScreen> createState() => _ChallengesScreenState();
}

class _ChallengesScreenState extends State<ChallengesScreen>
    with TickerProviderStateMixin {
  late AnimationController _glowController;
  late Animation<double> _glowAnimation;
  
  bool _isLoading = true;
  List<Challenge> _challenges = [];

  @override
  void initState() {
    super.initState();
    
    _glowController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );
    
    _glowAnimation = Tween<double>(
      begin: 0.5,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _glowController,
      curve: Curves.easeInOut,
    ));
    
    _glowController.repeat(reverse: true);
    _loadData();
  }

  @override
  void dispose() {
    _glowController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    final challenges = await ChallengesService.getActiveChallenges();

    setState(() {
      _challenges = challenges;
      _isLoading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: NeonColors.background,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        title: Text(
          'Challenges',
          style: GoogleFonts.orbitron(
            color: NeonColors.primaryAccent,
            fontWeight: FontWeight.bold,
            fontSize: 24,
          ),
        ),
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: NeonColors.primaryAccent,
          ),
          onPressed: () => Navigator.of(context).pop(),
        ),
        actions: [
          const TokenBalance(),
          const SizedBox(width: 16),
        ],
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: NeonColors.backgroundGradient,
        ),
        child: _isLoading
            ? const Center(child: CircularProgressIndicator())
            : SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Header section
                    _buildHeaderSection(),
                    const SizedBox(height: 24),
                    
                    // Active challenges section
                    _buildChallengesSection(),
                  ],
                ),
              ),
      ),
    );
  }

  Widget _buildHeaderSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: NeonColors.surface,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: NeonColors.primaryAccent.withValues(alpha: 0.3),
          width: 2,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.emoji_events,
                color: NeonColors.primaryAccent,
                size: 32,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Weekly Challenges',
                      style: GoogleFonts.orbitron(
                        color: NeonColors.primaryAccent,
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      'Complete challenges across all games to earn rewards',
                      style: GoogleFonts.orbitron(
                        color: NeonColors.textSecondary,
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: NeonColors.background.withValues(alpha: 0.5),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.info_outline,
                  color: NeonColors.primaryAccent,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'Challenges reset every Monday. Complete 5 active challenges to earn tokens!',
                    style: GoogleFonts.orbitron(
                      color: NeonColors.textSecondary,
                      fontSize: 12,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildChallengesSection() {
    if (_challenges.isEmpty) {
      return Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: NeonColors.surface,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: NeonColors.textSecondary.withValues(alpha: 0.3),
            width: 2,
          ),
        ),
        child: Column(
          children: [
            Icon(
              Icons.hourglass_empty,
              color: NeonColors.textSecondary,
              size: 48,
            ),
            const SizedBox(height: 16),
            Text(
              'No Active Challenges',
              style: GoogleFonts.orbitron(
                color: NeonColors.textSecondary,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            Text(
              'Check back later for new challenges!',
              style: GoogleFonts.orbitron(
                color: NeonColors.textSecondary,
                fontSize: 14,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Active Challenges (${_challenges.where((c) => !c.completed).length}/${_challenges.length})',
          style: GoogleFonts.orbitron(
            color: NeonColors.primaryAccent,
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),

        ..._challenges.map((challenge) => Padding(
          padding: const EdgeInsets.only(bottom: 16),
          child: _buildChallengeCard(challenge),
        )),
      ],
    );
  }

  Widget _buildChallengeCard(Challenge challenge) {
    final progress = challenge.progressPercentage;
    final isCompleted = challenge.completed;

    return AnimatedBuilder(
      animation: _glowAnimation,
      builder: (context, child) {
        return Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: NeonColors.surface,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: isCompleted
                  ? NeonColors.neonGreen.withValues(alpha: _glowAnimation.value)
                  : challenge.difficultyColor.withValues(alpha: 0.5),
              width: 2,
            ),
            boxShadow: isCompleted
                ? [
                    BoxShadow(
                      color: NeonColors.neonGreen.withValues(alpha: _glowAnimation.value * 0.3),
                      blurRadius: 15,
                      spreadRadius: 2,
                    ),
                  ]
                : null,
          ),
          child: Column(
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: challenge.difficultyColor.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      challenge.iconData,
                      color: isCompleted ? NeonColors.neonGreen : challenge.difficultyColor,
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Expanded(
                              child: Text(
                                challenge.title,
                                style: GoogleFonts.orbitron(
                                  color: isCompleted ? NeonColors.neonGreen : NeonColors.textPrimary,
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                            Container(
                              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                              decoration: BoxDecoration(
                                color: challenge.difficultyColor.withValues(alpha: 0.2),
                                borderRadius: BorderRadius.circular(4),
                              ),
                              child: Text(
                                challenge.difficulty.name.toUpperCase(),
                                style: GoogleFonts.orbitron(
                                  color: challenge.difficultyColor,
                                  fontSize: 10,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 4),
                        Text(
                          challenge.description,
                          style: GoogleFonts.orbitron(
                            color: NeonColors.textSecondary,
                            fontSize: 12,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Row(
                          children: [
                            Icon(
                              Icons.games,
                              color: NeonColors.textSecondary,
                              size: 12,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              challenge.applicableGames.join(', ').toUpperCase(),
                              style: GoogleFonts.orbitron(
                                color: NeonColors.textSecondary,
                                fontSize: 10,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: NeonColors.highlights.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.monetization_on,
                          color: NeonColors.highlights,
                          size: 12,
                        ),
                        const SizedBox(width: 2),
                        Text(
                          '${challenge.reward}',
                          style: GoogleFonts.orbitron(
                            color: NeonColors.highlights,
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),

              // Progress bar
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        '${challenge.progress}/${challenge.target}',
                        style: GoogleFonts.orbitron(
                          color: NeonColors.textSecondary,
                          fontSize: 12,
                        ),
                      ),
                      Row(
                        children: [
                          if (isCompleted)
                            Icon(
                              Icons.check_circle,
                              color: NeonColors.neonGreen,
                              size: 16,
                            ),
                          const SizedBox(width: 4),
                          Text(
                            '${(progress * 100).toInt()}%',
                            style: GoogleFonts.orbitron(
                              color: isCompleted ? NeonColors.neonGreen : NeonColors.textSecondary,
                              fontSize: 12,
                              fontWeight: isCompleted ? FontWeight.bold : FontWeight.normal,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  LinearProgressIndicator(
                    value: progress.clamp(0.0, 1.0),
                    backgroundColor: NeonColors.background,
                    valueColor: AlwaysStoppedAnimation<Color>(
                      isCompleted ? NeonColors.neonGreen : challenge.difficultyColor,
                    ),
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }
}
