import 'dart:async';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:google_fonts/google_fonts.dart';

import '../constants.dart';
import '../core/constants.dart' as core_constants;
import '../core/sound_manager.dart';
import '../models/level_config.dart';
import '../models/target.dart';
import '../models/token_transaction.dart';
import '../models/power_up.dart';
import '../providers/game_provider.dart';
import '../services/level_service.dart';
import '../game/power_up_manager.dart';
import '../game/target_system.dart';
import '../game/boss_fight_system.dart';
import '../game/input_system.dart';
import '../game/mechanics_engine.dart';
import '../game/level_progression.dart';
import '../widgets/target_widget.dart';
import '../widgets/boss_widget.dart';
import '../widgets/input_feedback.dart';
import '../widgets/level_completion_popup.dart';

/// Main game screen that handles all game modes and mechanics
class GameScreen extends StatefulWidget {
  final int levelNumber;
  final core_constants.GameMode gameMode;

  const GameScreen({
    super.key,
    required this.levelNumber,
    required this.gameMode,
  });

  @override
  State<GameScreen> createState() => _GameScreenState();
}

class _GameScreenState extends State<GameScreen>
    with TickerProviderStateMixin {
  // Core game state
  late LevelConfig _levelConfig;
  late Timer _gameTimer;
  Timer? _spawnTimer;
  
  // Animation controllers
  late AnimationController _backgroundController;
  late AnimationController _uiController;
  late Animation<double> _backgroundAnimation;
  late Animation<double> _scoreAnimation;
  
  // Game variables
  int _score = 0;
  int _timeLeft = 0;
  bool _gameActive = false;
  bool _gameStarted = false;
  bool _gameCompleted = false;
  
  // Game systems
  late TargetSystem _targetSystem;
  late InputSystem _inputSystem;
  late MechanicsEngine _mechanicsEngine;
  late LevelProgression _levelProgression;
  late BossFightSystem _bossFightSystem;
  final List<Widget> _particleEffects = [];

  // Boss fight state
  bool _isBossLevel = false;
  bool _bossFightActive = false;

  // Power-ups and effects
  late PowerUpManager _powerUpManager;
  bool _slowMotionActive = false;
  bool _shieldActive = false;

  // Mechanic feedback
  String _mechanicFeedback = '';
  Timer? _feedbackTimer;
  
  @override
  void initState() {
    super.initState();
    _initializeGame();
    // Start playing game music
    SoundManager().playRandomGameMusic();
  }

  void _initializeGame() {
    // Load level configuration
    _levelConfig = LevelService.getLevelConfig(widget.levelNumber);
    _timeLeft = _levelConfig.duration.inSeconds;
    
    // Initialize animation controllers
    _backgroundController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    );
    
    _uiController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    
    _backgroundAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _backgroundController,
      curve: Curves.easeInOut,
    ));
    
    _scoreAnimation = Tween<double>(
      begin: 1.0,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _uiController,
      curve: Curves.elasticOut,
    ));
    
    // Check if this is a boss level
    _isBossLevel = core_constants.GameCalculations.isBossLevel(widget.levelNumber);

    // Initialize game systems
    _powerUpManager = PowerUpManager();
    _targetSystem = TargetSystem();
    _inputSystem = InputSystem();
    _mechanicsEngine = MechanicsEngine(
      targetSystem: _targetSystem,
      inputSystem: _inputSystem,
    );
    _levelProgression = LevelProgression(gameProvider: context.read<GameProvider>());

    // Initialize boss fight system if needed
    if (_isBossLevel) {
      _bossFightSystem = BossFightSystem();
      _setupBossFightCallbacks();
    }

    // Set up callbacks
    _setupMechanicsCallbacks();
    _setupLevelProgressionCallbacks();

    // Start background animation
    _backgroundController.repeat(reverse: true);

    // Don't auto-start game - wait for user tap
  }

  void _startGame() {
    if (!mounted) return;

    setState(() {
      _gameActive = true;
      _gameStarted = true;
      _score = 0;
      _timeLeft = _levelConfig.duration.inSeconds;
    });

    // Initialize target system (only if not a boss level)
    if (!_isBossLevel) {
      _targetSystem.initialize(
        screenSize: MediaQuery.of(context).size,
        level: widget.levelNumber,
        gameMode: widget.gameMode,
      );
    }

    // Initialize mechanics engine for current level
    final gameProvider = context.read<GameProvider>();
    _mechanicsEngine.initializeLevel(gameProvider.currentLevel);

    // Initialize level progression
    _levelProgression.initializeLevel(_levelConfig);

    // Start boss fight if this is a boss level
    if (_isBossLevel) {
      _startBossFight();
    }

    _startGameTimer();
    _startGameMechanics();
  }

  void _startGameTimer() {
    _gameTimer = Timer.periodic(const Duration(milliseconds: 16), (timer) {
      if (!mounted || !_gameActive) return;

      if (_bossFightActive) {
        // Boss fight system manages its own updates
        // Just check for boss fight completion
        if (!_bossFightSystem.isActive) {
          _endGame();
        }
      } else {
        // Track targets before update for miss detection
        final targetsBeforeUpdate = List.from(_targetSystem.targets);

        // Apply slow motion factor to deltaTime
        final slowMotionFactor = _powerUpManager.getSlowMotionFactor();
        final deltaTime = 0.016 * slowMotionFactor; // 60 FPS with slow motion

        // Update target system
        _targetSystem.update(deltaTime);

        // Check for missed targets (targets that were active but are now removed)
        final targetsAfterUpdate = _targetSystem.targets;
        for (final target in targetsBeforeUpdate) {
          if (target.isActive && !target.isHit && !targetsAfterUpdate.contains(target)) {
            // Target expired without being hit - record as miss
            _levelProgression.recordTargetMiss();
          }
        }

        // Update mechanics engine
        _mechanicsEngine.update(deltaTime);

        // Check level progression
        _checkLevelCompletion();
      }

      setState(() {
        // Update time every second
        final now = DateTime.now();
        if (now.difference(_lastSecondUpdate).inMilliseconds >= 1000) {
          _timeLeft--;
          _lastSecondUpdate = now;
          if (_timeLeft <= 0) {
            _endGame();
          }
        }
      });
    });
  }

  DateTime _lastSecondUpdate = DateTime.now();

  void _setupMechanicsCallbacks() {
    // Set up callbacks for the mechanics engine
    _mechanicsEngine.onScoreUpdate = (points) {
      setState(() {
        _score += points;
      });
      // Update level progression
      _levelProgression.updateScore(points);
    };

    _mechanicsEngine.onMechanicFeedback = (message) {
      setState(() {
        _mechanicFeedback = message;
      });

      // Clear feedback after 2 seconds
      _feedbackTimer?.cancel();
      _feedbackTimer = Timer(const Duration(seconds: 2), () {
        setState(() {
          _mechanicFeedback = '';
        });
      });
    };

    _mechanicsEngine.onMechanicComplete = () {
      // Handle mechanic completion
      _checkLevelCompletion();
    };

    _mechanicsEngine.onMechanicFailed = () {
      // Handle mechanic failure
      _endGame();
    };
  }

  /// Setup level progression callbacks
  void _setupLevelProgressionCallbacks() {
    _levelProgression.onObjectiveCompleted = (objective) {
      // Show objective completion feedback
      setState(() {
        _mechanicFeedback = '${objective.name} Complete! +${objective.bonusPoints}';
      });
    };

    _levelProgression.onLevelCompleted = (finalScore, stats) {
      _handleLevelCompletion(finalScore, stats);
    };

    _levelProgression.onLevelFailed = (reason) {
      _handleLevelFailure(reason);
    };

    _levelProgression.onProgressUpdate = (progress) {
      // Update UI with progress if needed
      setState(() {});
    };
  }

  /// Setup boss fight callbacks
  void _setupBossFightCallbacks() {
    _bossFightSystem.onBossDamaged = (damage) {
      setState(() {
        _mechanicFeedback = 'Boss Hit! -$damage HP';
      });
      _showFeedback();
    };

    _bossFightSystem.onBossDefeated = (boss) {
      setState(() {
        _bossFightActive = false;
        _mechanicFeedback = '${boss.name} Defeated!';
      });
      _showFeedback();

      // End the level with victory
      Future.delayed(const Duration(seconds: 2), () {
        _endGame();
      });
    };

    _bossFightSystem.onFightMessage = (message) {
      setState(() {
        _mechanicFeedback = message;
      });
      _showFeedback();
    };

    _bossFightSystem.onScoreUpdate = (taps, timeRemaining) {
      final bonus = core_constants.GameCalculations.calculateBossScoreBonus(taps, timeRemaining);
      setState(() {
        _score += bonus;
      });
    };

    _bossFightSystem.onPlayerHit = () {
      // Handle player taking damage from boss
      setState(() {
        _mechanicFeedback = 'Boss Attack Hit!';
      });
      _showFeedback();
    };
  }

  /// Show feedback message with timer
  void _showFeedback() {
    // Clear feedback after 2 seconds
    _feedbackTimer?.cancel();
    _feedbackTimer = Timer(const Duration(seconds: 2), () {
      setState(() {
        _mechanicFeedback = '';
      });
    });
  }

  /// Show power-up selection menu
  void _showPowerUpMenu() {
    if (!_gameActive) return; // Don't show during countdown or game over

    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (context) => _buildPowerUpDialog(),
    );
  }

  /// Build power-up selection dialog
  Widget _buildPowerUpDialog() {
    // For now, show all available power-ups
    // In a full implementation, this would show only owned power-ups from player profile
    final availablePowerUps = [
      PowerUps.slowMotion,
      PowerUps.neonBomb,
      PowerUps.shield,
      PowerUps.magnetTouch,
      PowerUps.timeBoost,
    ];

    return Dialog(
      backgroundColor: Colors.transparent,
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: NeonColors.surface,
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: NeonColors.primaryAccent.withValues(alpha: 0.5),
            width: 2,
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Title
            Text(
              'Power-Ups',
              style: TextStyle(
                color: NeonColors.primaryAccent,
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),

            // Power-up grid
            GridView.builder(
              shrinkWrap: true,
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                childAspectRatio: 1.2,
                crossAxisSpacing: 12,
                mainAxisSpacing: 12,
              ),
              itemCount: availablePowerUps.length,
              itemBuilder: (context, index) {
                final powerUp = availablePowerUps[index];
                final isActive = _powerUpManager.isPowerUpActive(powerUp.id);

                return GestureDetector(
                  onTap: () => _usePowerUp(powerUp),
                  child: Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: isActive
                          ? NeonColors.primaryAccent.withValues(alpha: 0.2)
                          : NeonColors.background.withValues(alpha: 0.8),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: isActive
                            ? NeonColors.primaryAccent
                            : NeonColors.secondaryAccent.withValues(alpha: 0.3),
                        width: 1,
                      ),
                    ),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          powerUp.icon,
                          color: powerUp.color,
                          size: 32,
                        ),
                        const SizedBox(height: 8),
                        Text(
                          powerUp.name,
                          style: TextStyle(
                            color: NeonColors.textPrimary,
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                          ),
                          textAlign: TextAlign.center,
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                        if (isActive) ...[
                          const SizedBox(height: 4),
                          Text(
                            'ACTIVE',
                            style: TextStyle(
                              color: NeonColors.primaryAccent,
                              fontSize: 10,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
                );
              },
            ),

            const SizedBox(height: 16),

            // Close button
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(
                'Close',
                style: TextStyle(
                  color: NeonColors.secondaryAccent,
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Use a power-up
  void _usePowerUp(PowerUp powerUp) {
    // Close the dialog first
    Navigator.of(context).pop();

    // Check if power-up is already active
    if (_powerUpManager.isPowerUpActive(powerUp.id)) {
      setState(() {
        _mechanicFeedback = '${powerUp.name} is already active!';
      });
      _showFeedback();
      return;
    }

    // Activate the power-up
    _powerUpManager.activatePowerUp(powerUp);

    // Handle specific power-up effects
    _handlePowerUpActivation(powerUp);

    // Show feedback
    setState(() {
      _mechanicFeedback = '${powerUp.name} activated!';
    });
    _showFeedback();
  }

  /// Handle power-up activation effects
  void _handlePowerUpActivation(PowerUp powerUp) {
    switch (powerUp.type) {
      case core_constants.PowerUpType.slowMotion:
        // Slow motion effect - this will be handled by the game systems
        // that check _powerUpManager.getSlowMotionFactor()
        break;

      case core_constants.PowerUpType.neonBomb:
        // Clear all targets on screen - get count before clearing
        final clearedTargets = _targetSystem.targets.length;
        _targetSystem.clear();
        // Add score for cleared targets
        setState(() {
          _score += clearedTargets * 10; // 10 points per cleared target
        });
        break;

      case core_constants.PowerUpType.shield:
        // Shield effect - this will be handled by the game systems
        // that check _powerUpManager.isShieldActive()
        break;

      case core_constants.PowerUpType.magnetTouch:
        // Magnet touch effect - this will be handled by the input system
        // that checks _powerUpManager.getMagnetTouchMultiplier()
        break;

      case core_constants.PowerUpType.timeBoost:
        // Add time to the timer
        final timeToAdd = (powerUp.effects['timeAdd'] ?? 5.0) as double;
        setState(() {
          _timeLeft += timeToAdd.toInt();
        });
        break;

      default:
        // Handle any other power-up types
        break;
    }
  }

  void _startGameMechanics() {
    // The target system handles spawning automatically based on game mode
    // No need for separate spawn timers
  }

  /// Start boss fight for boss levels
  void _startBossFight() {
    if (!_isBossLevel) return;

    final screenSize = MediaQuery.of(context).size;
    final success = _bossFightSystem.startBossFight(widget.levelNumber, screenSize);

    if (success) {
      setState(() {
        _bossFightActive = true;
        _mechanicFeedback = 'Boss Fight Started!';
      });
      _showFeedback();
    }
  }



  void _checkLevelCompletion() {
    // Check if score goal is reached
    if (_score >= _levelConfig.goal) {
      _completeLevel();
      return;
    }

    // Check level progression completion
    if (_levelProgression.checkLevelCompletion()) {
      return; // Level completion handled by callback
    }

    // Check for level failure
    if (_levelProgression.checkLevelFailure()) {
      return; // Level failure handled by callback
    }
  }

  /// Handle level completion
  void _handleLevelCompletion(int finalScore, Map<String, dynamic> stats) {
    setState(() {
      _gameActive = false;
      _gameCompleted = true;
    });

    // Calculate tokens earned (20 base + bonus objectives)
    final tokensEarned = 20 + (stats['bonusScore'] as int? ?? 0) ~/ 50;

    // Award tokens to player
    final gameProvider = context.read<GameProvider>();
    gameProvider.earnTokens(
      tokensEarned,
      TokenTransactionType.levelCompletion,
      'Level ${widget.levelNumber} completed',
    );

    // Show completion popup
    _showLevelCompletionPopup(finalScore, tokensEarned, stats);
  }

  /// Handle level failure
  void _handleLevelFailure(String reason) {
    setState(() {
      _gameActive = false;
    });

    // Show failure dialog
    _showLevelFailureDialog(reason);
  }

  /// Show level completion popup
  void _showLevelCompletionPopup(int finalScore, int tokensEarned, Map<String, dynamic> stats) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => LevelCompletionPopup(
        level: widget.levelNumber,
        finalScore: finalScore,
        tokensEarned: tokensEarned,
        xpEarned: _levelConfig.xpReward,
        stats: stats,
        completedObjectives: _levelProgression.objectives
            .where((obj) => _levelProgression.completed[obj.id] == true)
            .toList(),
        achievementsUnlocked: [], // TODO: Implement achievement system
        onReplay: () {
          Navigator.of(context).pop();
          _restartLevel();
        },
        onNextLevel: () {
          Navigator.of(context).pop();
          _goToNextLevel();
        },
        onHome: () {
          Navigator.of(context).pop();
          Navigator.of(context).pop();
        },
      ),
    );
  }

  /// Show level failure dialog
  void _showLevelFailureDialog(String reason) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: NeonColors.surface,
        title: Text(
          'Level Failed',
          style: GoogleFonts.orbitron(
            color: NeonColors.error,
            fontWeight: FontWeight.bold,
          ),
        ),
        content: Text(
          reason,
          style: GoogleFonts.orbitron(
            color: NeonColors.textPrimary,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _restartLevel();
            },
            child: Text(
              'RETRY',
              style: GoogleFonts.orbitron(
                color: NeonColors.primaryAccent,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              Navigator.of(context).pop();
            },
            child: Text(
              'EXIT',
              style: GoogleFonts.orbitron(
                color: NeonColors.textSecondary,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Restart the current level
  void _restartLevel() {
    setState(() {
      _gameActive = false;
      _gameStarted = false;
      _gameCompleted = false;
      _score = 0;
      _timeLeft = _levelConfig.duration.inSeconds;
      _mechanicFeedback = '';
    });

    // Reset game systems
    _targetSystem.clear();
    _levelProgression.initializeLevel(_levelConfig);

    // Restart the game
    Future.delayed(const Duration(milliseconds: 500), () {
      if (mounted) {
        _startGame();
      }
    });
  }

  /// Go to the next level
  void _goToNextLevel() {
    final nextLevel = widget.levelNumber + 1;

    // Navigate to next level (level progression is handled by completeLevel)
    Navigator.of(context).pushReplacement(
      MaterialPageRoute(
        builder: (context) => GameScreen(
          levelNumber: nextLevel,
          gameMode: widget.gameMode,
        ),
      ),
    );
  }

  void _onTargetTapped(Offset position) {
    if (!_gameActive) return;

    final target = _targetSystem.getTargetAt(position);
    if (target != null) {
      _hitTarget(target);
    }
  }

  void _onBossTapped() {
    if (!_gameActive || !_bossFightActive) return;

    // Handle boss tap through the boss fight system
    _bossFightSystem.handleTap(Offset.zero); // Boss widget handles position internally
  }

  void _handleBackgroundTap(TapDownDetails details) {
    if (!_gameActive) return;

    final position = details.localPosition;

    // Check if tap hit any target
    bool hitTarget = false;

    if (_bossFightActive) {
      // Boss fight mode - let boss widget handle taps
      return;
    } else {
      // Check if tap hit any regular target
      final target = _targetSystem.getTargetAt(position);
      if (target != null) {
        _hitTarget(target);
        hitTarget = true;
      }
    }

    // If no target was hit, apply background tap penalty
    if (!hitTarget) {
      _applyBackgroundTapPenalty();
    }
  }

  void _applyBackgroundTapPenalty() {
    const penalty = 5; // Points deducted for background tap
    setState(() {
      _score = (_score - penalty).clamp(0, double.infinity).toInt();
    });

    // Show penalty feedback
    _showPenaltyFeedback('Background Tap: -$penalty points');
  }

  void _applyEnemyPenalty(GameTarget target) {
    if (target.type == TargetType.redEnemy) {
      // Red enemy: score penalty
      setState(() {
        _score = (_score + target.penalty).clamp(0, double.infinity).toInt();
      });
      _showPenaltyFeedback('Red Enemy Hit: ${target.penalty} points');
    } else if (target.type == TargetType.yellowEnemy) {
      // Yellow enemy: timer penalty
      setState(() {
        _timeLeft = (_timeLeft + target.penalty).clamp(0, double.infinity).toInt();
      });
      _showPenaltyFeedback('Yellow Enemy Hit: ${target.penalty} seconds');
    }
  }

  void _showPenaltyFeedback(String message) {
    setState(() {
      _mechanicFeedback = message;
    });

    // Clear feedback after 2 seconds
    Timer(const Duration(seconds: 2), () {
      if (mounted) {
        setState(() {
          _mechanicFeedback = '';
        });
      }
    });
  }

  void _hitTarget(GameTarget target) {
    // Handle enemy targets with penalties
    if (target.type == TargetType.redEnemy || target.type == TargetType.yellowEnemy) {
      _applyEnemyPenalty(target);
    } else {
      // Regular targets give score
      setState(() {
        _score += target.getScore().toInt();
      });
    }

    // Mark target as hit in the system
    _targetSystem.hitTarget(target.position);

    // Record hit in level progression
    _levelProgression.recordTargetHit(
      isPerfect: target.type == TargetType.hardBall,
      targetType: target.type.toString(),
    );

    // Animate score increase
    _uiController.forward().then((_) {
      _uiController.reverse();
    });

    // Add particle effect
    _addParticleEffect(target.position, target.color);

    // Level completion is now handled by level progression system
  }

  void _addParticleEffect(Offset position, Color color) {
    // Add particle effect at target position
    // This will be implemented in the particle system
  }

  void _completeLevel() {
    setState(() {
      _gameActive = false;
      _gameCompleted = true;
    });
    
    _gameTimer.cancel();
    _spawnTimer?.cancel();
    
    // Award tokens and XP
    final gameProvider = context.read<GameProvider>();
    final completionTime = Duration(seconds: _levelConfig.duration.inSeconds - _timeLeft);
    gameProvider.completeLevel(
      finalScore: _score,
      completionTime: completionTime,
    );
    
    // Show completion dialog
    _showLevelCompletionDialog();
  }

  void _endGame() {
    setState(() {
      _gameActive = false;
    });
    
    _gameTimer.cancel();
    _spawnTimer?.cancel();
    
    if (_score >= _levelConfig.goal) {
      _completeLevel();
    } else {
      _showGameOverDialog();
    }
  }

  void _showLevelCompletionDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => LevelCompletionPopup(
        level: widget.levelNumber,
        finalScore: _score,
        tokensEarned: _levelConfig.tokenReward,
        xpEarned: _levelConfig.xpReward,
        stats: {
          'timeLeft': _timeLeft,
          'targetsHit': _score,
          'accuracy': _score > 0 ? (_score / (_score + 1)) * 100 : 0,
        },
        completedObjectives: [], // TODO: Add objectives when implemented
        achievementsUnlocked: [], // TODO: Implement achievement system
        onNextLevel: () {
          Navigator.of(context).pop();
          _goToNextLevel();
        },
        onReplay: () {
          Navigator.of(context).pop();
          _initializeGame();
          _startGame();
        },
        onHome: () {
          Navigator.of(context).pop();
          Navigator.of(context).pop(); // Return to previous screen
        },
      ),
    );
  }

  void _showGameOverDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        backgroundColor: NeonColors.surface,
        title: Text(
          'Game Over',
          style: TextStyle(
            color: NeonColors.error,
            fontSize: 24,
            fontWeight: FontWeight.bold,
          ),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Score: $_score',
              style: TextStyle(color: NeonColors.textPrimary, fontSize: 18),
            ),
            const SizedBox(height: 8),
            Text(
              'Goal: ${_levelConfig.goal}',
              style: TextStyle(color: NeonColors.textSecondary, fontSize: 16),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _restartLevel();
            },
            child: Text(
              'Retry',
              style: TextStyle(color: NeonColors.warning),
            ),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              Navigator.of(context).pop(); // Return to previous screen
            },
            child: Text(
              'Exit',
              style: TextStyle(color: NeonColors.error),
            ),
          ),
        ],
      ),
    );
  }



  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: NeonColors.background,
      body: Stack(
        children: [
          // Animated background
          AnimatedBuilder(
            animation: _backgroundAnimation,
            builder: (context, child) {
              return Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      NeonColors.primaryAccent.withValues(alpha: 0.1),
                      NeonColors.secondaryAccent.withValues(alpha: 0.1),
                      NeonColors.background,
                    ],
                    stops: [0.0, 0.5 + (_backgroundAnimation.value * 0.3), 1.0],
                  ),
                ),
              );
            },
          ),

          // Game area
          SafeArea(
            child: GestureDetector(
              onTapDown: _handleBackgroundTap,
              child: SizedBox(
                width: double.infinity,
                height: double.infinity,
                child: Stack(
                  children: [
                    // Game objects
                    ..._buildGameObjects(),

                    // Particle effects
                    ..._particleEffects,

                    // Input feedback
                    InputFeedback(
                      inputSystem: _inputSystem,
                      screenSize: MediaQuery.of(context).size,
                    ),

                    // UI overlay
                    _buildUIOverlay(),
                  ],
                ),
              ),
            ),
          ),

          // Game start countdown
          if (!_gameStarted)
            _buildCountdownOverlay(),
        ],
      ),
    );
  }

  List<Widget> _buildGameObjects() {
    List<Widget> objects = [];

    // Add boss widget if in boss fight mode
    if (_bossFightActive) {
      objects.add(
        BossWidget(
          boss: _bossFightSystem.currentBoss!,
          onTap: _onBossTapped,
        ),
      );
    } else {
      // Add all active targets including fragments from target system
      for (final target in _targetSystem.getAllActiveTargets()) {
        objects.add(
          TargetWidget(
            target: target,
            onTap: () => _onTargetTapped(target.position),
          ),
        );
      }
    }

    return objects;
  }

  Widget _buildUIOverlay() {
    return LayoutBuilder(
      builder: (context, constraints) {
        // Responsive font sizes and padding
        final isSmallScreen = constraints.maxWidth < 400;
        final fontSize = isSmallScreen ? 14.0 : 18.0;
        final padding = isSmallScreen ? 12.0 : 16.0;
        final horizontalPadding = isSmallScreen ? 8.0 : 16.0;
        final verticalPadding = isSmallScreen ? 4.0 : 8.0;

        return Stack(
          children: [
            // Top UI bar
            Positioned(
              top: 0,
              left: 0,
              right: 0,
              child: Container(
                padding: EdgeInsets.all(padding),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    // Score display
                    Flexible(
                      child: AnimatedBuilder(
                        animation: _scoreAnimation,
                        builder: (context, child) {
                          return Transform.scale(
                            scale: _scoreAnimation.value,
                            child: Container(
                              padding: EdgeInsets.symmetric(
                                horizontal: horizontalPadding,
                                vertical: verticalPadding,
                              ),
                              decoration: BoxDecoration(
                                color: NeonColors.surface.withValues(alpha: 0.8),
                                borderRadius: BorderRadius.circular(20),
                                border: Border.all(
                                  color: NeonColors.primaryAccent.withValues(alpha: 0.5),
                                  width: 1,
                                ),
                              ),
                              child: Text(
                                'Score: $_score',
                                style: TextStyle(
                                  color: NeonColors.primaryAccent,
                                  fontSize: fontSize,
                                  fontWeight: FontWeight.bold,
                                ),
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          );
                        },
                      ),
                    ),

                    SizedBox(width: padding),

                    // Timer display
                    Flexible(
                      child: Container(
                        padding: EdgeInsets.symmetric(
                          horizontal: horizontalPadding,
                          vertical: verticalPadding,
                        ),
                        decoration: BoxDecoration(
                          color: NeonColors.surface.withValues(alpha: 0.8),
                          borderRadius: BorderRadius.circular(20),
                          border: Border.all(
                            color: _timeLeft <= 10 ? NeonColors.error : NeonColors.secondaryAccent,
                            width: 1,
                          ),
                        ),
                        child: Text(
                          'Time: $_timeLeft',
                          style: TextStyle(
                            color: _timeLeft <= 10 ? NeonColors.error : NeonColors.secondaryAccent,
                            fontSize: fontSize,
                            fontWeight: FontWeight.bold,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ),

                    SizedBox(width: padding),

                    // Power-up button
                    GestureDetector(
                      onTap: _showPowerUpMenu,
                      child: Container(
                        padding: EdgeInsets.all(verticalPadding + 2),
                        decoration: BoxDecoration(
                          color: _powerUpManager.getSlowMotionFactor() < 1.0
                              ? NeonColors.primaryAccent.withValues(alpha: 0.3)
                              : NeonColors.surface.withValues(alpha: 0.8),
                          borderRadius: BorderRadius.circular(20),
                          border: Border.all(
                            color: _powerUpManager.getSlowMotionFactor() < 1.0
                                ? NeonColors.primaryAccent
                                : NeonColors.primaryAccent.withValues(alpha: 0.5),
                            width: _powerUpManager.getSlowMotionFactor() < 1.0 ? 2 : 1,
                          ),
                        ),
                        child: Icon(
                          Icons.flash_on,
                          color: _powerUpManager.getSlowMotionFactor() < 1.0
                              ? NeonColors.primaryAccent
                              : NeonColors.primaryAccent,
                          size: fontSize + 2,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),

        // Mechanic info and feedback
        Positioned(
          top: 80,
          left: 16,
          right: 16,
          child: Column(
            children: [
              // Current mechanic display
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: NeonColors.surface.withValues(alpha: 0.9),
                  borderRadius: BorderRadius.circular(15),
                  border: Border.all(
                    color: NeonColors.secondaryAccent.withValues(alpha: 0.3),
                    width: 1,
                  ),
                ),
                child: Text(
                  _mechanicsEngine.getMechanicName(),
                  style: TextStyle(
                    color: NeonColors.secondaryAccent,
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),

              // Mechanic feedback
              if (_mechanicFeedback.isNotEmpty) ...[
                const SizedBox(height: 8),
                AnimatedOpacity(
                  opacity: _mechanicFeedback.isNotEmpty ? 1.0 : 0.0,
                  duration: const Duration(milliseconds: 300),
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    decoration: BoxDecoration(
                      color: NeonColors.primaryAccent.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(20),
                      border: Border.all(
                        color: NeonColors.primaryAccent.withValues(alpha: 0.5),
                        width: 1,
                      ),
                    ),
                    child: Text(
                      _mechanicFeedback,
                      style: TextStyle(
                        color: NeonColors.primaryAccent,
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ),
              ],
            ],
          ),
        ),
      ],
    );
      },
    );
  }

  Widget _buildCountdownOverlay() {
    return GestureDetector(
      onTap: _startGame,
      child: Container(
        color: NeonColors.background.withValues(alpha: 0.9),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Level title
              Text(
                _levelConfig.title,
                style: TextStyle(
                  color: NeonColors.primaryAccent,
                  fontSize: 32,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),

              // Level description
              Text(
                _levelConfig.description,
                style: TextStyle(
                  color: NeonColors.textPrimary,
                  fontSize: 18,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 32),

              // Required score
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
                decoration: BoxDecoration(
                  color: NeonColors.surface.withValues(alpha: 0.8),
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(
                    color: NeonColors.secondaryAccent.withValues(alpha: 0.5),
                    width: 1,
                  ),
                ),
                child: Column(
                  children: [
                    Text(
                      'Required Score',
                      style: TextStyle(
                        color: NeonColors.textSecondary,
                        fontSize: 16,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      '${_levelConfig.goal}',
                      style: TextStyle(
                        color: NeonColors.secondaryAccent,
                        fontSize: 28,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 24),

              // Time limit
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                decoration: BoxDecoration(
                  color: NeonColors.surface.withValues(alpha: 0.6),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: NeonColors.warning.withValues(alpha: 0.5),
                    width: 1,
                  ),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.timer,
                      color: NeonColors.warning,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'Time: ${_levelConfig.duration.inSeconds}s',
                      style: TextStyle(
                        color: NeonColors.warning,
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 40),

              // Tap to start instruction
              Text(
                'Press anywhere to begin',
                style: TextStyle(
                  color: NeonColors.primaryAccent,
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              )
                  .animate(onPlay: (controller) => controller.repeat())
                  .fadeIn(duration: const Duration(milliseconds: 800))
                  .then()
                  .fadeOut(duration: const Duration(milliseconds: 800)),
            ],
          ),
        ),
      ),
    );
  }



  @override
  void dispose() {
    _gameTimer.cancel();
    _feedbackTimer?.cancel();
    _backgroundController.dispose();
    _uiController.dispose();
    _targetSystem.dispose();
    _inputSystem.dispose();
    _mechanicsEngine.dispose();
    super.dispose();
  }
}
