import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';
import '../constants.dart';
import '../providers/game_provider.dart';
import '../core/constants.dart' as core_constants;
import 'game_screen.dart';

/// Enhanced levels screen showing all 100 levels with proper locking system
class LevelsScreen extends StatefulWidget {
  const LevelsScreen({super.key});

  @override
  State<LevelsScreen> createState() => _LevelsScreenState();
}

class _LevelsScreenState extends State<LevelsScreen> {
  final ScrollController _scrollController = ScrollController();

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: NeonColors.background,
      body: SafeArea(
        child: Column(
          children: [
            _buildHeader(),
            Expanded(
              child: _buildLevelsGrid(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: NeonColors.surface,
        border: Border(
          bottom: BorderSide(
            color: NeonColors.primaryAccent.withValues(alpha: 0.3),
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          // Back button
          IconButton(
            onPressed: () => Navigator.of(context).pop(),
            icon: Icon(
              Icons.arrow_back,
              color: NeonColors.primaryAccent,
            ),
          ),

          const SizedBox(width: 16),

          // Title
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'NEONRUSH',
                  style: GoogleFonts.orbitron(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: NeonColors.primaryAccent,
                    letterSpacing: 2,
                  ),
                ),
                Text(
                  'Select Level (1-100)',
                  style: GoogleFonts.orbitron(
                    fontSize: 14,
                    color: NeonColors.textSecondary,
                    letterSpacing: 1,
                  ),
                ),
              ],
            ),
          ),

          // Current level indicator
          Consumer<GameProvider>(
            builder: (context, gameProvider, child) {
              return Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: NeonColors.primaryAccent.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: NeonColors.primaryAccent,
                    width: 1,
                  ),
                ),
                child: Text(
                  'Current: ${gameProvider.currentLevel}',
                  style: GoogleFonts.orbitron(
                    fontSize: 12,
                    color: NeonColors.primaryAccent,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildLevelsGrid() {
    return Consumer<GameProvider>(
      builder: (context, gameProvider, child) {
        return LayoutBuilder(
          builder: (context, constraints) {
            // Calculate responsive grid columns based on screen width
            int crossAxisCount;
            if (constraints.maxWidth < 400) {
              crossAxisCount = 4; // Small phones
            } else if (constraints.maxWidth < 600) {
              crossAxisCount = 5; // Normal phones
            } else if (constraints.maxWidth < 900) {
              crossAxisCount = 6; // Large phones/small tablets
            } else {
              crossAxisCount = 8; // Tablets/desktop
            }

            return Padding(
              padding: EdgeInsets.symmetric(
                horizontal: constraints.maxWidth * 0.05, // 5% padding
                vertical: 20,
              ),
              child: GridView.builder(
                controller: _scrollController,
                gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: crossAxisCount,
                  childAspectRatio: 1.0,
                  crossAxisSpacing: constraints.maxWidth * 0.02, // 2% spacing
                  mainAxisSpacing: constraints.maxWidth * 0.02,
                ),
                itemCount: core_constants.GameConstants.maxLevels, // 100 levels
                itemBuilder: (context, index) {
                  final levelNumber = index + 1;
                  return _buildLevelCard(levelNumber, gameProvider);
                },
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildLevelCard(int levelNumber, GameProvider gameProvider) {
    final isUnlocked = gameProvider.isLevelUnlocked(levelNumber);
    final isBossLevel = core_constants.GameConstants.bossLevels.contains(levelNumber);
    final isCurrentLevel = levelNumber == gameProvider.currentLevel;

    return GestureDetector(
      onTap: isUnlocked ? () => _startLevel(levelNumber) : null,
      child: Container(
        decoration: BoxDecoration(
          color: isUnlocked
              ? (isBossLevel
                  ? NeonColors.highlights.withValues(alpha: 0.2)
                  : NeonColors.primaryAccent.withValues(alpha: 0.1))
              : NeonColors.surface.withValues(alpha: 0.3),
          border: Border.all(
            color: isUnlocked
                ? (isBossLevel
                    ? NeonColors.highlights
                    : (isCurrentLevel
                        ? NeonColors.success
                        : NeonColors.primaryAccent))
                : NeonColors.textSecondary.withValues(alpha: 0.3),
            width: isCurrentLevel ? 3 : 2,
          ),
          borderRadius: BorderRadius.circular(12),
          boxShadow: isUnlocked ? [
            BoxShadow(
              color: (isBossLevel
                  ? NeonColors.highlights
                  : NeonColors.primaryAccent).withValues(alpha: 0.3),
              blurRadius: 8,
              spreadRadius: 1,
            ),
          ] : null,
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            if (isUnlocked) ...[
              // Boss level crown icon
              if (isBossLevel) ...[
                Icon(
                  Icons.emoji_events,
                  color: NeonColors.highlights,
                  size: 16,
                ),
                const SizedBox(height: 2),
              ],
              // Level number
              Text(
                '$levelNumber',
                style: GoogleFonts.orbitron(
                  fontSize: isBossLevel ? 18 : 16,
                  fontWeight: FontWeight.bold,
                  color: isBossLevel
                      ? NeonColors.highlights
                      : (isCurrentLevel
                          ? NeonColors.success
                          : NeonColors.primaryAccent),
                ),
              ),
              // Boss level label
              if (isBossLevel) ...[
                const SizedBox(height: 2),
                Text(
                  'BOSS',
                  style: GoogleFonts.orbitron(
                    fontSize: 8,
                    fontWeight: FontWeight.bold,
                    color: NeonColors.highlights,
                    letterSpacing: 1,
                  ),
                ),
              ],
            ] else ...[
              Icon(
                Icons.lock,
                color: NeonColors.textSecondary.withValues(alpha: 0.5),
                size: 20,
              ),
              const SizedBox(height: 4),
              Text(
                'LOCKED',
                style: GoogleFonts.orbitron(
                  fontSize: 8,
                  color: NeonColors.textSecondary.withValues(alpha: 0.5),
                  letterSpacing: 1,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  void _startLevel(int levelNumber) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => GameScreen(
          levelNumber: levelNumber,
          gameMode: core_constants.GameMode.tap,
        ),
      ),
    );
  }
}
