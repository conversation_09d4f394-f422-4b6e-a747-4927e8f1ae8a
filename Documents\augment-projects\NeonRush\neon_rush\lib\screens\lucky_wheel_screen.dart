import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:provider/provider.dart';

import '../constants.dart';
import '../core/constants.dart' as core_constants;
import '../services/spin_wheel_service.dart';
import '../providers/game_provider.dart';
import '../design/components/neon_button.dart';
import '../design/design_tokens.dart';

/// Lucky Wheel screen with new odds system and bonus game keys
class LuckyWheelScreen extends StatefulWidget {
  const LuckyWheelScreen({super.key});

  @override
  State<LuckyWheelScreen> createState() => _LuckyWheelScreenState();
}

class _LuckyWheelScreenState extends State<LuckyWheelScreen>
    with TickerProviderStateMixin {
  late AnimationController _rotationController;
  late AnimationController _glowController;
  late Animation<double> _rotationAnimation;
  late Animation<double> _glowAnimation;
  
  bool _isSpinning = false;
  double _finalRotation = 0;
  int _bonusGameKeys = 0;

  @override
  void initState() {
    super.initState();
    
    _rotationController = AnimationController(
      duration: const Duration(seconds: 4),
      vsync: this,
    );
    
    _glowController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );
    
    _rotationAnimation = Tween<double>(
      begin: 0,
      end: 1,
    ).animate(CurvedAnimation(
      parent: _rotationController,
      curve: Curves.easeOut,
    ));
    
    _glowAnimation = Tween<double>(
      begin: 0.5,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _glowController,
      curve: Curves.easeInOut,
    ));
    
    _glowController.repeat(reverse: true);
    _loadBonusGameKeys();
  }

  Future<void> _loadBonusGameKeys() async {
    final keys = await SpinWheelService.getBonusGameKeyCount();
    setState(() {
      _bonusGameKeys = keys;
    });
  }

  @override
  void dispose() {
    _rotationController.dispose();
    _glowController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: NeonColors.background,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        title: Text(
          'Lucky Wheel',
          style: GoogleFonts.orbitron(
            color: NeonColors.primaryAccent,
            fontSize: 24,
            fontWeight: FontWeight.bold,
          ),
        ),
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: NeonColors.primaryAccent),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: Consumer<GameProvider>(
        builder: (context, gameProvider, child) {
          final canUseFree = !gameProvider.dailySpinUsed;
          final canAffordPaid = gameProvider.tokens >= GameConstants.luckyWheelSpinCost;
          
          return SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                // Bonus Game Keys Display
                _buildBonusKeysDisplay(),
                
                const SizedBox(height: 32),
                
                // Lucky Wheel
                _buildLuckyWheel(),
                
                const SizedBox(height: 32),
                
                // Spin Buttons
                _buildSpinButtons(canUseFree, canAffordPaid, gameProvider),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildBonusKeysDisplay() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            NeonColors.highlights.withValues(alpha: 0.2),
            NeonColors.highlights.withValues(alpha: 0.1),
          ],
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: NeonColors.highlights, width: 2),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.vpn_key,
            color: NeonColors.highlights,
            size: 32,
          ),
          const SizedBox(width: 12),
          Text(
            'Bonus Game Keys: $_bonusGameKeys',
            style: GoogleFonts.orbitron(
              color: NeonColors.highlights,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLuckyWheel() {
    return Center(
      child: Stack(
        alignment: Alignment.center,
        children: [
          // Wheel
          AnimatedBuilder(
            animation: _rotationAnimation,
            builder: (context, child) {
              return Transform.rotate(
                angle: _rotationAnimation.value * _finalRotation,
                child: _buildWheel(),
              );
            },
          ),
          
          // Center pointer
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: NeonColors.primaryAccent,
              boxShadow: [
                BoxShadow(
                  color: NeonColors.primaryAccent.withValues(alpha: 0.5),
                  blurRadius: 10,
                  spreadRadius: 2,
                ),
              ],
            ),
            child: Icon(
              Icons.play_arrow,
              color: NeonColors.background,
              size: 24,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildWheel() {
    final segments = [
      ...GameConstants.luckyWheelTokenLabels.asMap().entries.map((entry) => 
        WheelSegment(
          label: '${entry.value} Tokens',
          color: _getSegmentColor(entry.key),
          isTokens: true,
        )
      ),
      WheelSegment(
        label: 'Bonus Key!',
        color: NeonColors.highlights,
        isTokens: false,
      ),
    ];

    return AnimatedBuilder(
      animation: _glowAnimation,
      builder: (context, child) {
        return Container(
          width: 300,
          height: 300,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            boxShadow: [
              BoxShadow(
                color: NeonColors.primaryAccent.withValues(alpha: _glowAnimation.value * 0.5),
                blurRadius: 30,
                spreadRadius: 10,
              ),
            ],
          ),
          child: CustomPaint(
            size: const Size(300, 300),
            painter: WheelPainter(segments),
          ),
        );
      },
    );
  }

  Color _getSegmentColor(int index) {
    final colors = [
      NeonColors.neonGreen,
      NeonColors.electricBlue,
      NeonColors.hotPink,
      NeonColors.orangeFlame,
      NeonColors.purpleGlow,
      NeonColors.secondaryAccent,
      NeonColors.primaryAccent,
    ];
    return colors[index % colors.length];
  }

  Widget _buildSpinButtons(bool canUseFree, bool canAffordPaid, GameProvider gameProvider) {
    return Column(
      children: [
        // Free Spin Button
        if (canUseFree)
          NeonButton(
            text: 'FREE DAILY SPIN',
            onPressed: _isSpinning ? null : () => _performSpin(true, gameProvider),
            glowColor: NeonColors.neonGreen,
            width: 200,
            height: 50,
          )
        else
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: NeonColors.textSecondary),
            ),
            child: Text(
              'Free spin used today',
              style: GoogleFonts.orbitron(
                color: NeonColors.textSecondary,
                fontSize: 16,
              ),
            ),
          ),
        
        const SizedBox(height: 16),
        
        // Paid Spin Button
        NeonButton(
          text: 'SPIN (${GameConstants.luckyWheelSpinCost} Tokens)',
          onPressed: (_isSpinning || !canAffordPaid) ? null : () => _performSpin(false, gameProvider),
          glowColor: canAffordPaid ? NeonColors.primaryAccent : NeonColors.textSecondary,
          width: 250,
          height: 50,
        ),
        
        if (!canAffordPaid)
          Padding(
            padding: const EdgeInsets.only(top: 8),
            child: Text(
              'Not enough tokens',
              style: GoogleFonts.orbitron(
                color: NeonColors.error,
                fontSize: 14,
              ),
            ),
          ),
      ],
    );
  }



  Future<void> _performSpin(bool useFree, GameProvider gameProvider) async {
    if (_isSpinning) return;
    
    setState(() => _isSpinning = true);
    
    try {
      // Calculate rotation
      _finalRotation = (3 + math.Random().nextInt(3)) * 2 * math.pi + 
                     (math.Random().nextDouble() * 2 * math.pi);
      
      // Start animation
      _rotationController.reset();
      await _rotationController.forward();
      
      // Perform actual spin
      final result = await gameProvider.spinWheel(useFree: useFree);
      
      // Update bonus keys
      await _loadBonusGameKeys();
      
      // Show result
      if (mounted) {
        _showSpinResult(result);
      }
      
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: $e'),
            backgroundColor: NeonColors.error,
          ),
        );
      }
    } finally {
      setState(() => _isSpinning = false);
    }
  }

  void _showSpinResult(SpinResult result) {
    // Calculate net gain for paid spins
    final netTokenGain = result.wasFree ? result.tokensWon :
                        result.tokensWon - GameConstants.luckyWheelSpinCost;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: NeonColors.background,
        title: Text(
          'Spin Result!',
          style: GoogleFonts.orbitron(color: NeonColors.primaryAccent),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              result.bonusGameKeysWon > 0 ? Icons.vpn_key :
              result.tokensWon > 0 ? Icons.star : Icons.refresh,
              color: result.bonusGameKeysWon > 0 ? NeonColors.highlights :
                     result.tokensWon > 0 ? NeonColors.neonGreen : NeonColors.textSecondary,
              size: 48,
            ),
            const SizedBox(height: 16),
            Text(
              result.label,
              style: GoogleFonts.orbitron(
                color: NeonColors.textPrimary,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            // Show net gain for paid spins with tokens
            if (!result.wasFree && result.tokensWon > 0) ...[
              const SizedBox(height: 8),
              Text(
                'Net gain: $netTokenGain tokens',
                style: GoogleFonts.orbitron(
                  color: netTokenGain > 0 ? NeonColors.neonGreen : NeonColors.error,
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
                textAlign: TextAlign.center,
              ),
              Text(
                '(${result.tokensWon} won - ${GameConstants.luckyWheelSpinCost} spin cost)',
                style: GoogleFonts.orbitron(
                  color: NeonColors.textSecondary,
                  fontSize: 12,
                ),
                textAlign: TextAlign.center,
              ),
            ],
            if (result.bonusGameKeysWon > 0) ...[
              const SizedBox(height: 8),
              Text(
                'Use 3 keys to unlock bonus games!',
                style: GoogleFonts.orbitron(
                  color: NeonColors.textSecondary,
                  fontSize: 14,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(
              'Awesome!',
              style: GoogleFonts.orbitron(color: NeonColors.primaryAccent),
            ),
          ),
        ],
      ),
    );
  }
}

class WheelSegment {
  final String label;
  final Color color;
  final bool isTokens;

  WheelSegment({
    required this.label,
    required this.color,
    required this.isTokens,
  });
}

class WheelPainter extends CustomPainter {
  final List<WheelSegment> segments;

  WheelPainter(this.segments);

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width / 2;
    final segmentAngle = 2 * math.pi / segments.length;

    for (int i = 0; i < segments.length; i++) {
      final startAngle = i * segmentAngle - math.pi / 2;
      final sweepAngle = segmentAngle;

      // Draw segment
      final paint = Paint()
        ..color = segments[i].color
        ..style = PaintingStyle.fill;

      canvas.drawArc(
        Rect.fromCircle(center: center, radius: radius),
        startAngle,
        sweepAngle,
        true,
        paint,
      );

      // Draw border
      final borderPaint = Paint()
        ..color = NeonColors.background
        ..style = PaintingStyle.stroke
        ..strokeWidth = 2;

      canvas.drawArc(
        Rect.fromCircle(center: center, radius: radius),
        startAngle,
        sweepAngle,
        true,
        borderPaint,
      );

      // Draw text
      final textAngle = startAngle + sweepAngle / 2;
      final textRadius = radius * 0.7;
      final textX = center.dx + textRadius * math.cos(textAngle);
      final textY = center.dy + textRadius * math.sin(textAngle);

      final textPainter = TextPainter(
        text: TextSpan(
          text: segments[i].label,
          style: GoogleFonts.orbitron(
            color: NeonColors.background,
            fontSize: 12,
            fontWeight: FontWeight.bold,
          ),
        ),
        textDirection: TextDirection.ltr,
      );

      textPainter.layout();
      
      canvas.save();
      canvas.translate(textX, textY);
      canvas.rotate(textAngle + math.pi / 2);
      textPainter.paint(canvas, Offset(-textPainter.width / 2, -textPainter.height / 2));
      canvas.restore();
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
