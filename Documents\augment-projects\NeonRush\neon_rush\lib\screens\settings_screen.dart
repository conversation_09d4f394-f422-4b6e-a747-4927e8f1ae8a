import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../constants.dart';
import '../core/constants.dart' as core_constants;
import '../providers/game_provider.dart';
import '../providers/theme_provider.dart';
import '../services/audio_service.dart';
import '../services/spin_wheel_service.dart';
import '../services/token_service.dart';
import '../ui/neon_text.dart';
import '../ui/neon_button.dart';
import '../ui/neon_container.dart';
import '../models/neon_theme.dart';
import '../models/token_transaction.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  final AudioService _audioService = AudioService();
  bool _showAdminPanel = false;
  int _aboutPressCount = 0;
  DateTime? _lastAboutPress;

  @override
  void initState() {
    super.initState();
    _audioService.initialize();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: NeonColors.background,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: NeonColors.primaryAccent,
          ),
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: NeonText.heading(
          'Settings',
          glowColor: NeonColors.primaryAccent,
        ),
        centerTitle: true,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildAudioSection(),
            const SizedBox(height: 24),
            _buildThemeSection(),
            const SizedBox(height: 24),
            _buildAboutSection(),
            if (_showAdminPanel) ...[
              const SizedBox(height: 24),
              _buildAdminPanel(),
            ],
            // Bottom padding to prevent clipping under system nav buttons
            SizedBox(height: core_constants.GameConstants.bottomSystemNavPadding),
          ],
        ),
      ),
    );
  }

  Widget _buildAudioSection() {
    return NeonContainer(
      glowColor: NeonColors.primaryAccent,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          NeonText.subtitle(
            'Audio Controls',
            glowColor: NeonColors.primaryAccent,
          ),
          const SizedBox(height: 16),
          _buildMusicControls(),
        ],
      ),
    );
  }

  Widget _buildMusicControls() {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            NeonText.body(
              'Background Music',
              glowColor: NeonColors.textPrimary,
            ),
            Switch(
              value: _audioService.musicEnabled,
              onChanged: (value) async {
                await _audioService.setMusicEnabled(value);
                setState(() {});
              },
              activeColor: NeonColors.primaryAccent,
              activeTrackColor: NeonColors.primaryAccent.withValues(alpha: 0.3),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            NeonText.caption(
              'Volume',
              glowColor: NeonColors.textSecondary,
            ),
            Expanded(
              child: Slider(
                value: _audioService.musicVolume,
                onChanged: _audioService.musicEnabled
                    ? (value) async {
                        await _audioService.setMusicVolume(value);
                        setState(() {});
                      }
                    : null,
                activeColor: NeonColors.primaryAccent,
                inactiveColor: NeonColors.textSecondary.withValues(alpha: 0.3),
              ),
            ),
            NeonText.caption(
              '${(_audioService.musicVolume * 100).round()}%',
              glowColor: NeonColors.textSecondary,
            ),
          ],
        ),
      ],
    );
  }



  Widget _buildThemeSection() {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        return NeonContainer(
          glowColor: NeonColors.primaryAccent,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              NeonText.subtitle(
                'Theme Selection',
                glowColor: NeonColors.primaryAccent,
              ),
              const SizedBox(height: 16),
              _buildThemeGrid(themeProvider),
            ],
          ),
        );
      },
    );
  }

  Widget _buildThemeGrid(ThemeProvider themeProvider) {
    final availableThemes = themeProvider.availableThemes;
    final lockedThemes = themeProvider.lockedThemes;
    final allThemes = [...availableThemes, ...lockedThemes];

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3,
        crossAxisSpacing: 8,
        mainAxisSpacing: 8,
        childAspectRatio: 1.2,
      ),
      itemCount: allThemes.length,
      itemBuilder: (context, index) {
        final theme = allThemes[index];
        final isUnlocked = themeProvider.isThemeUnlocked(theme.id);
        final isCurrent = themeProvider.currentTheme.id == theme.id;

        return GestureDetector(
          onTap: isUnlocked
              ? () async {
                  await themeProvider.changeTheme(theme.id);
                  await _audioService.playButtonClick();
                }
              : null,
          child: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: theme.gradientColors,
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: isCurrent
                    ? NeonColors.primaryAccent
                    : isUnlocked
                        ? theme.primary.withValues(alpha: 0.5)
                        : NeonColors.textSecondary.withValues(alpha: 0.3),
                width: isCurrent ? 3 : 1,
              ),
              boxShadow: isCurrent
                  ? [
                      BoxShadow(
                        color: NeonColors.primaryAccent.withValues(alpha: 0.5),
                        blurRadius: 8,
                        spreadRadius: 2,
                      ),
                    ]
                  : null,
            ),
            child: Stack(
              children: [
                Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      NeonText(
                        theme.name,
                        glowColor: theme.primary,
                        textColor: Colors.white,
                        fontSize: 10,
                        fontWeight: FontWeight.w400,
                        glowIntensity: 0.4,
                      ),
                      if (isCurrent)
                        Icon(
                          Icons.check_circle,
                          color: NeonColors.primaryAccent,
                          size: 16,
                        ),
                    ],
                  ),
                ),
                if (!isUnlocked)
                  Container(
                    decoration: BoxDecoration(
                      color: Colors.black.withValues(alpha: 0.7),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: const Center(
                      child: Icon(
                        Icons.lock,
                        color: Colors.white,
                        size: 20,
                      ),
                    ),
                  ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildAboutSection() {
    return NeonContainer(
      glowColor: NeonColors.primaryAccent,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          NeonText.subtitle(
            'About',
            glowColor: NeonColors.primaryAccent,
          ),
          const SizedBox(height: 16),
          _buildAboutItem('Game Version', '1.0.0'),
          _buildAboutItem('Developer', 'Augment Code'),
          _buildAboutItem('Engine', 'Flutter'),
          const SizedBox(height: 16),
          Center(
            child: GestureDetector(
              onTap: _handleAboutAppPress,
              child: NeonButton(
                text: 'About App',
                onPressed: _handleAboutAppPress,
                glowColor: NeonColors.primaryAccent,
                width: 150,
                height: 40,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAboutItem(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          NeonText.body(
            label,
            glowColor: NeonColors.textSecondary,
          ),
          NeonText.body(
            value,
            glowColor: NeonColors.textPrimary,
          ),
        ],
      ),
    );
  }

  void _handleAboutAppPress() async {
    await _audioService.playButtonClick();
    
    final now = DateTime.now();
    if (_lastAboutPress != null && 
        now.difference(_lastAboutPress!).inSeconds > 2) {
      _aboutPressCount = 0;
    }
    
    _aboutPressCount++;
    _lastAboutPress = now;
    
    if (_aboutPressCount >= 5) {
      setState(() {
        _showAdminPanel = true;
        _aboutPressCount = 0;
      });
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: NeonText.body(
              'Admin panel unlocked!',
              glowColor: NeonColors.primaryAccent,
            ),
            backgroundColor: NeonColors.surface,
            duration: const Duration(seconds: 2),
          ),
        );
      }
    }
  }

  Widget _buildAdminPanel() {
    return Consumer<GameProvider>(
      builder: (context, gameProvider, child) {
        return NeonContainer(
          glowColor: Colors.red,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  NeonText.subtitle(
                    'Admin Panel',
                    glowColor: Colors.red,
                  ),
                  IconButton(
                    icon: Icon(Icons.close, color: Colors.red),
                    onPressed: () {
                      setState(() {
                        _showAdminPanel = false;
                      });
                    },
                  ),
                ],
              ),
              const SizedBox(height: 16),
              _buildAdminButton(
                'Add 1000 Tokens',
                () async {
                  await gameProvider.earnTokens(
                    1000,
                    TokenTransactionType.mysteryBox,
                    'Admin: Added 1000 tokens',
                  );
                  await _audioService.playReward();
                },
              ),
              const SizedBox(height: 8),
              _buildAdminButton(
                'Add 10 Keys',
                () async {
                  await SpinWheelService.awardBonusGameKeys(10);
                  await _audioService.playReward();
                },
              ),
              const SizedBox(height: 8),
              _buildAdminButton(
                'Reset Progress',
                () async {
                  await _resetProgress();
                  await _audioService.playButtonClick();
                },
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildAdminButton(String text, VoidCallback onPressed) {
    return SizedBox(
      width: double.infinity,
      child: NeonButton(
        text: text,
        onPressed: onPressed,
        glowColor: Colors.red,
        height: 40,
      ),
    );
  }

  /// Reset all game progress (admin function)
  Future<void> _resetProgress() async {
    final prefs = await SharedPreferences.getInstance();

    // Reset token balance
    await TokenService.setTokenBalance(core_constants.GameConstants.startingTokens);

    // Reset bonus game keys
    await prefs.setInt('bonus_game_keys', 0);

    // Reset current level
    await prefs.setInt('current_level', 1);

    // Clear unlocked modes and bonus games
    await prefs.remove(StorageKeys.unlockedModes);
    await prefs.remove(StorageKeys.unlockedBonusGames);

    // Clear achievements and statistics
    await prefs.remove(StorageKeys.achievements);
    await prefs.remove(StorageKeys.statistics);

    // Clear transaction history
    await prefs.remove('transaction_history');

    // Reset daily bonus
    await prefs.remove('daily_bonus_claimed');
    await prefs.remove('last_login_date');
    await prefs.setInt('login_streak', 0);

    // Clear trial counts
    final bonusGames = ['neon_snake', 'pong', 'astro_blaster', 'crate_smash', 'glow_flow',
                       'neon_stack', 'swipe_slice', 'tap_runner', 'neon_dig', 'slide_match',
                       'tap_shot', 'drag_maze', 'color_zap', 'bounce_tap', 'tap_craft'];
    for (final gameId in bonusGames) {
      await prefs.remove('trial_count_$gameId');
      await prefs.remove('pinned_$gameId');
    }

    // Reinitialize the game provider
    if (mounted) {
      final gameProvider = Provider.of<GameProvider>(context, listen: false);
      await gameProvider.initialize();
    }

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('All progress has been reset!'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }
}
