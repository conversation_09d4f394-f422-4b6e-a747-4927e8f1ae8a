import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../game/game_state.dart';
import '../models/neon_theme.dart';
import '../ui/neon_text.dart';
import '../ui/neon_container.dart';


/// Shop screen for purchasing power-ups and upgrades
class ShopScreen extends StatefulWidget {
  const ShopScreen({super.key});

  @override
  State<ShopScreen> createState() => _ShopScreenState();
}

class _ShopScreenState extends State<ShopScreen>
    with TickerProviderStateMixin {
  late AnimationController _backgroundController;
  late Animation<double> _backgroundAnimation;
  
  @override
  void initState() {
    super.initState();
    _backgroundController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    );
    _backgroundAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _backgroundController,
      curve: Curves.easeInOut,
    ));
    _backgroundController.repeat(reverse: true);
  }

  @override
  void dispose() {
    _backgroundController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: AnimatedBuilder(
        animation: _backgroundAnimation,
        builder: (context, child) {
          return Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  Colors.black,
                  NeonThemes.cyberBlue.primary.withValues(alpha: 0.03 * _backgroundAnimation.value),
                  NeonThemes.neonPink.primary.withValues(alpha: 0.03 * _backgroundAnimation.value),
                  NeonThemes.electricGreen.primary.withValues(alpha: 0.03 * _backgroundAnimation.value),
                  Colors.black,
                ],
                stops: [0.0, 0.25, 0.5, 0.75, 1.0],
              ),
            ),
            child: SafeArea(
              child: Column(
                children: [
                  _buildHeader(),
                  Expanded(
                    child: _buildShopContent(),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildHeader() {
    return Consumer<GameStateManager>(
      builder: (context, gameState, child) {
        final profile = gameState.playerProfile;
        return Container(
          padding: const EdgeInsets.all(20),
          child: Row(
            children: [
              // Back button
              IconButton(
                icon: Icon(
                  Icons.arrow_back,
                  color: NeonThemes.cyberBlue.primary,
                  size: 24,
                ),
                onPressed: () {
                  Navigator.of(context).pop();
                },
              ),
              
              const SizedBox(width: 20),
              
              // Shop title
              Expanded(
                child: NeonText.title(
                  'SHOP',
                  glowColor: NeonThemes.neonPink.primary,
                  fontSize: 32,
                  fontWeight: FontWeight.w900,
                ),
              ),
              
              const SizedBox(width: 20),
              
              // Player tokens
              NeonContainer.card(
                glowColor: NeonThemes.electricGreen.primary,
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.monetization_on,
                        color: NeonThemes.electricGreen.primary,
                        size: 20,
                      ),
                      const SizedBox(width: 8),
                      NeonText.body(
                        '${profile?.tokens ?? 0}',
                        glowColor: NeonThemes.electricGreen.primary,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildShopContent() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          const SizedBox(height: 50),

          // Shop icon
          Icon(
            Icons.shopping_bag_outlined,
            size: 80,
            color: NeonThemes.neonPink.primary,
          ),

          const SizedBox(height: 30),

          // Shop description
          NeonText.title(
            'Shop Coming Soon!',
            glowColor: NeonThemes.neonPink.primary,
            fontSize: 28,
          ),

          const SizedBox(height: 20),

          NeonText.body(
            'The token shop is under construction.\nNew items and upgrades will be available soon!',
            glowColor: NeonThemes.cyberBlue.secondary,
            fontSize: 16,
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: 40),

          // Token balance display
          Consumer<GameStateManager>(
            builder: (context, gameState, child) {
              final profile = gameState.playerProfile;
              return NeonContainer.card(
                glowColor: NeonThemes.electricGreen.primary,
                child: Padding(
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    children: [
                      NeonText.subtitle(
                        'Your Token Balance',
                        glowColor: NeonThemes.electricGreen.primary,
                      ),
                      const SizedBox(height: 10),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.monetization_on,
                            color: NeonThemes.electricGreen.primary,
                            size: 24,
                          ),
                          const SizedBox(width: 8),
                          NeonText.title(
                            '${profile?.tokens ?? 0}',
                            glowColor: NeonThemes.electricGreen.primary,
                            fontSize: 32,
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        ],
      ),
    );
  }



}


