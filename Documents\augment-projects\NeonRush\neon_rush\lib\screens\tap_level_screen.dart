import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../core/constants.dart';
import '../models/level_config.dart';
import '../game/game_state.dart';
import '../levels/tap_level.dart';
import '../ui/neon_widgets.dart';
import '../ui/dialogs/level_completion_dialog.dart';
import '../ui/power_up_hud.dart';
import '../game/power_up_manager.dart';
import '../core/sound_manager.dart';

/// Screen for playing the tap level
class TapLevelScreen extends StatefulWidget {
  final LevelConfig levelConfig;

  const TapLevelScreen({
    super.key,
    required this.levelConfig,
  });

  @override
  State<TapLevelScreen> createState() => _TapLevelScreenState();
}

class _TapLevelScreenState extends State<TapLevelScreen>
    with TickerProviderStateMixin {
  late TapGameLogic _gameLogic;
  late PowerUpManager _powerUpManager;
  late AnimationController _backgroundController;
  late Animation<double> _backgroundAnimation;
  
  @override
  void initState() {
    super.initState();

    final gameState = context.read<GameStateManager>();
    _powerUpManager = PowerUpManager();
    _gameLogic = TapGameLogic(
      levelConfig: widget.levelConfig,
      gameState: gameState,
      powerUpManager: _powerUpManager,
    );
    
    // Background animation
    _backgroundController = AnimationController(
      duration: const Duration(seconds: 10),
      vsync: this,
    );
    _backgroundAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(_backgroundController);
    _backgroundController.repeat();
    
    // Start the game after the widget is built
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _startGame();
    });
  }

  @override
  void dispose() {
    _gameLogic.dispose();
    _powerUpManager.dispose();
    _backgroundController.dispose();
    super.dispose();
  }

  void _startGame() {
    final renderBox = context.findRenderObject() as RenderBox?;
    if (renderBox != null) {
      final size = renderBox.size;
      _gameLogic.startGame(size);
    }
  }

  void _handleTap(TapDownDetails details) {
    final gameState = context.read<GameStateManager>();
    if (gameState.currentState == GameState.playing) {
      _gameLogic.handleTap(details.localPosition);
    }
  }

  void _pauseGame() {
    final gameState = context.read<GameStateManager>();
    gameState.pauseGame();
    _gameLogic.pauseGame();
  }

  void _resumeGame() {
    final gameState = context.read<GameStateManager>();
    gameState.resumeGame();
    _gameLogic.resumeGame();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: widget.levelConfig.theme.background,
      body: Consumer<GameStateManager>(
        builder: (context, gameState, child) {
          // Show level completion dialog when level is completed
          if (gameState.currentState == GameState.levelComplete) {
            WidgetsBinding.instance.addPostFrameCallback((_) {
              _showLevelCompletionDialog(gameState);
            });
          }

          return Stack(
            children: [
              // Animated background
              _buildAnimatedBackground(),

              // Game area
              Positioned.fill(
                child: GestureDetector(
                  onTapDown: _handleTap,
                  child: Container(
                    decoration: BoxDecoration(
                      gradient: NeonEffects.createRadialNeonGradient(
                        colors: [
                          widget.levelConfig.theme.background,
                          widget.levelConfig.theme.background.withOpacity(0.8),
                        ],
                      ),
                    ),
                    child: _buildGameContent(gameState),
                  ),
                ),
              ),

              // UI Overlay
              _buildUIOverlay(gameState),

              // Power-up HUD
              ChangeNotifierProvider.value(
                value: _powerUpManager,
                child: const PowerUpHUD(),
              ),

              // Pause overlay
              if (gameState.currentState == GameState.paused)
                _buildPauseOverlay(),
            ],
          );
        },
      ),
    );
  }

  Widget _buildAnimatedBackground() {
    return AnimatedBuilder(
      animation: _backgroundAnimation,
      builder: (context, child) {
        return Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                widget.levelConfig.theme.background,
                widget.levelConfig.theme.primary.withOpacity(0.1),
                widget.levelConfig.theme.secondary.withOpacity(0.1),
                widget.levelConfig.theme.background,
              ],
              stops: [
                0.0,
                0.3 + _backgroundAnimation.value * 0.2,
                0.7 + _backgroundAnimation.value * 0.2,
                1.0,
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildGameContent(GameStateManager gameState) {
    final activeDots = gameState.getGameData<List<Map<String, dynamic>>>('activeDots') ?? [];
    
    return Stack(
      children: [
        // Render active dots
        ...activeDots.map((dotData) {
          final dot = TapDot.fromMap(dotData);
          return _buildDot(dot);
        }).toList(),
        
        // Power-up effects
        if (gameState.isPowerUpActive('slow_motion'))
          _buildSlowMotionEffect(),
        
        if (gameState.isPowerUpActive('magnet_touch'))
          _buildMagnetTouchEffect(),
      ],
    );
  }

  Widget _buildDot(TapDot dot) {
    return Positioned(
      left: dot.position.dx - dot.size / 2,
      top: dot.position.dy - dot.size / 2,
      child: AnimatedOpacity(
        opacity: dot.opacity,
        duration: const Duration(milliseconds: 100),
        child: Transform.scale(
          scale: dot.scale,
          child: Container(
            width: dot.size,
            height: dot.size,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: dot.color,
              boxShadow: NeonEffects.createGlow(
                color: dot.color,
                intensity: dot.opacity,
              ),
            ),
            child: Center(
              child: Container(
                width: dot.size * 0.6,
                height: dot.size * 0.6,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: Colors.white.withOpacity(0.8),
                ),
              ),
            ),
          ).withPulse(),
        ),
      ),
    );
  }

  Widget _buildSlowMotionEffect() {
    return Positioned.fill(
      child: Container(
        decoration: BoxDecoration(
          gradient: RadialGradient(
            colors: [
              Colors.blue.withOpacity(0.1),
              Colors.transparent,
            ],
          ),
        ),
        child: const Center(
          child: Icon(
            Icons.slow_motion_video,
            size: 100,
            color: Colors.blue,
          ),
        ).withPulse(),
      ),
    );
  }

  Widget _buildMagnetTouchEffect() {
    return Positioned.fill(
      child: Container(
        decoration: BoxDecoration(
          gradient: RadialGradient(
            colors: [
              Colors.yellow.withOpacity(0.1),
              Colors.transparent,
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildUIOverlay(GameStateManager gameState) {
    return SafeArea(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            // Top bar
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                // Score
                NeonContainer.badge(
                  glowColor: widget.levelConfig.theme.primary,
                  child: NeonText.body(
                    'Score: ${gameState.currentScore}',
                    glowColor: widget.levelConfig.theme.primary,
                  ),
                ),
                
                // Pause button
                NeonCircularButton(
                  icon: Icons.pause,
                  glowColor: widget.levelConfig.theme.secondary,
                  onPressed: _pauseGame,
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // Progress bar
            NeonProgressBar(
              value: gameState.getLevelProgress(),
              glowColor: widget.levelConfig.theme.primary,
              label: 'Progress: ${gameState.getGameData<int>('dotsHit') ?? 0}/${widget.levelConfig.goal}',
            ),
            
            const SizedBox(height: 16),
            
            // Time remaining
            NeonContainer.badge(
              glowColor: widget.levelConfig.theme.accent,
              child: NeonText.body(
                'Time: ${gameState.timeRemaining.inSeconds}s',
                glowColor: widget.levelConfig.theme.accent,
              ),
            ),
            
            const Spacer(),
            
            // Instructions (only show for first few seconds)
            if (gameState.timeElapsed.inSeconds < 3)
              NeonContainer.card(
                glowColor: widget.levelConfig.theme.secondary,
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: widget.levelConfig.instructions.map((instruction) {
                    return NeonText.body(
                      instruction,
                      glowColor: widget.levelConfig.theme.secondary,
                      textAlign: TextAlign.center,
                    );
                  }).toList(),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildPauseOverlay() {
    return Container(
      color: Colors.black.withOpacity(0.8),
      child: Center(
        child: NeonContainer.panel(
          glowColor: widget.levelConfig.theme.primary,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              NeonText.title(
                'PAUSED',
                glowColor: widget.levelConfig.theme.primary,
              ),
              
              const SizedBox(height: 32),
              
              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  NeonButton.secondary(
                    text: 'Resume',
                    glowColor: widget.levelConfig.theme.primary,
                    onPressed: _resumeGame,
                  ),
                  
                  const SizedBox(width: 16),
                  
                  NeonButton.secondary(
                    text: 'Quit',
                    glowColor: widget.levelConfig.theme.secondary,
                    onPressed: () {
                      final gameState = context.read<GameStateManager>();
                      gameState.returnToMenu();
                      Navigator.of(context).pop();
                    },
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Show the level completion dialog
  void _showLevelCompletionDialog(GameStateManager gameState) {
    final completionData = gameState.getLevelCompletionData();

    if (completionData.isEmpty) return;

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => LevelCompletionDialog(
        level: completionData['level'] as LevelConfig,
        score: completionData['score'] as int,
        timeBonus: completionData['timeBonus'] as int,
        totalXP: completionData['totalXP'] as int,
        tokensEarned: completionData['tokensEarned'] as int,
        onReplay: () {
          Navigator.of(context).pop(); // Close dialog
          gameState.startLevel(widget.levelConfig);
          // Get the screen size for the game area
          final screenSize = MediaQuery.of(context).size;
          _gameLogic.startGame(screenSize);
        },
        onNextLevel: completionData['hasNextLevel'] as bool && completionData['nextLevelUnlocked'] as bool
            ? () {
                Navigator.of(context).pop(); // Close dialog
                final nextLevelNumber = widget.levelConfig.levelNumber + 1;
                final nextLevel = LevelConfigs.allLevels.firstWhere(
                  (level) => level.levelNumber == nextLevelNumber,
                );
                Navigator.of(context).pushReplacement(
                  MaterialPageRoute(
                    builder: (context) => TapLevelScreen(levelConfig: nextLevel),
                  ),
                );
              }
            : null,
        onBackToMenu: () {
          Navigator.of(context).pop(); // Close dialog
          gameState.returnToMenu();
          Navigator.of(context).pop(); // Return to home screen
        },
      ),
    );
  }
}
