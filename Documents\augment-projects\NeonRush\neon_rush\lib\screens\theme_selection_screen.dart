import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../providers/theme_provider.dart';
import '../models/neon_theme.dart';
import '../ui/neon_text.dart';
import '../ui/neon_button.dart';
import '../core/sound_manager.dart';

class ThemeSelectionScreen extends StatelessWidget {
  const ThemeSelectionScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: RadialGradient(
            center: Alignment.center,
            radius: 1.5,
            colors: [
              context.watch<ThemeProvider>().currentTheme.background,
              Colors.black,
            ],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              _buildHeader(context),
              Expanded(
                child: _buildThemeGrid(context),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          IconButton(
            icon: Icon(
              Icons.arrow_back,
              color: context.watch<ThemeProvider>().currentTheme.primary,
              size: 28,
            ),
            onPressed: () {
              SoundManager().playSfx(SoundType.buttonClick);
              Navigator.of(context).pop();
            },
          ),
          const SizedBox(width: 16),
          Expanded(
            child: NeonText(
              'Choose Theme',
              fontSize: 28,
              textColor: context.watch<ThemeProvider>().currentTheme.primary,
              glowColor: context.watch<ThemeProvider>().currentTheme.primary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildThemeGrid(BuildContext context) {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        final allThemes = NeonThemes.allThemes;
        
        return GridView.builder(
          padding: const EdgeInsets.all(16),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 2,
            childAspectRatio: 1.2,
            crossAxisSpacing: 16,
            mainAxisSpacing: 16,
          ),
          itemCount: allThemes.length,
          itemBuilder: (context, index) {
            final theme = allThemes[index];
            final isUnlocked = themeProvider.isThemeUnlocked(theme.id);
            final isCurrent = themeProvider.currentTheme.id == theme.id;
            final canUnlock = themeProvider.canUnlockTheme(theme.id);
            
            return _buildThemeCard(
              context,
              theme,
              isUnlocked,
              isCurrent,
              canUnlock,
              themeProvider,
            );
          },
        );
      },
    );
  }

  Widget _buildThemeCard(
    BuildContext context,
    NeonTheme theme,
    bool isUnlocked,
    bool isCurrent,
    bool canUnlock,
    ThemeProvider themeProvider,
  ) {
    return GestureDetector(
      onTap: () => _handleThemeSelection(context, theme, isUnlocked, canUnlock, themeProvider),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isCurrent 
                ? theme.primary 
                : isUnlocked 
                    ? theme.primary.withValues(alpha: 0.5)
                    : Colors.grey.withValues(alpha: 0.3),
            width: isCurrent ? 3 : 1,
          ),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: isUnlocked
                ? [
                    theme.background.withValues(alpha: 0.8),
                    theme.background.withValues(alpha: 0.6),
                  ]
                : [
                    Colors.grey.withValues(alpha: 0.2),
                    Colors.grey.withValues(alpha: 0.1),
                  ],
          ),
          boxShadow: isCurrent
              ? [
                  BoxShadow(
                    color: theme.primary.withValues(alpha: 0.5),
                    blurRadius: 12,
                    spreadRadius: 2,
                  ),
                ]
              : null,
        ),
        child: Stack(
          children: [
            // Theme preview
            Positioned.fill(
              child: Container(
                margin: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                  gradient: LinearGradient(
                    colors: isUnlocked ? theme.gradientColors : [Colors.grey, Colors.grey.shade800],
                  ),
                ),
              ),
            ),
            
            // Theme info
            Positioned(
              bottom: 8,
              left: 8,
              right: 8,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  NeonText(
                    theme.name,
                    fontSize: 14,
                    textColor: isUnlocked ? theme.primary : Colors.grey,
                    glowColor: isUnlocked ? theme.primary.withValues(alpha: 0.5) : Colors.transparent,
                  ),
                  if (!isUnlocked) ...[
                    const SizedBox(height: 4),
                    _buildUnlockInfo(context, theme, canUnlock, themeProvider),
                  ],
                ],
              ),
            ),
            
            // Current theme indicator
            if (isCurrent)
              Positioned(
                top: 8,
                right: 8,
                child: Container(
                  padding: const EdgeInsets.all(4),
                  decoration: BoxDecoration(
                    color: theme.primary,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    Icons.check,
                    color: theme.background,
                    size: 16,
                  ),
                ),
              ),
            
            // Lock overlay
            if (!isUnlocked)
              Positioned.fill(
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12),
                    color: Colors.black.withValues(alpha: 0.6),
                  ),
                  child: Center(
                    child: Icon(
                      Icons.lock,
                      color: Colors.grey,
                      size: 32,
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildUnlockInfo(BuildContext context, NeonTheme theme, bool canUnlock, ThemeProvider themeProvider) {
    final requirements = themeProvider.getThemeUnlockRequirements(theme.id);
    final requirementText = requirements['requirement'] as String;
    
    if (requirements['type'] == 'tokens' && canUnlock) {
      return NeonButton(
        text: 'Purchase',
        onPressed: () => _purchaseTheme(context, theme, themeProvider),
        glowColor: theme.primary,
        fontSize: 10,
      );
    }
    
    return NeonText(
      requirementText,
      fontSize: 10,
      textColor: canUnlock ? Colors.green : Colors.grey,
      glowColor: canUnlock ? Colors.green.withValues(alpha: 0.5) : Colors.transparent,
    );
  }

  void _handleThemeSelection(
    BuildContext context,
    NeonTheme theme,
    bool isUnlocked,
    bool canUnlock,
    ThemeProvider themeProvider,
  ) async {
    SoundManager().playSfx(SoundType.buttonClick);
    
    if (isUnlocked) {
      try {
        await themeProvider.changeTheme(theme.id);
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Theme changed to ${theme.name}'),
              backgroundColor: theme.primary,
            ),
          );
        }
      } catch (e) {
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Error changing theme: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } else if (canUnlock) {
      final requirements = themeProvider.getThemeUnlockRequirements(theme.id);
      if (requirements['type'] == 'tokens') {
        _purchaseTheme(context, theme, themeProvider);
      } else {
        _showUnlockDialog(context, theme, requirements);
      }
    } else {
      _showUnlockDialog(context, theme, themeProvider.getThemeUnlockRequirements(theme.id));
    }
  }

  void _purchaseTheme(BuildContext context, NeonTheme theme, ThemeProvider themeProvider) async {
    try {
      final success = await themeProvider.purchaseTheme(theme.id);
      if (success && context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${theme.name} theme unlocked!'),
            backgroundColor: theme.primary,
          ),
        );
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error purchasing theme: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _showUnlockDialog(BuildContext context, NeonTheme theme, Map<String, dynamic> requirements) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: theme.background,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
          side: BorderSide(color: theme.primary, width: 2),
        ),
        title: NeonText(
          'Theme Locked',
          fontSize: 20,
          textColor: theme.primary,
          glowColor: theme.primary,
        ),
        content: NeonText(
          'Unlock requirement: ${requirements['requirement']}',
          fontSize: 16,
          textColor: Colors.white,
          glowColor: Colors.white.withValues(alpha: 0.5),
        ),
        actions: [
          NeonButton(
            text: 'OK',
            onPressed: () => Navigator.of(context).pop(),
            glowColor: theme.primary,
          ),
        ],
      ),
    );
  }
}
