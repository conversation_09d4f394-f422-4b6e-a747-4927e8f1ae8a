import 'dart:math';
import '../core/constants.dart';
import '../models/level_config.dart';
import '../models/neon_theme.dart';

/// Service for generating and managing level configurations
class LevelService {
  /// Generate a specific level configuration
  static LevelConfig getLevelConfig(int levelNumber) {
    return LevelConfig(
      levelNumber: levelNumber,
      title: 'Level $levelNumber',
      description: _getLevelDescription(levelNumber),
      mode: _getGameMode(levelNumber),
      duration: _calculateDuration(levelNumber),
      goal: _calculateGoal(levelNumber),
      speed: _calculateSpeed(levelNumber),
      theme: _getLevelTheme(levelNumber),
      difficulty: _getDifficultyLevel(levelNumber),
      xpReward: _calculateXpReward(levelNumber),
      tokenReward: 20, // Fixed token reward per level
      instructions: _getLevelInstructions(levelNumber),
      modeSpecificConfig: _getModeSpecificConfig(levelNumber),
    );
  }

  /// Get level description
  static String _getLevelDescription(int levelNumber) {
    if (levelNumber <= 10) {
      return 'Tap the glowing neon targets to score points';
    } else if (levelNumber <= 20) {
      return 'Swipe in the direction shown by the arrows';
    } else if (levelNumber <= 30) {
      return 'Hold down on targets for the required time';
    } else if (levelNumber <= 40) {
      return 'Avoid the red danger zones while hitting targets';
    } else {
      return 'Complete the challenging neon objectives';
    }
  }

  /// Get game mode based on level
  static GameMode _getGameMode(int levelNumber) {
    final modeIndex = ((levelNumber - 1) / 10).floor();
    final modes = [
      GameMode.tap,
      GameMode.swipe,
      GameMode.hold,
      GameMode.avoid,
      GameMode.drag,
    ];
    return modes[min(modeIndex, modes.length - 1)];
  }

  /// Calculate level duration
  static Duration _calculateDuration(int levelNumber) {
    // Base time of 60 seconds, increases by 5 seconds every 10 levels
    final baseTime = 60;
    final bonus = ((levelNumber - 1) / 10).floor() * 5;
    return Duration(seconds: baseTime + bonus);
  }

  /// Calculate level goal (score target)
  static int _calculateGoal(int levelNumber) {
    // Base goal of 100, increases by 50 per level
    return 100 + (levelNumber * 50);
  }

  /// Calculate game speed
  static double _calculateSpeed(int levelNumber) {
    // Speed increases gradually with level
    return 1.0 + (levelNumber * 0.05);
  }

  /// Get level theme
  static NeonTheme _getLevelTheme(int levelNumber) {
    final themes = [
      NeonThemes.cyberBlue,
      NeonThemes.neonPink,
      NeonThemes.electricGreen,
      NeonThemes.fireOrange,
      NeonThemes.purpleHaze,
    ];
    
    final themeIndex = ((levelNumber - 1) / 20).floor();
    return themes[min(themeIndex, themes.length - 1)];
  }

  /// Get difficulty level
  static DifficultyLevel _getDifficultyLevel(int levelNumber) {
    if (levelNumber <= 25) return DifficultyLevel.easy;
    if (levelNumber <= 50) return DifficultyLevel.medium;
    if (levelNumber <= 75) return DifficultyLevel.hard;
    return DifficultyLevel.expert;
  }

  /// Calculate XP reward
  static int _calculateXpReward(int levelNumber) {
    // Base XP of 10, increases by 5 per level
    return 10 + (levelNumber * 5);
  }

  /// Get level instructions
  static List<String> _getLevelInstructions(int levelNumber) {
    final mode = _getGameMode(levelNumber);
    
    switch (mode) {
      case GameMode.tap:
        return ['Tap the glowing targets', 'Avoid red danger zones', 'Score ${_calculateGoal(levelNumber)} points'];
      case GameMode.swipe:
        return ['Swipe in the arrow direction', 'Match the swipe pattern', 'Complete within time limit'];
      case GameMode.hold:
        return ['Hold down on targets', 'Maintain pressure until complete', 'Release at the right time'];
      case GameMode.avoid:
        return ['Avoid red danger zones', 'Hit only safe targets', 'Survive the level'];
      case GameMode.drag:
        return ['Drag targets to destinations', 'Follow the path shown', 'Complete all objectives'];
      default:
        return ['Complete the level objectives', 'Follow on-screen instructions'];
    }
  }

  /// Get mode-specific configuration
  static Map<String, dynamic> _getModeSpecificConfig(int levelNumber) {
    final mode = _getGameMode(levelNumber);
    
    switch (mode) {
      case GameMode.tap:
        return {
          'targetCount': 10 + (levelNumber * 2),
          'targetSize': max(40.0, 80.0 - (levelNumber * 2)),
          'spawnRate': min(2.0, 0.5 + (levelNumber * 0.1)),
        };
      case GameMode.swipe:
        return {
          'swipePatterns': min(3 + (levelNumber / 5).floor(), 8),
          'swipeSpeed': 1.0 + (levelNumber * 0.05),
          'accuracy': 0.8,
        };
      case GameMode.hold:
        return {
          'holdDuration': min(1000 + (levelNumber * 100), 3000), // milliseconds
          'tolerance': max(0.1, 0.3 - (levelNumber * 0.01)),
        };
      case GameMode.avoid:
        return {
          'dangerZones': min(1 + (levelNumber / 10).floor(), 5),
          'safeTargets': 5 + levelNumber,
          'moveSpeed': 1.0 + (levelNumber * 0.03),
        };
      case GameMode.drag:
        return {
          'dragDistance': 50.0 + (levelNumber * 5),
          'pathComplexity': min(levelNumber / 10, 3),
          'accuracy': 0.85,
        };
      default:
        return {};
    }
  }

  /// Check if level is unlocked
  static bool isLevelUnlocked(int levelNumber, int currentLevel) {
    return levelNumber <= currentLevel;
  }

  /// Get level preview data for UI
  static Map<String, dynamic> getLevelPreview(int levelNumber) {
    final config = getLevelConfig(levelNumber);
    
    return {
      'level': levelNumber,
      'title': config.title,
      'description': config.description,
      'mode': config.mode.toString().split('.').last,
      'difficulty': config.difficulty.toString().split('.').last,
      'duration': config.duration.inSeconds,
      'goal': config.goal,
      'xpReward': config.xpReward,
      'tokenReward': config.tokenReward,
    };
  }

  /// Validate level configuration
  static bool validateLevelConfig(LevelConfig config) {
    return config.levelNumber >= 1 &&
           config.levelNumber <= 100 &&
           config.goal > 0 &&
           config.duration.inSeconds > 0 &&
           config.speed > 0;
  }

  /// Get total number of levels
  static int get totalLevels => 100;

  /// Get levels by difficulty
  static List<int> getLevelsByDifficulty(DifficultyLevel difficulty) {
    final levels = <int>[];
    
    for (int level = 1; level <= totalLevels; level++) {
      if (_getDifficultyLevel(level) == difficulty) {
        levels.add(level);
      }
    }
    
    return levels;
  }

  /// Get levels by game mode
  static List<int> getLevelsByMode(GameMode mode) {
    final levels = <int>[];
    
    for (int level = 1; level <= totalLevels; level++) {
      if (_getGameMode(level) == mode) {
        levels.add(level);
      }
    }
    
    return levels;
  }
}
