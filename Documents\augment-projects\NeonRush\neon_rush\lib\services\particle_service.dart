import 'dart:math';
import 'package:flutter/material.dart';
import 'package:confetti/confetti.dart';
import 'package:flutter_animate/flutter_animate.dart';

import '../constants.dart';

/// Service for managing particle effects throughout the game
class ParticleService {
  static final ParticleService _instance = ParticleService._internal();
  factory ParticleService() => _instance;
  ParticleService._internal();

  // Confetti controllers for different effects
  final Map<String, ConfettiController> _confettiControllers = {};

  /// Initialize particle service
  void initialize() {
    // Create confetti controllers for different scenarios
    _confettiControllers['level_complete'] = ConfettiController(
      duration: const Duration(seconds: 3),
    );
    _confettiControllers['token_reward'] = ConfettiController(
      duration: const Duration(seconds: 2),
    );
    _confettiControllers['achievement'] = ConfettiController(
      duration: const Duration(seconds: 4),
    );
    _confettiControllers['spin_win'] = ConfettiController(
      duration: const Duration(seconds: 2),
    );
  }

  /// Dispose all controllers
  void dispose() {
    for (final controller in _confettiControllers.values) {
      controller.dispose();
    }
    _confettiControllers.clear();
  }

  /// Get confetti controller for a specific effect
  ConfettiController? getController(String effectName) {
    return _confettiControllers[effectName];
  }

  /// Play confetti effect
  void playConfetti(String effectName) {
    final controller = _confettiControllers[effectName];
    if (controller != null) {
      controller.play();
    }
  }

  /// Create a confetti widget for level completion
  Widget buildLevelCompleteConfetti() {
    final controller = _confettiControllers['level_complete'];
    if (controller == null) return const SizedBox.shrink();

    return Align(
      alignment: Alignment.topCenter,
      child: ConfettiWidget(
        confettiController: controller,
        blastDirection: pi / 2, // Down
        maxBlastForce: 5,
        minBlastForce: 2,
        emissionFrequency: 0.05,
        numberOfParticles: 50,
        gravity: 0.1,
        shouldLoop: false,
        colors: [
          NeonColors.primaryAccent,
          NeonColors.secondaryAccent,
          NeonColors.neonGreen,
          NeonColors.highlights,
        ],
      ),
    );
  }

  /// Create a confetti widget for token rewards
  Widget buildTokenRewardConfetti() {
    final controller = _confettiControllers['token_reward'];
    if (controller == null) return const SizedBox.shrink();

    return Align(
      alignment: Alignment.center,
      child: ConfettiWidget(
        confettiController: controller,
        blastDirection: -pi / 2, // Up
        blastDirectionality: BlastDirectionality.explosive,
        maxBlastForce: 10,
        minBlastForce: 5,
        emissionFrequency: 0.1,
        numberOfParticles: 30,
        gravity: 0.2,
        shouldLoop: false,
        colors: [
          NeonColors.primaryAccent,
          NeonColors.neonGreen,
          Colors.yellow,
          Colors.orange,
        ],
      ),
    );
  }

  /// Create a confetti widget for achievements
  Widget buildAchievementConfetti() {
    final controller = _confettiControllers['achievement'];
    if (controller == null) return const SizedBox.shrink();

    return Align(
      alignment: Alignment.topCenter,
      child: ConfettiWidget(
        confettiController: controller,
        blastDirection: pi / 2,
        blastDirectionality: BlastDirectionality.explosive,
        maxBlastForce: 15,
        minBlastForce: 8,
        emissionFrequency: 0.02,
        numberOfParticles: 100,
        gravity: 0.05,
        shouldLoop: false,
        colors: [
          NeonColors.primaryAccent,
          NeonColors.secondaryAccent,
          NeonColors.neonGreen,
          NeonColors.highlights,
          const Color(0xFFFFD700), // Gold color
          Colors.purple,
        ],
      ),
    );
  }

  /// Create a confetti widget for spin wheel wins
  Widget buildSpinWinConfetti() {
    final controller = _confettiControllers['spin_win'];
    if (controller == null) return const SizedBox.shrink();

    return Align(
      alignment: Alignment.center,
      child: ConfettiWidget(
        confettiController: controller,
        blastDirection: 0, // Right
        blastDirectionality: BlastDirectionality.explosive,
        maxBlastForce: 8,
        minBlastForce: 3,
        emissionFrequency: 0.1,
        numberOfParticles: 40,
        gravity: 0.1,
        shouldLoop: false,
        colors: [
          NeonColors.primaryAccent,
          NeonColors.secondaryAccent,
          NeonColors.neonGreen,
        ],
      ),
    );
  }

  /// Create animated glow effect widget
  Widget buildGlowEffect({
    required Widget child,
    Color glowColor = NeonColors.primaryAccent,
    double glowRadius = 10.0,
    Duration duration = const Duration(seconds: 2),
  }) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(glowRadius),
        boxShadow: [
          BoxShadow(
            color: glowColor.withOpacity(0.5),
            blurRadius: glowRadius,
            spreadRadius: 2,
          ),
        ],
      ),
      child: child
          .animate(onPlay: (controller) => controller.repeat(reverse: true))
          .shimmer(
            duration: duration,
            color: glowColor.withOpacity(0.3),
          ),
    );
  }

  /// Create pulsing effect widget
  Widget buildPulseEffect({
    required Widget child,
    double scale = 1.1,
    Duration duration = const Duration(milliseconds: 800),
  }) {
    return child
        .animate(onPlay: (controller) => controller.repeat(reverse: true))
        .scale(
          duration: duration,
          end: Offset(scale, scale),
          curve: Curves.easeInOut,
        );
  }

  /// Create floating effect widget
  Widget buildFloatingEffect({
    required Widget child,
    double offset = 10.0,
    Duration duration = const Duration(seconds: 3),
  }) {
    return child
        .animate(onPlay: (controller) => controller.repeat(reverse: true))
        .moveY(
          duration: duration,
          end: -offset,
          curve: Curves.easeInOut,
        );
  }

  /// Create sparkle effect widget
  Widget buildSparkleEffect({
    required Widget child,
    Duration duration = const Duration(milliseconds: 1500),
  }) {
    return child
        .animate(onPlay: (controller) => controller.repeat())
        .shimmer(
          duration: duration,
          color: NeonColors.primaryAccent.withOpacity(0.5),
        )
        .then()
        .shimmer(
          duration: duration,
          color: NeonColors.secondaryAccent.withOpacity(0.5),
        );
  }

  /// Create neon border effect widget
  Widget buildNeonBorderEffect({
    required Widget child,
    Color borderColor = NeonColors.primaryAccent,
    double borderWidth = 2.0,
    Duration duration = const Duration(seconds: 2),
  }) {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(
          color: borderColor,
          width: borderWidth,
        ),
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: borderColor.withOpacity(0.5),
            blurRadius: 10,
            spreadRadius: 2,
          ),
        ],
      ),
      child: child,
    )
        .animate(onPlay: (controller) => controller.repeat(reverse: true))
        .shimmer(
          duration: duration,
          color: borderColor.withOpacity(0.3),
        );
  }

  /// Create explosion effect for target hits
  Widget buildTargetHitExplosion({
    required Offset position,
    Color color = NeonColors.primaryAccent,
  }) {
    return Positioned(
      left: position.dx - 25,
      top: position.dy - 25,
      child: Container(
        width: 50,
        height: 50,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: color.withOpacity(0.3),
          boxShadow: [
            BoxShadow(
              color: color,
              blurRadius: 20,
              spreadRadius: 5,
            ),
          ],
        ),
      )
          .animate()
          .scale(
            duration: const Duration(milliseconds: 300),
            begin: const Offset(0.1, 0.1),
            end: const Offset(1.5, 1.5),
          )
          .fadeOut(
            duration: const Duration(milliseconds: 300),
          ),
    );
  }

  /// Create ripple effect
  Widget buildRippleEffect({
    required Offset position,
    Color color = NeonColors.primaryAccent,
    double maxRadius = 100.0,
  }) {
    return Positioned(
      left: position.dx - maxRadius / 2,
      top: position.dy - maxRadius / 2,
      child: Container(
        width: maxRadius,
        height: maxRadius,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          border: Border.all(
            color: color.withOpacity(0.8),
            width: 2,
          ),
        ),
      )
          .animate()
          .scale(
            duration: const Duration(milliseconds: 600),
            begin: const Offset(0.1, 0.1),
            end: const Offset(1.0, 1.0),
          )
          .fadeOut(
            duration: const Duration(milliseconds: 600),
          ),
    );
  }
}
