import 'dart:math';
import 'package:shared_preferences/shared_preferences.dart';
import '../constants.dart';
import '../models/token_transaction.dart';
import '../models/challenge.dart';
import 'token_service.dart';
import 'challenges_service.dart';

/// Service for managing the spin wheel functionality
class SpinWheelService {
  static const String _dailySpinUsedKey = 'daily_spin_used';
  static const String _lastSpinDateKey = 'last_spin_date';
  static const String _totalSpinsKey = 'total_spins';
  static const String _freeSpinsKey = 'free_spins_count';

  /// Check if daily free spin is available
  static Future<bool> isDailySpinAvailable() async {
    final prefs = await SharedPreferences.getInstance();
    final today = DateTime.now();
    final todayString = today.toIso8601String().split('T')[0]; // Get YYYY-MM-DD format
    
    final lastSpinDate = prefs.getString(_lastSpinDateKey);
    return lastSpinDate != todayString;
  }

  /// Perform a spin (free or paid)
  static Future<SpinResult> spin({required bool useFree}) async {
    if (useFree) {
      final canUseFree = await isDailySpinAvailable();
      if (!canUseFree) {
        throw Exception('Daily free spin already used');
      }
    } else {
      // Check if player has enough tokens for paid spin
      final hasTokens = await TokenService.hasEnoughTokens(GameConstants.luckyWheelSpinCost);
      if (!hasTokens) {
        throw Exception('Insufficient tokens for paid spin');
      }
    }

    // Generate spin result
    final result = _generateSpinResult(useFree);

    // Process the spin
    if (useFree) {
      await _markDailySpinUsed();
    } else {
      // Deduct tokens for paid spin
      await TokenService.spendTokens(
        amount: GameConstants.luckyWheelSpinCost,
        type: TokenTransactionType.spinWheelPurchase,
        description: 'Lucky Wheel Spin',
      );
    }

    // Award tokens if won
    if (result.tokensWon > 0) {
      await TokenService.earnTokens(
        amount: result.tokensWon,
        type: TokenTransactionType.spinWheel,
        description: useFree ? 'Free Spin Reward' : 'Paid Spin Reward',
        metadata: {
          'wasFree': useFree,
          'tokensWon': result.tokensWon,
          'bonusGameKeysWon': result.bonusGameKeysWon,
        },
      );
    }

    // Award bonus game keys if won
    if (result.bonusGameKeysWon > 0) {
      await awardBonusGameKeys(result.bonusGameKeysWon);
    }

    // Update statistics
    await _updateSpinStatistics(useFree);

    // Update challenge progress for lucky wheel usage
    ChallengesService.updateProgress(
      gameId: GameIdentifiers.luckyWheel,
      type: ChallengeType.bonusGame,
      amount: 1,
      metadata: {
        'wasFree': useFree,
        'tokensWon': result.tokensWon,
        'bonusGameKeysWon': result.bonusGameKeysWon,
        'resultType': result.type.name,
        'action': 'wheelSpin',
      },
    );

    // Update token earning challenges if tokens were won
    if (result.tokensWon > 0) {
      ChallengesService.updateProgress(
        gameId: GameIdentifiers.luckyWheel,
        type: ChallengeType.tokenEarning,
        amount: result.tokensWon,
      );
    }

    return result;
  }

  /// Generate a random spin result based on new odds system
  static SpinResult _generateSpinResult(bool wasFree) {
    final random = Random();
    final randomValue = random.nextDouble();

    // Check for bonus game key first (5% chance)
    if (randomValue < GameConstants.bonusGameKeyOdds) {
      return SpinResult(
        isWin: true,
        tokensWon: 0,
        bonusGameKeysWon: GameConstants.bonusGameKeyReward,
        label: 'Bonus Key!',
        segmentIndex: -2, // Special index for bonus key
        type: SpinResultType.bonusGameKey,
        wasFree: wasFree,
      );
    }

    // Calculate cumulative odds for token rewards
    double cumulativeOdds = GameConstants.bonusGameKeyOdds;
    for (int i = 0; i < GameConstants.luckyWheelTokenOdds.length; i++) {
      cumulativeOdds += GameConstants.luckyWheelTokenOdds[i];
      if (randomValue < cumulativeOdds) {
        return SpinResult(
          isWin: true,
          tokensWon: GameConstants.luckyWheelTokenRewards[i],
          bonusGameKeysWon: 0,
          label: GameConstants.luckyWheelTokenLabels[i],
          segmentIndex: i,
          type: SpinResultType.tokens,
          wasFree: wasFree,
        );
      }
    }

    // If no token reward hit, return "Try Again"
    return SpinResult(
      isWin: false,
      tokensWon: 0,
      bonusGameKeysWon: 0,
      label: 'Try Again',
      segmentIndex: -1,
      type: SpinResultType.tryAgain,
      wasFree: wasFree,
    );
  }

  /// Mark daily spin as used
  static Future<void> _markDailySpinUsed() async {
    final prefs = await SharedPreferences.getInstance();
    final today = DateTime.now();
    final todayString = today.toIso8601String().split('T')[0]; // Get YYYY-MM-DD format
    
    await prefs.setString(_lastSpinDateKey, todayString);
    await prefs.setBool(_dailySpinUsedKey, true);
  }

  /// Award bonus game keys to player
  static Future<void> awardBonusGameKeys(int keyCount) async {
    final prefs = await SharedPreferences.getInstance();
    final currentKeys = prefs.getInt('bonus_game_keys') ?? 0;
    await prefs.setInt('bonus_game_keys', currentKeys + keyCount);
  }

  /// Get current bonus game key count
  static Future<int> getBonusGameKeyCount() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getInt('bonus_game_keys') ?? 0;
  }

  /// Spend bonus game keys to unlock a bonus game
  static Future<bool> spendBonusGameKeys(int keyCount) async {
    final prefs = await SharedPreferences.getInstance();
    final currentKeys = prefs.getInt('bonus_game_keys') ?? 0;

    if (currentKeys >= keyCount) {
      await prefs.setInt('bonus_game_keys', currentKeys - keyCount);
      return true;
    }
    return false;
  }

  /// Update spin statistics
  static Future<void> _updateSpinStatistics(bool wasFree) async {
    final prefs = await SharedPreferences.getInstance();

    // Update total spins
    final totalSpins = prefs.getInt(_totalSpinsKey) ?? 0;
    await prefs.setInt(_totalSpinsKey, totalSpins + 1);

    // Update free spins count
    if (wasFree) {
      final freeSpins = prefs.getInt(_freeSpinsKey) ?? 0;
      await prefs.setInt(_freeSpinsKey, freeSpins + 1);
    }
  }

  /// Get spin statistics
  static Future<Map<String, dynamic>> getSpinStatistics() async {
    final prefs = await SharedPreferences.getInstance();
    
    final totalSpins = prefs.getInt(_totalSpinsKey) ?? 0;
    final freeSpins = prefs.getInt(_freeSpinsKey) ?? 0;
    final paidSpins = totalSpins - freeSpins;

    return {
      'totalSpins': totalSpins,
      'freeSpins': freeSpins,
      'paidSpins': paidSpins,
      'dailySpinAvailable': await isDailySpinAvailable(),
    };
  }

  /// Reset daily spin (for testing)
  static Future<void> resetDailySpin() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_dailySpinUsedKey);
    await prefs.remove(_lastSpinDateKey);
  }

  /// Calculate spin wheel rotation angle for animation
  static double calculateSpinAngle(int segmentIndex) {
    if (segmentIndex < 0) {
      // "Try Again" or special result - random angle
      return Random().nextDouble() * 360;
    }

    final segmentCount = GameConstants.luckyWheelTokenRewards.length;
    final segmentAngle = 360.0 / segmentCount;
    final targetAngle = segmentIndex * segmentAngle;

    // Add multiple full rotations for dramatic effect
    final fullRotations = 3 + Random().nextInt(3); // 3-5 full rotations
    return (fullRotations * 360) + targetAngle + (Random().nextDouble() * segmentAngle);
  }

  /// Get spin wheel segment colors
  static List<int> getSegmentColors() {
    return [
      0xFF00FFF3, // Cyan
      0xFFFF00E0, // Magenta
      0xFFFFD700, // Gold
      0xFF00FF80, // Green
      0xFF8000FF, // Purple
      0xFFFF8000, // Orange
      0xFFFF0080, // Hot Pink
      0xFF80FF00, // Lime
    ];
  }

  /// Get cost for paid spin
  static int getSpinCost() {
    return GameConstants.luckyWheelSpinCost;
  }

  /// Check if player can afford a paid spin
  static Future<bool> canAffordSpin() async {
    return await TokenService.hasEnoughTokens(getSpinCost());
  }
}

/// Result of a spin wheel spin
class SpinResult {
  final bool isWin;
  final int tokensWon;
  final int bonusGameKeysWon;
  final String label;
  final int segmentIndex; // -1 for "Try Again"
  final SpinResultType type;
  final bool wasFree; // Whether this was a free spin

  const SpinResult({
    required this.isWin,
    required this.tokensWon,
    required this.bonusGameKeysWon,
    required this.label,
    required this.segmentIndex,
    required this.type,
    required this.wasFree,
  });

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'isWin': isWin,
      'tokensWon': tokensWon,
      'bonusGameKeysWon': bonusGameKeysWon,
      'label': label,
      'segmentIndex': segmentIndex,
      'type': type.toString(),
      'wasFree': wasFree,
    };
  }

  /// Create from JSON
  factory SpinResult.fromJson(Map<String, dynamic> json) {
    return SpinResult(
      isWin: json['isWin'],
      tokensWon: json['tokensWon'],
      bonusGameKeysWon: json['bonusGameKeysWon'] ?? 0,
      label: json['label'],
      segmentIndex: json['segmentIndex'],
      type: SpinResultType.values.firstWhere(
        (e) => e.toString() == json['type'],
        orElse: () => SpinResultType.tokens,
      ),
      wasFree: json['wasFree'] ?? true,
    );
  }

  @override
  String toString() {
    return 'SpinResult(isWin: $isWin, tokensWon: $tokensWon, bonusGameKeysWon: $bonusGameKeysWon, label: $label, type: $type, wasFree: $wasFree)';
  }
}

/// Types of spin results
enum SpinResultType {
  tokens,
  bonusGameKey,
  tryAgain,
}

/// Spin wheel configuration
class SpinWheelConfig {
  static const double wheelSize = 300.0;
  static const double pointerSize = 40.0;
  static const Duration spinDuration = Duration(seconds: 3);
  static const Duration resultDisplayDuration = Duration(seconds: 2);

  /// Get segment labels (tokens + bonus key)
  static List<String> get segmentLabels => [
    ...GameConstants.luckyWheelTokenLabels,
    'Bonus Key!'
  ];

  /// Get segment rewards (tokens only, bonus key handled separately)
  static List<int> get segmentRewards => GameConstants.luckyWheelTokenRewards;

  /// Get segment angle in degrees
  static double get segmentAngle => 360.0 / segmentLabels.length;

  /// Get number of segments
  static int get segmentCount => segmentLabels.length;
}
