import 'dart:convert';
import 'dart:math';
import 'package:shared_preferences/shared_preferences.dart';
import '../core/constants.dart';
import '../models/token_transaction.dart';

/// Service for managing token economy and transactions
class TokenService {
  static const String _tokenBalanceKey = 'token_balance';
  static const String _transactionHistoryKey = 'transaction_history';
  static const String _dailyBonusKey = 'daily_bonus_claimed';
  static const String _lastLoginKey = 'last_login_date';
  static const String _loginStreakKey = 'login_streak';

  /// Get current token balance
  static Future<int> getTokenBalance() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getInt(_tokenBalanceKey) ?? 0;
  }

  /// Set token balance
  static Future<void> setTokenBalance(int balance) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt(_tokenBalanceKey, balance);
  }

  /// Add tokens to balance and record transaction
  static Future<void> earnTokens({
    required int amount,
    required TokenTransactionType type,
    required String description,
    Map<String, dynamic> metadata = const {},
  }) async {
    if (amount <= 0) return;

    final currentBalance = await getTokenBalance();
    final newBalance = currentBalance + amount;
    await setTokenBalance(newBalance);

    // Record transaction
    final transaction = TokenTransaction(
      id: _generateTransactionId(),
      type: type,
      amount: amount,
      timestamp: DateTime.now(),
      description: description,
      metadata: metadata,
    );

    await _recordTransaction(transaction);
  }

  /// Spend tokens from balance and record transaction
  static Future<bool> spendTokens({
    required int amount,
    required TokenTransactionType type,
    required String description,
    Map<String, dynamic> metadata = const {},
  }) async {
    if (amount <= 0) return false;

    final currentBalance = await getTokenBalance();
    if (currentBalance < amount) return false;

    final newBalance = currentBalance - amount;
    await setTokenBalance(newBalance);

    // Record transaction
    final transaction = TokenTransaction(
      id: _generateTransactionId(),
      type: type,
      amount: amount,
      timestamp: DateTime.now(),
      description: description,
      metadata: metadata,
    );

    await _recordTransaction(transaction);
    return true;
  }

  /// Check if player has enough tokens
  static Future<bool> hasEnoughTokens(int amount) async {
    final balance = await getTokenBalance();
    return balance >= amount;
  }

  /// Get transaction history
  static Future<List<TokenTransaction>> getTransactionHistory({
    int? limit,
    TokenTransactionType? filterType,
  }) async {
    final prefs = await SharedPreferences.getInstance();
    final historyJson = prefs.getString(_transactionHistoryKey);
    
    if (historyJson == null) return [];

    final List<dynamic> historyList = jsonDecode(historyJson);
    List<TokenTransaction> transactions = historyList
        .map((json) => TokenTransaction.fromJson(json))
        .toList();

    // Filter by type if specified
    if (filterType != null) {
      transactions = transactions.where((t) => t.type == filterType).toList();
    }

    // Sort by timestamp (newest first)
    transactions.sort((a, b) => b.timestamp.compareTo(a.timestamp));

    // Apply limit if specified
    if (limit != null && limit > 0) {
      transactions = transactions.take(limit).toList();
    }

    return transactions;
  }

  /// Get daily login bonus
  static Future<int> claimDailyBonus() async {
    final prefs = await SharedPreferences.getInstance();
    final today = DateTime.now();
    final todayString = today.toIso8601String().split('T')[0]; // Get YYYY-MM-DD format
    
    final lastClaimedString = prefs.getString(_dailyBonusKey);
    
    // Check if already claimed today
    if (lastClaimedString == todayString) {
      return 0; // Already claimed
    }

    // Update login streak
    final streak = await _updateLoginStreak();
    
    // Calculate bonus based on streak
    int bonusAmount = 10; // Base bonus
    if (streak >= 7) {
      bonusAmount = 25;
    } else if (streak >= 3) {
      bonusAmount = 15;
    }

    // Award tokens
    await earnTokens(
      amount: bonusAmount,
      type: TokenTransactionType.dailyBonus,
      description: 'Daily Login Bonus (Day $streak)',
      metadata: {'streak': streak},
    );

    // Mark as claimed
    await prefs.setString(_dailyBonusKey, todayString);

    return bonusAmount;
  }

  /// Check if daily bonus is available
  static Future<bool> isDailyBonusAvailable() async {
    final prefs = await SharedPreferences.getInstance();
    final today = DateTime.now();
    final todayString = today.toIso8601String().split('T')[0]; // Get YYYY-MM-DD format
    
    final lastClaimedString = prefs.getString(_dailyBonusKey);
    return lastClaimedString != todayString;
  }

  /// Get current login streak
  static Future<int> getLoginStreak() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getInt(_loginStreakKey) ?? 0;
  }

  /// Update login streak
  static Future<int> _updateLoginStreak() async {
    final prefs = await SharedPreferences.getInstance();
    final today = DateTime.now();
    final todayString = today.toIso8601String().split('T')[0]; // Get YYYY-MM-DD format

    final lastLoginString = prefs.getString(_lastLoginKey);
    final currentStreak = prefs.getInt(_loginStreakKey) ?? 0;

    int newStreak;

    if (lastLoginString == null) {
      // First login
      newStreak = 1;
    } else {
      // Check if the stored date is in old format (contains single digits)
      if (lastLoginString.contains(RegExp(r'-\d-|-\d$'))) {
        // Old format detected, reset streak and start fresh
        newStreak = 1;
      } else {
        try {
          final yesterday = today.subtract(const Duration(days: 1));
          final yesterdayString = yesterday.toIso8601String().split('T')[0]; // Get YYYY-MM-DD format

          if (lastLoginString == yesterdayString) {
            // Consecutive day
            newStreak = currentStreak + 1;
          } else if (lastLoginString == todayString) {
            // Already logged in today
            newStreak = currentStreak;
          } else {
            // Streak broken
            newStreak = 1;
          }
        } catch (e) {
          // Any parsing error, reset streak
          newStreak = 1;
        }
      }
    }

    await prefs.setString(_lastLoginKey, todayString);
    await prefs.setInt(_loginStreakKey, newStreak);

    return newStreak;
  }

  /// Record a transaction in history
  static Future<void> _recordTransaction(TokenTransaction transaction) async {
    final prefs = await SharedPreferences.getInstance();
    final historyJson = prefs.getString(_transactionHistoryKey);
    
    List<dynamic> historyList = [];
    if (historyJson != null) {
      historyList = jsonDecode(historyJson);
    }

    historyList.add(transaction.toJson());

    // Keep only last 100 transactions to prevent storage bloat
    if (historyList.length > 100) {
      historyList = historyList.sublist(historyList.length - 100);
    }

    await prefs.setString(_transactionHistoryKey, jsonEncode(historyList));
  }

  /// Generate unique transaction ID
  static String _generateTransactionId() {
    return 'txn_${DateTime.now().millisecondsSinceEpoch}_${DateTime.now().microsecond}';
  }

  /// Get token statistics
  static Future<Map<String, dynamic>> getTokenStatistics() async {
    final transactions = await getTransactionHistory();
    
    int totalEarned = 0;
    int totalSpent = 0;
    Map<TokenTransactionType, int> earningsByType = {};
    Map<TokenTransactionType, int> spendingByType = {};

    for (final transaction in transactions) {
      if (transaction.isEarning) {
        totalEarned += transaction.amount;
        earningsByType[transaction.type] = 
            (earningsByType[transaction.type] ?? 0) + transaction.amount;
      } else {
        totalSpent += transaction.amount;
        spendingByType[transaction.type] = 
            (spendingByType[transaction.type] ?? 0) + transaction.amount;
      }
    }

    return {
      'totalEarned': totalEarned,
      'totalSpent': totalSpent,
      'currentBalance': await getTokenBalance(),
      'earningsByType': earningsByType,
      'spendingByType': spendingByType,
      'transactionCount': transactions.length,
    };
  }

  /// Clear all token data (for testing/reset)
  static Future<void> clearAllData() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_tokenBalanceKey);
    await prefs.remove(_transactionHistoryKey);
    await prefs.remove(_dailyBonusKey);
    await prefs.remove(_lastLoginKey);
    await prefs.remove(_loginStreakKey);
  }

  // ===== NEW TOKEN ECONOMY METHODS (v2.0) =====

  /// Award tokens for level completion based on v2.0 documentation
  static Future<void> awardLevelCompletionTokens({
    required int level,
    required bool isBossLevel,
    int? bossScore,
    int? timeRemainingSeconds,
  }) async {
    int baseReward = GameCalculations.getTokenRewardForLevel(level);
    int bonusReward = 0;

    // Add boss score bonus if applicable
    if (isBossLevel && bossScore != null && timeRemainingSeconds != null) {
      bonusReward = GameCalculations.calculateBossScoreBonus(bossScore, timeRemainingSeconds);
    }

    final totalReward = baseReward + bonusReward;

    await earnTokens(
      amount: totalReward,
      type: TokenTransactionType.levelCompletion,
      description: isBossLevel
          ? 'Level $level Boss Completion (Base: $baseReward, Bonus: $bonusReward)'
          : 'Level $level Completion',
      metadata: {
        'level': level,
        'isBossLevel': isBossLevel,
        'baseReward': baseReward,
        'bonusReward': bonusReward,
        if (bossScore != null) 'bossScore': bossScore,
        if (timeRemainingSeconds != null) 'timeRemaining': timeRemainingSeconds,
      },
    );
  }

  /// Award mystery box tokens (after each level)
  static Future<int> awardMysteryBoxTokens() async {
    final random = Random();
    final rewards = GameConstants.mysteryBoxRewards;
    final selectedReward = rewards[random.nextInt(rewards.length)];

    await earnTokens(
      amount: selectedReward,
      type: TokenTransactionType.mysteryBox,
      description: 'Mystery Box Reward',
      metadata: {
        'possibleRewards': rewards,
        'selectedReward': selectedReward,
      },
    );

    return selectedReward;
  }

  /// Purchase power-up with tier support
  static Future<bool> purchasePowerUp({
    required String powerUpId,
    required int tier,
    required int quantity,
  }) async {
    final cost = GameCalculations.getPowerUpCost(powerUpId, tier);
    if (cost == 0) return false;

    final totalCost = cost * quantity;
    final success = await spendTokens(
      amount: totalCost,
      type: TokenTransactionType.powerUpPurchase,
      description: 'Purchased $quantity x $powerUpId (Tier $tier)',
      metadata: {
        'powerUpId': powerUpId,
        'tier': tier,
        'quantity': quantity,
        'unitCost': cost,
        'totalCost': totalCost,
      },
    );

    return success;
  }

  /// Purchase bonus game
  static Future<bool> purchaseBonusGame(String gameId) async {
    int cost;
    switch (gameId) {
      case 'neon_snake':
        cost = GameConstants.neonSnakeCost;
        break;
      case 'neon_pong':
        cost = GameConstants.neonPongCost;
        break;
      case 'astro_blaster':
        cost = GameConstants.astroBlasterCost;
        break;
      case 'glow_flow':
        cost = GameConstants.glowFlowCost;
        break;
      default:
        return false;
    }

    final success = await spendTokens(
      amount: cost,
      type: TokenTransactionType.bonusGameUnlock,
      description: 'Unlocked bonus game: $gameId',
      metadata: {
        'gameId': gameId,
        'cost': cost,
      },
    );

    return success;
  }

  /// Upgrade power-up to next tier
  static Future<bool> upgradePowerUp({
    required String powerUpId,
    required int currentTier,
  }) async {
    final nextTier = currentTier + 1;
    final upgradeCost = GameCalculations.getPowerUpCost(powerUpId, nextTier);

    if (upgradeCost == 0) return false;

    final success = await spendTokens(
      amount: upgradeCost,
      type: TokenTransactionType.powerUpUpgrade,
      description: 'Upgraded $powerUpId from Tier $currentTier to Tier $nextTier',
      metadata: {
        'powerUpId': powerUpId,
        'fromTier': currentTier,
        'toTier': nextTier,
        'upgradeCost': upgradeCost,
      },
    );

    return success;
  }
}
