import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../core/constants.dart';
import '../../models/level_config.dart';
import '../../models/neon_theme.dart';
import '../../game/game_state.dart';
import '../neon_widgets.dart';
import '../../core/sound_manager.dart';
import 'reward_box_dialog.dart';

class LevelCompletionDialog extends StatefulWidget {
  final LevelConfig level;
  final int score;
  final int timeBonus;
  final int totalXP;
  final int tokensEarned;
  final VoidCallback onReplay;
  final VoidCallback? onNextLevel;
  final VoidCallback onBackToMenu;

  const LevelCompletionDialog({
    super.key,
    required this.level,
    required this.score,
    required this.timeBonus,
    required this.totalXP,
    required this.tokensEarned,
    required this.onReplay,
    this.onNextLevel,
    required this.onBackToMenu,
  });

  @override
  State<LevelCompletionDialog> createState() => _LevelCompletionDialogState();
}

class _LevelCompletionDialogState extends State<LevelCompletionDialog>
    with TickerProviderStateMixin {
  late AnimationController _slideController;
  late AnimationController _glowController;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _glowAnimation;

  @override
  void initState() {
    super.initState();

    // Play completion sound
    SoundManager().playSfx(SoundType.reward);

    _slideController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _glowController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.elasticOut,
    ));

    _glowAnimation = Tween<double>(
      begin: 0.5,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _glowController,
      curve: Curves.easeInOut,
    ));

    _slideController.forward();
    _glowController.repeat(reverse: true);

    // Check if reward boxes should appear (every 3 levels)
    if (_shouldShowRewardBoxes()) {
      Future.delayed(const Duration(seconds: 2), () {
        if (mounted) {
          _showRewardBoxes();
        }
      });
    }
  }

  /// Check if reward boxes should appear (every 3 levels)
  bool _shouldShowRewardBoxes() {
    return widget.level.levelNumber % 3 == 0;
  }

  /// Show the reward boxes popup
  void _showRewardBoxes() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => RewardBoxDialog(
        levelNumber: widget.level.levelNumber,
        tokensEarned: widget.tokensEarned,
        onComplete: () {
          Navigator.of(context).pop(); // Close reward box dialog
          // The main completion dialog remains open
        },
      ),
    );
  }

  @override
  void dispose() {
    _slideController.dispose();
    _glowController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      child: SlideTransition(
        position: _slideAnimation,
        child: AnimatedBuilder(
          animation: _glowAnimation,
          builder: (context, child) {
            return ConstrainedBox(
              constraints: BoxConstraints(
                maxWidth: MediaQuery.of(context).size.width * 0.9,
                maxHeight: MediaQuery.of(context).size.height * 0.8,
              ),
              child: NeonContainer.card(
                glowColor: widget.level.theme.primary,
                glowIntensity: _glowAnimation.value * 0.5,
                child: Padding(
                  padding: const EdgeInsets.all(20.0),
                  child: SingleChildScrollView(
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        // Level Complete Title
                        NeonText.title(
                          'LEVEL ${widget.level.levelNumber} COMPLETE!',
                          glowColor: widget.level.theme.primary,
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),

                        const SizedBox(height: 15),

                        // Level Name
                        NeonText.body(
                          widget.level.title,
                          glowColor: widget.level.theme.secondary,
                          fontSize: 16,
                        ),

                        const SizedBox(height: 20),

                        // Score Section
                        _buildScoreSection(),

                        const SizedBox(height: 20),

                        // Rewards Section
                        _buildRewardsSection(),

                        const SizedBox(height: 20),

                        // Action Buttons
                        _buildActionButtons(),
                      ],
                    ),
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildScoreSection() {
    return NeonContainer.card(
      glowColor: widget.level.theme.secondary,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            NeonText.body(
              'FINAL SCORE',
              glowColor: widget.level.theme.primary,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
            const SizedBox(height: 10),
            NeonText.title(
              '${widget.score}',
              glowColor: widget.level.theme.primary,
              fontSize: 32,
              fontWeight: FontWeight.bold,
            ),
            if (widget.timeBonus > 0) ...[
              const SizedBox(height: 8),
              NeonText.body(
                'Time Bonus: +${widget.timeBonus}',
                glowColor: widget.level.theme.secondary,
                fontSize: 14,
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildRewardsSection() {
    return NeonContainer.card(
      glowColor: widget.level.theme.accent,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            NeonText.body(
              'REWARDS EARNED',
              glowColor: widget.level.theme.accent,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
            const SizedBox(height: 15),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                _buildRewardItem(
                  'XP',
                  '${widget.totalXP}',
                  widget.level.theme.primary,
                ),
                _buildRewardItem(
                  'TOKENS',
                  '${widget.tokensEarned}',
                  widget.level.theme.secondary,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRewardItem(String label, String value, Color color) {
    return Column(
      children: [
        NeonText.body(
          label,
          glowColor: color,
          fontSize: 12,
          fontWeight: FontWeight.bold,
        ),
        const SizedBox(height: 5),
        NeonText.title(
          value,
          glowColor: color,
          fontSize: 20,
          fontWeight: FontWeight.bold,
        ),
      ],
    );
  }

  Widget _buildActionButtons() {
    return Column(
      children: [
        // Next Level Button (if available)
        if (widget.onNextLevel != null) ...[
          SizedBox(
            width: double.infinity,
            height: 50,
            child: NeonButton.primary(
              text: 'NEXT LEVEL',
              glowColor: widget.level.theme.primary,
              onPressed: () async {
                await SoundManager().playSfx(SoundType.buttonClick);
                if (mounted) Navigator.of(context).pop();
                widget.onNextLevel!();
              },
            ),
          ),
          const SizedBox(height: 15),
        ],
        
        // Replay Button
        SizedBox(
          width: double.infinity,
          height: 50,
          child: NeonButton.secondary(
            text: 'REPLAY LEVEL',
            glowColor: widget.level.theme.secondary,
            onPressed: () async {
              await SoundManager().playSfx(SoundType.buttonClick);
              if (mounted) Navigator.of(context).pop();
              widget.onReplay();
            },
          ),
        ),
        
        const SizedBox(height: 15),
        
        // Back to Menu Button
        SizedBox(
          width: double.infinity,
          height: 50,
          child: NeonButton.secondary(
            text: 'BACK TO MENU',
            glowColor: widget.level.theme.accent,
            onPressed: () async {
              await SoundManager().playSfx(SoundType.buttonClick);
              if (mounted) Navigator.of(context).pop();
              widget.onBackToMenu();
            },
          ),
        ),
      ],
    );
  }
}
