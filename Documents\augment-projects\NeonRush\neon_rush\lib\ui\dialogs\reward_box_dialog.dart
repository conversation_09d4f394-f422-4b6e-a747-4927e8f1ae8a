import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../core/sound_manager.dart';
import '../../core/constants.dart';
import '../../constants.dart';
import '../../models/crate.dart';

/// Reward Box Dialog that appears every 3 levels with countdown timer and reveal all feature
class RewardBoxDialog extends StatefulWidget {
  final int levelNumber;
  final int tokensEarned;
  final VoidCallback onComplete;
  
  const RewardBoxDialog({
    super.key,
    required this.levelNumber,
    required this.tokensEarned,
    required this.onComplete,
  });

  @override
  State<RewardBoxDialog> createState() => _RewardBoxDialogState();
}

class _RewardBoxDialogState extends State<RewardBoxDialog>
    with TickerProviderStateMixin {
  List<RewardBoxData> _boxes = [];
  int _countdown = 5;
  Timer? _countdownTimer;
  bool _canReveal = false;
  bool _allRevealed = false;
  
  late AnimationController _backgroundController;
  late AnimationController _pulseController;
  late Animation<double> _backgroundAnimation;
  late Animation<double> _pulseAnimation;
  
  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _initializeBoxes();
    _startCountdown();
  }
  
  void _initializeAnimations() {
    _backgroundController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    );

    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _backgroundAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(_backgroundController);

    _pulseAnimation = Tween<double>(
      begin: 0.8,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));

    _backgroundController.repeat();
    _pulseController.repeat(reverse: true);
  }
  
  void _initializeBoxes() {
    // Create 6 reward boxes with different crate types
    _boxes = List.generate(6, (index) {
      CrateType crateType;
      if (index < 3) {
        crateType = CrateType.basic;
      } else if (index < 5) {
        crateType = CrateType.advanced;
      } else {
        crateType = CrateType.epic;
      }
      
      return RewardBoxData(
        id: index,
        position: Offset(
          (index % 3) * 120.0 + 60,
          (index ~/ 3) * 120.0 + 150,
        ),
        crateType: crateType,
        isRevealed: false,
        crate: _generateCrateForType(crateType),
        color: _getColorForCrateType(crateType),
      );
    });
  }
  
  Crate _generateCrateForType(CrateType type) {
    switch (type) {
      case CrateType.basic:
        return Crates.basicCrate;
      case CrateType.advanced:
        return Crates.advancedCrate;
      case CrateType.epic:
        return Crates.epicCrate;
    }
  }

  Color _getColorForCrateType(CrateType type) {
    switch (type) {
      case CrateType.basic:
        return NeonColors.neonGreen;
      case CrateType.advanced:
        return NeonColors.primaryAccent;
      case CrateType.epic:
        return NeonColors.highlights;
    }
  }
  
  void _startCountdown() {
    _countdownTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        _countdown--;
      });
      
      if (_countdown <= 0) {
        timer.cancel();
        setState(() {
          _canReveal = true;
        });
        SoundManager().playSfx(SoundType.reward);
      }
    });
  }
  
  void _revealBox(RewardBoxData box) async {
    if (!_canReveal || box.isRevealed || _allRevealed) return;

    await SoundManager().playSfx(SoundType.buttonClick);

    setState(() {
      box.isRevealed = true;
    });

    // Open the crate and show rewards
    final rewards = box.crate.open();
    _showRewardPopup(rewards);
  }
  
  void _revealAllBoxes() async {
    if (!_canReveal || _allRevealed) return;

    await SoundManager().playSfx(SoundType.reward);

    setState(() {
      _allRevealed = true;
      for (var box in _boxes) {
        box.isRevealed = true;
      }
    });

    // Show all rewards
    List<CrateReward> allRewards = [];
    for (var box in _boxes) {
      allRewards.addAll(box.crate.open());
    }
    _showAllRewardsPopup(allRewards);
  }
  
  void _showRewardPopup(List<CrateReward> rewards) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        backgroundColor: NeonColors.surface,
        title: Text(
          'Reward Box Opened!',
          style: GoogleFonts.orbitron(
            color: NeonColors.primaryAccent,
            fontWeight: FontWeight.bold,
            fontSize: 20,
          ),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: rewards.map((reward) => Padding(
            padding: const EdgeInsets.symmetric(vertical: 4),
            child: Text(
              '${reward.type.name}: ${reward.value}',
              style: GoogleFonts.orbitron(
                color: NeonColors.textPrimary,
                fontSize: 16,
              ),
            ),
          )).toList(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(
              'Awesome!',
              style: GoogleFonts.orbitron(color: NeonColors.primaryAccent),
            ),
          ),
        ],
      ),
    );
  }
  
  void _showAllRewardsPopup(List<CrateReward> allRewards) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        backgroundColor: NeonColors.surface,
        title: Text(
          'All Boxes Revealed!',
          style: GoogleFonts.orbitron(
            color: NeonColors.highlights,
            fontWeight: FontWeight.bold,
            fontSize: 22,
          ),
        ),
        content: SizedBox(
          width: double.maxFinite,
          height: 300,
          child: ListView.builder(
            itemCount: allRewards.length,
            itemBuilder: (context, index) {
              final reward = allRewards[index];
              return Padding(
                padding: const EdgeInsets.symmetric(vertical: 4),
                child: Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: NeonColors.primaryAccent.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: NeonColors.primaryAccent.withValues(alpha: 0.3),
                    ),
                  ),
                  child: Text(
                    '${reward.type.name}: ${reward.value}',
                    style: GoogleFonts.orbitron(
                      color: NeonColors.textPrimary,
                      fontSize: 16,
                    ),
                  ),
                ),
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop(); // Close rewards popup
              widget.onComplete(); // Close main dialog
            },
            child: Text(
              'Collect All!',
              style: GoogleFonts.orbitron(color: NeonColors.highlights),
            ),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _countdownTimer?.cancel();
    _backgroundController.dispose();
    _pulseController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      child: AnimatedBuilder(
        animation: _backgroundAnimation,
        builder: (context, child) {
          return ConstrainedBox(
            constraints: BoxConstraints(
              maxWidth: MediaQuery.of(context).size.width * 0.9,
              maxHeight: MediaQuery.of(context).size.height * 0.8,
            ),
            child: Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: NeonColors.surface,
                borderRadius: BorderRadius.circular(20),
                border: Border.all(
                  color: NeonColors.primaryAccent.withValues(alpha: 0.5),
                  width: 2,
                ),
                boxShadow: [
                  BoxShadow(
                    color: NeonColors.primaryAccent.withValues(alpha: 0.3),
                    blurRadius: 20,
                    spreadRadius: 5,
                  ),
                ],
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Title
                  Text(
                    'REWARD BOXES',
                    style: GoogleFonts.orbitron(
                      color: NeonColors.primaryAccent,
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      letterSpacing: 2,
                    ),
                  ).animate()
                    .fadeIn(duration: const Duration(milliseconds: 500))
                    .scale(begin: const Offset(0.8, 0.8)),
                  
                  const SizedBox(height: 10),
                  
                  // Level indicator
                  Text(
                    'Level ${widget.levelNumber} Complete!',
                    style: GoogleFonts.orbitron(
                      color: NeonColors.textSecondary,
                      fontSize: 16,
                    ),
                  ),
                  
                  const SizedBox(height: 20),
                  
                  // Countdown or instruction
                  if (!_canReveal) ...[
                    AnimatedBuilder(
                      animation: _pulseAnimation,
                      builder: (context, child) {
                        return Transform.scale(
                          scale: _pulseAnimation.value,
                          child: Text(
                            'Boxes unlock in: $_countdown',
                            style: GoogleFonts.orbitron(
                              color: NeonColors.highlights,
                              fontSize: 20,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        );
                      },
                    ),
                  ] else ...[
                    Text(
                      'Tap boxes to reveal rewards!',
                      style: GoogleFonts.orbitron(
                        color: NeonColors.neonGreen,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                  
                  const SizedBox(height: 30),
                  
                  // Reward boxes grid
                  _buildRewardBoxesGrid(),
                  
                  const SizedBox(height: 30),
                  
                  // Reveal all button
                  if (_canReveal && !_allRevealed) ...[
                    SizedBox(
                      width: double.infinity,
                      height: 50,
                      child: ElevatedButton(
                        onPressed: _revealAllBoxes,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: NeonColors.highlights.withValues(alpha: 0.2),
                          side: BorderSide(color: NeonColors.highlights),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        child: Text(
                          'REVEAL ALL BOXES',
                          style: GoogleFonts.orbitron(
                            color: NeonColors.highlights,
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ).animate()
                      .fadeIn(delay: const Duration(milliseconds: 500))
                      .slideY(begin: 0.3, end: 0),
                  ],
                  
                  // Close button (only if all revealed or countdown finished)
                  if (_allRevealed || (_canReveal && _boxes.every((box) => box.isRevealed))) ...[
                    const SizedBox(height: 15),
                    SizedBox(
                      width: double.infinity,
                      height: 50,
                      child: ElevatedButton(
                        onPressed: widget.onComplete,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: NeonColors.primaryAccent.withValues(alpha: 0.2),
                          side: BorderSide(color: NeonColors.primaryAccent),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        child: Text(
                          'CONTINUE',
                          style: GoogleFonts.orbitron(
                            color: NeonColors.primaryAccent,
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                  ],
                ],
              ),
            ),
          );
        },
      ),
    );
  }
  
  Widget _buildRewardBoxesGrid() {
    return SizedBox(
      height: 280,
      child: GridView.builder(
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 3,
          crossAxisSpacing: 15,
          mainAxisSpacing: 15,
          childAspectRatio: 1.0,
        ),
        itemCount: _boxes.length,
        itemBuilder: (context, index) {
          final box = _boxes[index];
          return _buildRewardBox(box);
        },
      ),
    );
  }
  
  Widget _buildRewardBox(RewardBoxData box) {
    return GestureDetector(
      onTap: () => _revealBox(box),
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        decoration: BoxDecoration(
          color: box.isRevealed 
            ? box.color.withValues(alpha: 0.3)
            : NeonColors.surface,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: _canReveal 
              ? box.color
              : NeonColors.textSecondary.withValues(alpha: 0.3),
            width: 2,
          ),
          boxShadow: _canReveal ? [
            BoxShadow(
              color: box.color.withValues(alpha: 0.3),
              blurRadius: 10,
              spreadRadius: 2,
            ),
          ] : null,
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              box.isRevealed ? Icons.card_giftcard : Icons.lock,
              color: _canReveal ? box.color : NeonColors.textSecondary,
              size: 40,
            ),
            const SizedBox(height: 8),
            Text(
              box.crateType.name.toUpperCase(),
              style: GoogleFonts.orbitron(
                color: _canReveal ? box.color : NeonColors.textSecondary,
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    ).animate(delay: Duration(milliseconds: box.id * 100))
      .fadeIn(duration: const Duration(milliseconds: 500))
      .scale(begin: const Offset(0.8, 0.8));
  }
}

/// Data class for reward boxes
class RewardBoxData {
  final int id;
  final Offset position;
  final CrateType crateType;
  bool isRevealed;
  final Crate crate;
  final Color color;

  RewardBoxData({
    required this.id,
    required this.position,
    required this.crateType,
    required this.isRevealed,
    required this.crate,
    required this.color,
  });
}
