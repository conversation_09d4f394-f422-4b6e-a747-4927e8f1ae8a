import 'dart:math';
import 'package:flutter/material.dart';
import '../models/neon_theme.dart';

/// Dynamic animated gradient background with neon effects
class DynamicNeonBackground extends StatefulWidget {
  final Widget child;
  final NeonTheme theme;
  final double intensity;
  final Duration animationDuration;
  final bool enableParallax;

  const DynamicNeonBackground({
    super.key,
    required this.child,
    required this.theme,
    this.intensity = 1.0,
    this.animationDuration = const Duration(seconds: 8),
    this.enableParallax = true,
  });

  @override
  State<DynamicNeonBackground> createState() => _DynamicNeonBackgroundState();
}

class _DynamicNeonBackgroundState extends State<DynamicNeonBackground>
    with TickerProviderStateMixin {
  late AnimationController _gradientController;
  late AnimationController _parallaxController;
  late Animation<double> _gradientAnimation;
  late Animation<double> _parallaxAnimation;

  @override
  void initState() {
    super.initState();
    
    // Gradient animation for color shifting
    _gradientController = AnimationController(
      duration: widget.animationDuration,
      vsync: this,
    );
    _gradientAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _gradientController,
      curve: Curves.easeInOut,
    ));

    // Parallax animation for depth effect
    _parallaxController = AnimationController(
      duration: const Duration(seconds: 12),
      vsync: this,
    );
    _parallaxAnimation = Tween<double>(
      begin: 0.0,
      end: 2 * pi,
    ).animate(CurvedAnimation(
      parent: _parallaxController,
      curve: Curves.linear,
    ));

    // Start animations
    _gradientController.repeat(reverse: true);
    if (widget.enableParallax) {
      _parallaxController.repeat();
    }
  }

  @override
  void dispose() {
    _gradientController.dispose();
    _parallaxController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: Listenable.merge([_gradientAnimation, _parallaxAnimation]),
      builder: (context, child) {
        return Container(
          decoration: BoxDecoration(
            gradient: _buildDynamicGradient(),
          ),
          child: Stack(
            children: [
              // Parallax layers
              if (widget.enableParallax) ..._buildParallaxLayers(),
              
              // Noise texture overlay
              _buildNoiseOverlay(),
              
              // Main content
              widget.child,
            ],
          ),
        );
      },
    );
  }

  Gradient _buildDynamicGradient() {
    final progress = _gradientAnimation.value;
    final theme = widget.theme;
    
    // Create shifting colors based on animation progress
    final color1 = Color.lerp(
      Colors.black,
      theme.primary.withValues(alpha: 0.1 * widget.intensity),
      0.3 + 0.2 * sin(progress * 2 * pi),
    )!;
    
    final color2 = Color.lerp(
      Colors.black,
      theme.secondary.withValues(alpha: 0.08 * widget.intensity),
      0.2 + 0.15 * cos(progress * 2 * pi + pi / 3),
    )!;
    
    final color3 = Color.lerp(
      Colors.black,
      theme.accent.withValues(alpha: 0.06 * widget.intensity),
      0.1 + 0.1 * sin(progress * 2 * pi + 2 * pi / 3),
    )!;

    return RadialGradient(
      center: Alignment(
        0.3 * sin(progress * 2 * pi),
        0.2 * cos(progress * 2 * pi),
      ),
      radius: 1.5 + 0.5 * sin(progress * pi),
      colors: [color1, color2, color3, Colors.black],
      stops: const [0.0, 0.3, 0.7, 1.0],
    );
  }

  List<Widget> _buildParallaxLayers() {
    final parallaxValue = _parallaxAnimation.value;
    
    return [
      // Background layer - slowest movement
      Positioned.fill(
        child: Transform.translate(
          offset: Offset(
            20 * sin(parallaxValue * 0.3),
            15 * cos(parallaxValue * 0.2),
          ),
          child: SizedBox.expand(
            child: CustomPaint(
              painter: ParallaxShapesPainter(
                color: widget.theme.primary.withValues(alpha: 0.03),
                animationValue: parallaxValue * 0.3,
                shapeCount: 3,
              ),
            ),
          ),
        ),
      ),
      
      // Middle layer - medium movement
      Positioned.fill(
        child: Transform.translate(
          offset: Offset(
            30 * sin(parallaxValue * 0.5),
            25 * cos(parallaxValue * 0.4),
          ),
          child: SizedBox.expand(
            child: CustomPaint(
              painter: ParallaxShapesPainter(
                color: widget.theme.secondary.withValues(alpha: 0.05),
                animationValue: parallaxValue * 0.5,
                shapeCount: 5,
              ),
            ),
          ),
        ),
      ),
      
      // Foreground layer - fastest movement
      Positioned.fill(
        child: Transform.translate(
          offset: Offset(
            40 * sin(parallaxValue * 0.8),
            35 * cos(parallaxValue * 0.7),
          ),
          child: SizedBox.expand(
            child: CustomPaint(
              painter: ParallaxShapesPainter(
                color: widget.theme.accent.withValues(alpha: 0.02),
                animationValue: parallaxValue * 0.8,
                shapeCount: 7,
              ),
            ),
          ),
        ),
      ),
    ];
  }

  Widget _buildNoiseOverlay() {
    return Positioned.fill(
      child: CustomPaint(
        painter: NoiseTexturePainter(
          intensity: 0.02 * widget.intensity,
          animationValue: _gradientAnimation.value,
        ),
      ),
    );
  }
}

/// Custom painter for parallax geometric shapes
class ParallaxShapesPainter extends CustomPainter {
  final Color color;
  final double animationValue;
  final int shapeCount;

  ParallaxShapesPainter({
    required this.color,
    required this.animationValue,
    required this.shapeCount,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    final random = Random(42); // Fixed seed for consistent shapes
    
    for (int i = 0; i < shapeCount; i++) {
      final centerX = random.nextDouble() * size.width;
      final centerY = random.nextDouble() * size.height;
      final radius = 20 + random.nextDouble() * 40;
      
      // Animated position based on animation value
      final animatedX = centerX + 50 * sin(animationValue + i * 0.5);
      final animatedY = centerY + 30 * cos(animationValue + i * 0.3);
      
      // Draw geometric shapes (circles, triangles, hexagons)
      switch (i % 3) {
        case 0:
          _drawCircle(canvas, paint, animatedX, animatedY, radius);
          break;
        case 1:
          _drawTriangle(canvas, paint, animatedX, animatedY, radius);
          break;
        case 2:
          _drawHexagon(canvas, paint, animatedX, animatedY, radius);
          break;
      }
    }
  }

  void _drawCircle(Canvas canvas, Paint paint, double x, double y, double radius) {
    canvas.drawCircle(Offset(x, y), radius, paint);
  }

  void _drawTriangle(Canvas canvas, Paint paint, double x, double y, double radius) {
    final path = Path();
    path.moveTo(x, y - radius);
    path.lineTo(x - radius * 0.866, y + radius * 0.5);
    path.lineTo(x + radius * 0.866, y + radius * 0.5);
    path.close();
    canvas.drawPath(path, paint);
  }

  void _drawHexagon(Canvas canvas, Paint paint, double x, double y, double radius) {
    final path = Path();
    for (int i = 0; i < 6; i++) {
      final angle = i * pi / 3;
      final pointX = x + radius * cos(angle);
      final pointY = y + radius * sin(angle);
      
      if (i == 0) {
        path.moveTo(pointX, pointY);
      } else {
        path.lineTo(pointX, pointY);
      }
    }
    path.close();
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

/// Custom painter for subtle noise texture
class NoiseTexturePainter extends CustomPainter {
  final double intensity;
  final double animationValue;

  NoiseTexturePainter({
    required this.intensity,
    required this.animationValue,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..style = PaintingStyle.fill;

    final random = Random((animationValue * 1000).round());
    
    // Draw random noise points
    for (int i = 0; i < (size.width * size.height * intensity / 100).round(); i++) {
      final x = random.nextDouble() * size.width;
      final y = random.nextDouble() * size.height;
      final alpha = random.nextDouble() * 0.1;
      
      paint.color = Colors.white.withValues(alpha: alpha);
      canvas.drawCircle(Offset(x, y), 0.5, paint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
