import 'dart:math';
import 'package:flutter/material.dart';
import '../game/enemy_system.dart';
import '../ui/neon_effects.dart';
import '../ui/neon_text.dart';

/// Widget for rendering enemies in the game
class EnemyRenderer extends StatelessWidget {
  final List<Enemy> enemies;
  final Size screenSize;
  final Function(Enemy)? onEnemyTapped;

  const EnemyRenderer({
    super.key,
    required this.enemies,
    required this.screenSize,
    this.onEnemyTapped,
  });

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: enemies.map((enemy) => _buildEnemy(enemy)).toList(),
    );
  }

  Widget _buildEnemy(Enemy enemy) {
    final screenX = enemy.position.dx * screenSize.width;
    final screenY = enemy.position.dy * screenSize.height;

    return Positioned(
      left: screenX - enemy.size / 2,
      top: screenY - enemy.size / 2,
      child: GestureDetector(
        onTap: onEnemyTapped != null ? () => onEnemyTapped!(enemy) : null,
        child: _buildEnemyWidget(enemy),
      ),
    );
  }

  Widget _buildEnemyWidget(Enemy enemy) {
    switch (enemy.type) {
      case EnemyType.basic:
        return _buildBasicEnemy(enemy);
      case EnemyType.spike:
        return _buildSpikeEnemy(enemy);
      case EnemyType.chaser:
        return _buildChaserEnemy(enemy);
      case EnemyType.bouncer:
        return _buildBouncerEnemy(enemy);
      case EnemyType.exploder:
        return _buildExploderEnemy(enemy);
      case EnemyType.boss:
        return _buildBossEnemy(enemy);
    }
  }

  Widget _buildBasicEnemy(Enemy enemy) {
    return Container(
      width: enemy.size,
      height: enemy.size,
      decoration: BoxDecoration(
        color: enemy.color.withValues(alpha: 0.3),
        shape: BoxShape.circle,
        border: Border.all(
          color: enemy.color,
          width: 2,
        ),
        boxShadow: NeonEffects.createGlow(
          color: enemy.color,
          intensity: 0.8,
        ),
      ),
      child: Center(
        child: Icon(
          Icons.circle,
          color: enemy.color,
          size: enemy.size * 0.6,
        ),
      ),
    );
  }

  Widget _buildSpikeEnemy(Enemy enemy) {
    return Transform.rotate(
      angle: enemy.rotation,
      child: Container(
        width: enemy.size,
        height: enemy.size,
        decoration: BoxDecoration(
          color: enemy.color.withValues(alpha: 0.3),
          border: Border.all(
            color: enemy.color,
            width: 3,
          ),
          boxShadow: NeonEffects.createGlow(
            color: enemy.color,
            intensity: 1.0,
          ),
        ),
        child: CustomPaint(
          painter: SpikePainter(enemy.color),
          size: Size(enemy.size, enemy.size),
        ),
      ),
    );
  }

  Widget _buildChaserEnemy(Enemy enemy) {
    return Container(
      width: enemy.size,
      height: enemy.size,
      decoration: BoxDecoration(
        color: enemy.color.withValues(alpha: 0.4),
        shape: BoxShape.circle,
        border: Border.all(
          color: enemy.color,
          width: 2,
        ),
        boxShadow: NeonEffects.createGlow(
          color: enemy.color,
          intensity: 0.9,
        ),
      ),
      child: Center(
        child: Icon(
          Icons.my_location,
          color: enemy.color,
          size: enemy.size * 0.7,
        ),
      ),
    );
  }

  Widget _buildBouncerEnemy(Enemy enemy) {
    return Container(
      width: enemy.size,
      height: enemy.size,
      decoration: BoxDecoration(
        color: enemy.color.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: enemy.color,
          width: 2,
        ),
        boxShadow: NeonEffects.createGlow(
          color: enemy.color,
          intensity: 0.8,
        ),
      ),
      child: Center(
        child: Icon(
          Icons.sports_basketball,
          color: enemy.color,
          size: enemy.size * 0.6,
        ),
      ),
    );
  }

  Widget _buildExploderEnemy(Enemy enemy) {
    return Container(
      width: enemy.size,
      height: enemy.size,
      decoration: BoxDecoration(
        color: enemy.color.withValues(alpha: 0.4),
        shape: BoxShape.circle,
        border: Border.all(
          color: enemy.color,
          width: 3,
        ),
        boxShadow: NeonEffects.createGlow(
          color: enemy.color,
          intensity: 1.2,
        ),
      ),
      child: Center(
        child: Icon(
          Icons.warning,
          color: enemy.color,
          size: enemy.size * 0.7,
        ),
      ),
    );
  }

  Widget _buildBossEnemy(Enemy enemy) {
    return Container(
      width: enemy.size,
      height: enemy.size,
      decoration: BoxDecoration(
        color: enemy.color.withValues(alpha: 0.2),
        shape: BoxShape.circle,
        border: Border.all(
          color: enemy.color,
          width: 4,
        ),
        boxShadow: NeonEffects.createGlow(
          color: enemy.color,
          intensity: 1.5,
        ),
      ),
      child: Stack(
        children: [
          Center(
            child: Icon(
              Icons.shield,
              color: enemy.color,
              size: enemy.size * 0.6,
            ),
          ),
          if (enemy.health > 1)
            Positioned(
              top: 5,
              right: 5,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
                decoration: BoxDecoration(
                  color: enemy.color.withValues(alpha: 0.8),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: NeonText.body(
                  '${enemy.health}',
                  glowColor: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
        ],
      ),
    );
  }
}

/// Custom painter for spike enemies
class SpikePainter extends CustomPainter {
  final Color color;

  SpikePainter(this.color);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width / 2;

    // Draw spikes around the circle
    const spikeCount = 8;
    for (int i = 0; i < spikeCount; i++) {
      final angle = (i * 2 * pi) / spikeCount;
      final spikeStart = Offset(
        center.dx + cos(angle) * radius * 0.6,
        center.dy + sin(angle) * radius * 0.6,
      );
      final spikeEnd = Offset(
        center.dx + cos(angle) * radius * 1.2,
        center.dy + sin(angle) * radius * 1.2,
      );

      // Draw spike as a triangle
      final path = Path();
      path.moveTo(spikeStart.dx, spikeStart.dy);
      path.lineTo(spikeEnd.dx, spikeEnd.dy);
      
      // Add width to the spike
      final perpAngle = angle + pi / 2;
      final spikeWidth = radius * 0.1;
      final side1 = Offset(
        spikeStart.dx + cos(perpAngle) * spikeWidth,
        spikeStart.dy + sin(perpAngle) * spikeWidth,
      );
      final side2 = Offset(
        spikeStart.dx - cos(perpAngle) * spikeWidth,
        spikeStart.dy - sin(perpAngle) * spikeWidth,
      );
      
      path.lineTo(side1.dx, side1.dy);
      path.lineTo(side2.dx, side2.dy);
      path.close();

      canvas.drawPath(path, paint);
    }

    // Draw center circle
    canvas.drawCircle(center, radius * 0.4, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

/// Enemy health bar widget
class EnemyHealthBar extends StatelessWidget {
  final Enemy enemy;
  final int maxHealth;

  const EnemyHealthBar({
    super.key,
    required this.enemy,
    required this.maxHealth,
  });

  @override
  Widget build(BuildContext context) {
    final healthPercent = enemy.health / maxHealth;
    
    return Container(
      width: enemy.size,
      height: 6,
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.5),
        borderRadius: BorderRadius.circular(3),
        border: Border.all(color: Colors.white, width: 1),
      ),
      child: FractionallySizedBox(
        alignment: Alignment.centerLeft,
        widthFactor: healthPercent,
        child: Container(
          decoration: BoxDecoration(
            color: healthPercent > 0.5 ? Colors.green : 
                   healthPercent > 0.25 ? Colors.orange : Colors.red,
            borderRadius: BorderRadius.circular(2),
            boxShadow: [
              BoxShadow(
                color: (healthPercent > 0.5 ? Colors.green : 
                       healthPercent > 0.25 ? Colors.orange : Colors.red)
                    .withValues(alpha: 0.5),
                blurRadius: 4,
                spreadRadius: 1,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
