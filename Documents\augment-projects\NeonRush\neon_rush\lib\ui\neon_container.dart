import 'package:flutter/material.dart';
import 'neon_effects.dart';

/// A container widget with neon glow effects and customizable styling
class NeonContainer extends StatelessWidget {
  final Widget? child;
  final double? width;
  final double? height;
  final Color glowColor;
  final Color? backgroundColor;
  final double borderRadius;
  final double borderWidth;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final bool animate;
  final double glowIntensity;
  final Gradient? gradient;
  final List<BoxShadow>? customShadows;
  final AlignmentGeometry? alignment;

  const NeonContainer({
    super.key,
    this.child,
    this.width,
    this.height,
    required this.glowColor,
    this.backgroundColor,
    this.borderRadius = 12.0,
    this.borderWidth = 2.0,
    this.padding,
    this.margin,
    this.animate = false,
    this.glowIntensity = 1.0,
    this.gradient,
    this.customShadows,
    this.alignment,
  });

  /// Creates a card-style container with neon glow
  const NeonContainer.card({
    super.key,
    this.child,
    this.width,
    this.height,
    required this.glowColor,
    this.backgroundColor,
    this.animate = false,
    this.glowIntensity = 0.8,
    this.gradient,
    this.customShadows,
    this.alignment,
  })  : borderRadius = 16.0,
        borderWidth = 1.5,
        padding = const EdgeInsets.all(16.0),
        margin = const EdgeInsets.all(8.0);

  /// Creates a panel-style container with strong neon glow
  const NeonContainer.panel({
    super.key,
    this.child,
    this.width,
    this.height,
    required this.glowColor,
    this.backgroundColor,
    this.animate = true,
    this.glowIntensity = 1.2,
    this.gradient,
    this.customShadows,
    this.alignment,
  })  : borderRadius = 20.0,
        borderWidth = 3.0,
        padding = const EdgeInsets.all(20.0),
        margin = const EdgeInsets.all(12.0);

  /// Creates a badge-style container with subtle neon glow
  const NeonContainer.badge({
    super.key,
    this.child,
    this.width,
    this.height,
    required this.glowColor,
    this.backgroundColor,
    this.animate = false,
    this.glowIntensity = 0.6,
    this.gradient,
    this.customShadows,
    this.alignment,
  })  : borderRadius = 8.0,
        borderWidth = 1.0,
        padding = const EdgeInsets.symmetric(horizontal: 12.0, vertical: 6.0),
        margin = const EdgeInsets.all(4.0);

  @override
  Widget build(BuildContext context) {
    final effectiveBackgroundColor = backgroundColor ?? 
        glowColor.withOpacity(0.05);

    final decoration = BoxDecoration(
      color: gradient == null ? effectiveBackgroundColor : null,
      gradient: gradient,
      borderRadius: BorderRadius.circular(borderRadius),
      border: Border.all(
        color: glowColor.withOpacity(0.8),
        width: borderWidth,
      ),
      boxShadow: customShadows ?? NeonEffects.createGlow(
        color: glowColor,
        intensity: glowIntensity,
      ),
    );

    Widget container = Container(
      width: width,
      height: height,
      margin: margin,
      padding: padding,
      alignment: alignment,
      decoration: decoration,
      child: child,
    );

    if (animate) {
      container = container.withPulse();
    }

    return container;
  }
}

/// A container with animated neon border that flows around the edges
class NeonBorderContainer extends StatefulWidget {
  final Widget? child;
  final double? width;
  final double? height;
  final Color glowColor;
  final Color? backgroundColor;
  final double borderRadius;
  final double borderWidth;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final Duration animationDuration;
  final double glowIntensity;

  const NeonBorderContainer({
    super.key,
    this.child,
    this.width,
    this.height,
    required this.glowColor,
    this.backgroundColor,
    this.borderRadius = 12.0,
    this.borderWidth = 2.0,
    this.padding,
    this.margin,
    this.animationDuration = const Duration(seconds: 3),
    this.glowIntensity = 1.0,
  });

  @override
  State<NeonBorderContainer> createState() => _NeonBorderContainerState();
}

class _NeonBorderContainerState extends State<NeonBorderContainer>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: widget.animationDuration,
      vsync: this,
    );
    _animation = Tween<double>(begin: 0.0, end: 1.0).animate(_controller);
    _controller.repeat();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Container(
          width: widget.width,
          height: widget.height,
          margin: widget.margin,
          child: CustomPaint(
            painter: _NeonBorderPainter(
              glowColor: widget.glowColor,
              borderRadius: widget.borderRadius,
              borderWidth: widget.borderWidth,
              progress: _animation.value,
              glowIntensity: widget.glowIntensity,
            ),
            child: Container(
              padding: widget.padding,
              decoration: BoxDecoration(
                color: widget.backgroundColor ?? 
                    widget.glowColor.withOpacity(0.05),
                borderRadius: BorderRadius.circular(widget.borderRadius),
              ),
              child: widget.child,
            ),
          ),
        );
      },
    );
  }
}

/// Custom painter for animated neon border
class _NeonBorderPainter extends CustomPainter {
  final Color glowColor;
  final double borderRadius;
  final double borderWidth;
  final double progress;
  final double glowIntensity;

  _NeonBorderPainter({
    required this.glowColor,
    required this.borderRadius,
    required this.borderWidth,
    required this.progress,
    required this.glowIntensity,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final rect = Rect.fromLTWH(0, 0, size.width, size.height);
    final rrect = RRect.fromRectAndRadius(rect, Radius.circular(borderRadius));
    
    final paint = Paint()
      ..color = glowColor
      ..style = PaintingStyle.stroke
      ..strokeWidth = borderWidth;

    // Create glow effect
    for (int i = 0; i < 3; i++) {
      final glowPaint = Paint()
        ..color = glowColor.withOpacity(0.3 * glowIntensity / (i + 1))
        ..style = PaintingStyle.stroke
        ..strokeWidth = borderWidth + (i * 2)
        ..maskFilter = MaskFilter.blur(BlurStyle.normal, (i + 1) * 2.0);
      
      canvas.drawRRect(rrect, glowPaint);
    }

    // Draw the main border
    canvas.drawRRect(rrect, paint);

    // Draw animated highlight
    final highlightPaint = Paint()
      ..color = glowColor.withOpacity(0.8)
      ..style = PaintingStyle.stroke
      ..strokeWidth = borderWidth * 1.5
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 4.0);

    final path = Path()..addRRect(rrect);
    final pathMetrics = path.computeMetrics();
    
    for (final metric in pathMetrics) {
      final totalLength = metric.length;
      final startDistance = (progress * totalLength) % totalLength;
      final endDistance = (startDistance + totalLength * 0.1) % totalLength;
      
      if (startDistance < endDistance) {
        final extractPath = metric.extractPath(startDistance, endDistance);
        canvas.drawPath(extractPath, highlightPaint);
      } else {
        final extractPath1 = metric.extractPath(startDistance, totalLength);
        final extractPath2 = metric.extractPath(0, endDistance);
        canvas.drawPath(extractPath1, highlightPaint);
        canvas.drawPath(extractPath2, highlightPaint);
      }
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

/// A container that displays a holographic effect
class HolographicContainer extends StatefulWidget {
  final Widget? child;
  final double? width;
  final double? height;
  final Color primaryColor;
  final Color secondaryColor;
  final double borderRadius;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final Duration animationDuration;

  const HolographicContainer({
    super.key,
    this.child,
    this.width,
    this.height,
    required this.primaryColor,
    required this.secondaryColor,
    this.borderRadius = 12.0,
    this.padding,
    this.margin,
    this.animationDuration = const Duration(seconds: 4),
  });

  @override
  State<HolographicContainer> createState() => _HolographicContainerState();
}

class _HolographicContainerState extends State<HolographicContainer>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: widget.animationDuration,
      vsync: this,
    );
    _animation = Tween<double>(begin: 0.0, end: 1.0).animate(_controller);
    _controller.repeat();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        final gradientColors = [
          widget.primaryColor,
          widget.secondaryColor,
          widget.primaryColor,
        ];
        
        final gradientStops = [
          (_animation.value - 0.3).clamp(0.0, 1.0),
          _animation.value,
          (_animation.value + 0.3).clamp(0.0, 1.0),
        ];

        return Container(
          width: widget.width,
          height: widget.height,
          margin: widget.margin,
          padding: widget.padding,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(widget.borderRadius),
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: gradientColors,
              stops: gradientStops,
            ),
            boxShadow: [
              BoxShadow(
                color: widget.primaryColor.withOpacity(0.5),
                blurRadius: 20.0,
                spreadRadius: 2.0,
              ),
              BoxShadow(
                color: widget.secondaryColor.withOpacity(0.3),
                blurRadius: 30.0,
                spreadRadius: 4.0,
              ),
            ],
          ),
          child: widget.child,
        );
      },
    );
  }
}

/// A container with a matrix-style digital rain effect
class MatrixContainer extends StatefulWidget {
  final Widget? child;
  final double? width;
  final double? height;
  final Color glowColor;
  final double borderRadius;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;

  const MatrixContainer({
    super.key,
    this.child,
    this.width,
    this.height,
    required this.glowColor,
    this.borderRadius = 12.0,
    this.padding,
    this.margin,
  });

  @override
  State<MatrixContainer> createState() => _MatrixContainerState();
}

class _MatrixContainerState extends State<MatrixContainer>
    with TickerProviderStateMixin {
  late List<AnimationController> _controllers;
  late List<Animation<double>> _animations;

  @override
  void initState() {
    super.initState();
    _controllers = List.generate(10, (index) {
      return AnimationController(
        duration: Duration(milliseconds: 1000 + (index * 200)),
        vsync: this,
      );
    });
    
    _animations = _controllers.map((controller) {
      return Tween<double>(begin: 0.0, end: 1.0).animate(controller);
    }).toList();

    for (int i = 0; i < _controllers.length; i++) {
      Future.delayed(Duration(milliseconds: i * 100), () {
        if (mounted) {
          _controllers[i].repeat();
        }
      });
    }
  }

  @override
  void dispose() {
    for (final controller in _controllers) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: widget.width,
      height: widget.height,
      margin: widget.margin,
      child: Stack(
        children: [
          // Background container
          Container(
            decoration: BoxDecoration(
              color: Colors.black.withOpacity(0.8),
              borderRadius: BorderRadius.circular(widget.borderRadius),
              border: Border.all(
                color: widget.glowColor.withOpacity(0.5),
                width: 1.0,
              ),
            ),
          ),
          // Matrix effect
          ...List.generate(_animations.length, (index) {
            return AnimatedBuilder(
              animation: _animations[index],
              builder: (context, child) {
                return Positioned(
                  left: (index * 20.0) % (widget.width ?? 200),
                  top: _animations[index].value * (widget.height ?? 100) - 20,
                  child: Text(
                    String.fromCharCode(0x30A0 + (index % 10)),
                    style: TextStyle(
                      color: widget.glowColor.withOpacity(
                        1.0 - _animations[index].value,
                      ),
                      fontSize: 12.0,
                      fontWeight: FontWeight.bold,
                      shadows: NeonEffects.createTextGlow(
                        color: widget.glowColor,
                        intensity: 1.0 - _animations[index].value,
                      ),
                    ),
                  ),
                );
              },
            );
          }),
          // Content
          Container(
            padding: widget.padding,
            child: widget.child,
          ),
        ],
      ),
    );
  }
}
