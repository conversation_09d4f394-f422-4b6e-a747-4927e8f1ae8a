import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'neon_effects.dart';

/// A text widget with neon glow effects
class NeonText extends StatelessWidget {
  final String text;
  final Color glowColor;
  final Color? textColor;
  final double fontSize;
  final FontWeight fontWeight;
  final TextAlign textAlign;
  final bool animate;
  final double glowIntensity;
  final TextStyle? customTextStyle;
  final int? maxLines;
  final TextOverflow? overflow;

  const NeonText(
    this.text, {
    super.key,
    required this.glowColor,
    this.textColor,
    this.fontSize = 24.0,
    this.fontWeight = FontWeight.bold,
    this.textAlign = TextAlign.center,
    this.animate = false,
    this.glowIntensity = 1.0,
    this.customTextStyle,
    this.maxLines,
    this.overflow,
  });

  /// Creates a large title with neon glow
  const NeonText.title(
    this.text, {
    super.key,
    required this.glowColor,
    this.textColor,
    this.fontSize = 32.0,
    this.fontWeight = FontWeight.w900,
    this.animate = true,
    this.glowIntensity = 1.2,
    this.textAlign = TextAlign.center,
    this.customTextStyle,
    this.maxLines,
    this.overflow,
  });

  /// Creates a subtitle with neon glow
  const NeonText.subtitle(
    this.text, {
    super.key,
    required this.glowColor,
    this.textColor,
    this.animate = false,
    this.glowIntensity = 0.8,
    this.textAlign = TextAlign.center,
    this.customTextStyle,
    this.maxLines,
    this.overflow,
  })  : fontSize = 20.0,
        fontWeight = FontWeight.w700;

  /// Creates a body text with subtle neon glow
  const NeonText.body(
    this.text, {
    super.key,
    required this.glowColor,
    this.textColor,
    this.fontSize = 16.0,
    this.fontWeight = FontWeight.w500,
    this.animate = false,
    this.glowIntensity = 0.6,
    this.textAlign = TextAlign.left,
    this.customTextStyle,
    this.maxLines,
    this.overflow,
  });

  /// Creates a small caption with minimal neon glow
  const NeonText.caption(
    this.text, {
    super.key,
    required this.glowColor,
    this.textColor,
    this.animate = false,
    this.glowIntensity = 0.4,
    this.textAlign = TextAlign.center,
    this.customTextStyle,
    this.maxLines,
    this.overflow,
  })  : fontSize = 12.0,
        fontWeight = FontWeight.w400;

  /// Creates a button text with neon glow
  const NeonText.button(
    this.text, {
    super.key,
    required this.glowColor,
    this.textColor,
    this.fontSize = 18.0,
    this.fontWeight = FontWeight.w600,
    this.animate = false,
    this.glowIntensity = 1.0,
    this.textAlign = TextAlign.center,
    this.customTextStyle,
    this.maxLines = 1,
    this.overflow = TextOverflow.ellipsis,
  });

  /// Creates a heading text with neon glow
  const NeonText.heading(
    this.text, {
    super.key,
    required this.glowColor,
    this.textColor,
    this.fontSize = 28.0,
    this.fontWeight = FontWeight.w800,
    this.animate = false,
    this.glowIntensity = 1.1,
    this.textAlign = TextAlign.center,
    this.customTextStyle,
    this.maxLines,
    this.overflow,
  });

  @override
  Widget build(BuildContext context) {
    final effectiveTextColor = textColor ?? glowColor;
    
    final textStyle = customTextStyle ?? GoogleFonts.orbitron(
      fontSize: fontSize,
      fontWeight: fontWeight,
      color: effectiveTextColor,
      shadows: NeonEffects.createTextGlow(
        color: glowColor,
        intensity: glowIntensity,
      ),
    );

    Widget textWidget = Text(
      text,
      style: textStyle,
      textAlign: textAlign,
      maxLines: maxLines,
      overflow: overflow,
    );

    if (animate) {
      textWidget = textWidget.withPulse();
    }

    return textWidget;
  }
}

/// A text widget that cycles through different colors
class RainbowNeonText extends StatefulWidget {
  final String text;
  final double fontSize;
  final FontWeight fontWeight;
  final TextAlign textAlign;
  final Duration cycleDuration;
  final List<Color> colors;
  final double glowIntensity;

  const RainbowNeonText(
    this.text, {
    super.key,
    this.fontSize = 24.0,
    this.fontWeight = FontWeight.bold,
    this.textAlign = TextAlign.center,
    this.cycleDuration = const Duration(seconds: 3),
    this.colors = const [
      Colors.red,
      Colors.orange,
      Colors.yellow,
      Colors.green,
      Colors.blue,
      Colors.indigo,
      Colors.purple,
    ],
    this.glowIntensity = 1.0,
  });

  @override
  State<RainbowNeonText> createState() => _RainbowNeonTextState();
}

class _RainbowNeonTextState extends State<RainbowNeonText>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: widget.cycleDuration,
      vsync: this,
    );
    _animation = Tween<double>(begin: 0.0, end: 1.0).animate(_controller);
    _controller.repeat();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        final colorIndex = (_animation.value * widget.colors.length).floor();
        final nextColorIndex = (colorIndex + 1) % widget.colors.length;
        final t = (_animation.value * widget.colors.length) - colorIndex;
        
        final currentColor = Color.lerp(
          widget.colors[colorIndex],
          widget.colors[nextColorIndex],
          t,
        )!;

        return NeonText(
          widget.text,
          glowColor: currentColor,
          fontSize: widget.fontSize,
          fontWeight: widget.fontWeight,
          textAlign: widget.textAlign,
          glowIntensity: widget.glowIntensity,
        );
      },
    );
  }
}

/// A text widget that types out text with neon effect
class TypewriterNeonText extends StatefulWidget {
  final String text;
  final Color glowColor;
  final double fontSize;
  final FontWeight fontWeight;
  final Duration typingSpeed;
  final Duration pauseDuration;
  final bool repeat;
  final VoidCallback? onComplete;

  const TypewriterNeonText(
    this.text, {
    super.key,
    required this.glowColor,
    this.fontSize = 24.0,
    this.fontWeight = FontWeight.bold,
    this.typingSpeed = const Duration(milliseconds: 100),
    this.pauseDuration = const Duration(seconds: 2),
    this.repeat = true,
    this.onComplete,
  });

  @override
  State<TypewriterNeonText> createState() => _TypewriterNeonTextState();
}

class _TypewriterNeonTextState extends State<TypewriterNeonText> {
  String _displayedText = '';
  int _currentIndex = 0;
  bool _isTyping = true;

  @override
  void initState() {
    super.initState();
    _startTyping();
  }

  void _startTyping() {
    if (_currentIndex < widget.text.length) {
      setState(() {
        _displayedText = widget.text.substring(0, _currentIndex + 1);
        _currentIndex++;
      });
      
      Future.delayed(widget.typingSpeed, _startTyping);
    } else {
      _isTyping = false;
      widget.onComplete?.call();
      
      if (widget.repeat) {
        Future.delayed(widget.pauseDuration, () {
          if (mounted) {
            setState(() {
              _displayedText = '';
              _currentIndex = 0;
              _isTyping = true;
            });
            _startTyping();
          }
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        NeonText(
          _displayedText,
          glowColor: widget.glowColor,
          fontSize: widget.fontSize,
          fontWeight: widget.fontWeight,
        ),
        if (_isTyping)
          NeonText(
            '|',
            glowColor: widget.glowColor,
            fontSize: widget.fontSize,
            fontWeight: widget.fontWeight,
            animate: true,
          ),
      ],
    );
  }
}

/// A text widget with a glitch effect
class GlitchNeonText extends StatefulWidget {
  final String text;
  final Color glowColor;
  final double fontSize;
  final FontWeight fontWeight;
  final Duration glitchDuration;
  final double glitchIntensity;

  const GlitchNeonText(
    this.text, {
    super.key,
    required this.glowColor,
    this.fontSize = 24.0,
    this.fontWeight = FontWeight.bold,
    this.glitchDuration = const Duration(milliseconds: 200),
    this.glitchIntensity = 0.1,
  });

  @override
  State<GlitchNeonText> createState() => _GlitchNeonTextState();
}

class _GlitchNeonTextState extends State<GlitchNeonText>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  bool _isGlitching = false;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: widget.glitchDuration,
      vsync: this,
    );
    _startRandomGlitch();
  }

  void _startRandomGlitch() {
    final randomDelay = Duration(
      milliseconds: 1000 + (2000 * (1 - widget.glitchIntensity)).round(),
    );
    
    Future.delayed(randomDelay, () {
      if (mounted) {
        setState(() {
          _isGlitching = true;
        });
        _controller.forward().then((_) {
          if (mounted) {
            setState(() {
              _isGlitching = false;
            });
            _controller.reset();
            _startRandomGlitch();
          }
        });
      }
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (!_isGlitching) {
      return NeonText(
        widget.text,
        glowColor: widget.glowColor,
        fontSize: widget.fontSize,
        fontWeight: widget.fontWeight,
      );
    }

    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return Stack(
          children: [
            // Original text
            NeonText(
              widget.text,
              glowColor: widget.glowColor,
              fontSize: widget.fontSize,
              fontWeight: widget.fontWeight,
            ),
            // Red glitch layer
            Transform.translate(
              offset: Offset(-2 * _controller.value, 0),
              child: NeonText(
                widget.text,
                glowColor: Colors.red,
                textColor: Colors.red.withOpacity(0.7),
                fontSize: widget.fontSize,
                fontWeight: widget.fontWeight,
                glowIntensity: 0.5,
              ),
            ),
            // Blue glitch layer
            Transform.translate(
              offset: Offset(2 * _controller.value, 0),
              child: NeonText(
                widget.text,
                glowColor: Colors.blue,
                textColor: Colors.blue.withOpacity(0.7),
                fontSize: widget.fontSize,
                fontWeight: widget.fontWeight,
                glowIntensity: 0.5,
              ),
            ),
          ],
        );
      },
    );
  }
}
