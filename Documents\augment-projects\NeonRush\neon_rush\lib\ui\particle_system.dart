import 'dart:math';
import 'package:flutter/material.dart';

/// Particle system for neon effects like sparks, trails, and fragments
class ParticleSystem extends StatefulWidget {
  final ParticleConfig config;
  final bool isActive;
  final Offset? emissionPoint;

  const ParticleSystem({
    super.key,
    required this.config,
    this.isActive = true,
    this.emissionPoint,
  });

  @override
  State<ParticleSystem> createState() => _ParticleSystemState();
}

class _ParticleSystemState extends State<ParticleSystem>
    with TickerProviderStateMixin {
  late AnimationController _controller;
  final List<Particle> _particles = [];
  final Random _random = Random();

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 16), // ~60 FPS
      vsync: this,
    );
    
    _controller.addListener(_updateParticles);
    if (widget.isActive) {
      _controller.repeat();
    }
  }

  @override
  void didUpdateWidget(ParticleSystem oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.isActive && !_controller.isAnimating) {
      _controller.repeat();
    } else if (!widget.isActive && _controller.isAnimating) {
      _controller.stop();
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _updateParticles() {
    if (!mounted) return;

    setState(() {
      // Remove dead particles
      _particles.removeWhere((particle) => particle.isDead);

      // Update existing particles
      for (final particle in _particles) {
        particle.update();
      }

      // Emit new particles
      if (widget.isActive && _particles.length < widget.config.maxParticles) {
        _emitParticles();
      }
    });
  }

  void _emitParticles() {
    final emissionCount = widget.config.emissionRate;
    final emissionPoint = widget.emissionPoint ?? 
        Offset(MediaQuery.of(context).size.width / 2, MediaQuery.of(context).size.height / 2);

    for (int i = 0; i < emissionCount; i++) {
      if (_particles.length >= widget.config.maxParticles) break;

      _particles.add(Particle(
        position: emissionPoint + Offset(
          (_random.nextDouble() - 0.5) * widget.config.emissionRadius,
          (_random.nextDouble() - 0.5) * widget.config.emissionRadius,
        ),
        velocity: _generateRandomVelocity(),
        color: _generateRandomColor(),
        size: widget.config.minSize + 
               _random.nextDouble() * (widget.config.maxSize - widget.config.minSize),
        life: widget.config.particleLifetime,
        type: widget.config.particleType,
      ));
    }
  }

  Offset _generateRandomVelocity() {
    final angle = _random.nextDouble() * 2 * pi;
    final speed = widget.config.minSpeed + 
                  _random.nextDouble() * (widget.config.maxSpeed - widget.config.minSpeed);
    
    return Offset(
      cos(angle) * speed,
      sin(angle) * speed,
    );
  }

  Color _generateRandomColor() {
    final colors = widget.config.colors;
    return colors[_random.nextInt(colors.length)];
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox.expand(
      child: CustomPaint(
        painter: ParticlePainter(_particles),
      ),
    );
  }
}

/// Configuration for particle system behavior
class ParticleConfig {
  final ParticleType particleType;
  final int maxParticles;
  final int emissionRate;
  final double emissionRadius;
  final double minSize;
  final double maxSize;
  final double minSpeed;
  final double maxSpeed;
  final Duration particleLifetime;
  final List<Color> colors;
  final double gravity;
  final double friction;

  const ParticleConfig({
    this.particleType = ParticleType.spark,
    this.maxParticles = 50,
    this.emissionRate = 3,
    this.emissionRadius = 10.0,
    this.minSize = 2.0,
    this.maxSize = 6.0,
    this.minSpeed = 20.0,
    this.maxSpeed = 80.0,
    this.particleLifetime = const Duration(seconds: 2),
    this.colors = const [Colors.cyan, Colors.blue, Colors.purple],
    this.gravity = 0.0,
    this.friction = 0.98,
  });

  // Predefined configurations
  static const ParticleConfig sparks = ParticleConfig(
    particleType: ParticleType.spark,
    maxParticles: 30,
    emissionRate: 2,
    emissionRadius: 5.0,
    minSize: 1.0,
    maxSize: 3.0,
    minSpeed: 40.0,
    maxSpeed: 120.0,
    particleLifetime: Duration(milliseconds: 800),
    colors: [Color(0xFF00FFFF), Color(0xFF0080FF), Color(0xFF8000FF)],
    friction: 0.95,
  );

  static const ParticleConfig trail = ParticleConfig(
    particleType: ParticleType.trail,
    maxParticles: 20,
    emissionRate: 5,
    emissionRadius: 2.0,
    minSize: 2.0,
    maxSize: 4.0,
    minSpeed: 10.0,
    maxSpeed: 30.0,
    particleLifetime: Duration(milliseconds: 1200),
    colors: [Color(0xFF00FF88), Color(0xFF00DDFF)],
    friction: 0.99,
  );

  static const ParticleConfig explosion = ParticleConfig(
    particleType: ParticleType.fragment,
    maxParticles: 60,
    emissionRate: 15,
    emissionRadius: 8.0,
    minSize: 1.5,
    maxSize: 5.0,
    minSpeed: 60.0,
    maxSpeed: 200.0,
    particleLifetime: Duration(milliseconds: 1500),
    colors: [Color(0xFFFF0080), Color(0xFFFF8000), Color(0xFFFFFF00)],
    gravity: 50.0,
    friction: 0.92,
  );
}

enum ParticleType {
  spark,
  trail,
  fragment,
  glow,
}

/// Individual particle with physics and rendering properties
class Particle {
  Offset position;
  Offset velocity;
  Color color;
  double size;
  double life;
  double maxLife;
  ParticleType type;
  double rotation;
  double rotationSpeed;

  Particle({
    required this.position,
    required this.velocity,
    required this.color,
    required this.size,
    required Duration life,
    required this.type,
  }) : life = life.inMilliseconds.toDouble(),
       maxLife = life.inMilliseconds.toDouble(),
       rotation = Random().nextDouble() * 2 * pi,
       rotationSpeed = (Random().nextDouble() - 0.5) * 0.2;

  void update() {
    // Update position
    position += velocity * 0.016; // Assuming 60 FPS

    // Apply friction
    velocity *= 0.98;

    // Apply gravity (if any)
    velocity += const Offset(0, 0.5);

    // Update rotation
    rotation += rotationSpeed;

    // Update life
    life -= 16; // Decrease by frame time in milliseconds
  }

  bool get isDead => life <= 0;

  double get alpha {
    final lifeRatio = life / maxLife;
    return (lifeRatio * lifeRatio).clamp(0.0, 1.0);
  }

  double get currentSize {
    final lifeRatio = life / maxLife;
    return size * (0.5 + 0.5 * lifeRatio);
  }
}

/// Custom painter for rendering particles
class ParticlePainter extends CustomPainter {
  final List<Particle> particles;

  ParticlePainter(this.particles);

  @override
  void paint(Canvas canvas, Size size) {
    for (final particle in particles) {
      _drawParticle(canvas, particle);
    }
  }

  void _drawParticle(Canvas canvas, Particle particle) {
    final paint = Paint()
      ..color = particle.color.withValues(alpha: particle.alpha)
      ..style = PaintingStyle.fill;

    switch (particle.type) {
      case ParticleType.spark:
        _drawSpark(canvas, paint, particle);
        break;
      case ParticleType.trail:
        _drawTrail(canvas, paint, particle);
        break;
      case ParticleType.fragment:
        _drawFragment(canvas, paint, particle);
        break;
      case ParticleType.glow:
        _drawGlow(canvas, paint, particle);
        break;
    }
  }

  void _drawSpark(Canvas canvas, Paint paint, Particle particle) {
    // Draw a small glowing circle
    paint.maskFilter = MaskFilter.blur(BlurStyle.normal, particle.currentSize * 0.5);
    canvas.drawCircle(particle.position, particle.currentSize, paint);
  }

  void _drawTrail(Canvas canvas, Paint paint, Particle particle) {
    // Draw an elongated oval in the direction of movement
    canvas.save();
    canvas.translate(particle.position.dx, particle.position.dy);
    canvas.rotate(atan2(particle.velocity.dy, particle.velocity.dx));
    
    final rect = Rect.fromCenter(
      center: Offset.zero,
      width: particle.currentSize * 3,
      height: particle.currentSize,
    );
    
    paint.maskFilter = MaskFilter.blur(BlurStyle.normal, particle.currentSize * 0.3);
    canvas.drawOval(rect, paint);
    canvas.restore();
  }

  void _drawFragment(Canvas canvas, Paint paint, Particle particle) {
    // Draw a rotated square fragment
    canvas.save();
    canvas.translate(particle.position.dx, particle.position.dy);
    canvas.rotate(particle.rotation);
    
    final rect = Rect.fromCenter(
      center: Offset.zero,
      width: particle.currentSize,
      height: particle.currentSize,
    );
    
    canvas.drawRect(rect, paint);
    canvas.restore();
  }

  void _drawGlow(Canvas canvas, Paint paint, Particle particle) {
    // Draw a soft glowing orb with multiple layers
    final center = particle.position;
    final radius = particle.currentSize;
    
    // Outer glow
    paint.maskFilter = MaskFilter.blur(BlurStyle.normal, radius);
    paint.color = particle.color.withValues(alpha: particle.alpha * 0.3);
    canvas.drawCircle(center, radius * 2, paint);
    
    // Inner core
    paint.maskFilter = null;
    paint.color = particle.color.withValues(alpha: particle.alpha);
    canvas.drawCircle(center, radius * 0.5, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

/// Utility class for creating particle effects at specific events
class ParticleEffects {
  static Widget targetHitEffect(Offset position, Color color) {
    return Positioned(
      left: position.dx - 50,
      top: position.dy - 50,
      child: ParticleSystem(
        config: ParticleConfig.sparks.copyWith(
          colors: [color, color.withValues(alpha: 0.7)],
          maxParticles: 15,
          emissionRate: 8,
        ),
        isActive: true,
        emissionPoint: const Offset(50, 50),
      ),
    );
  }

  static Widget powerUpActivation(Offset position) {
    return Positioned(
      left: position.dx - 75,
      top: position.dy - 75,
      child: ParticleSystem(
        config: ParticleConfig.explosion.copyWith(
          colors: const [Color(0xFFFFD700), Color(0xFFFF8C00), Color(0xFFFF4500)],
          maxParticles: 25,
        ),
        emissionPoint: const Offset(75, 75),
      ),
    );
  }

  static Widget gameObjectTrail(Offset position, Color color) {
    return Positioned(
      left: position.dx - 10,
      top: position.dy - 10,
      child: ParticleSystem(
        config: ParticleConfig.trail.copyWith(
          colors: [color],
          maxParticles: 8,
          emissionRate: 2,
        ),
        emissionPoint: const Offset(10, 10),
      ),
    );
  }

  static Widget backgroundTapEffect(Offset position) {
    return Positioned(
      left: position.dx - 30,
      top: position.dy - 30,
      child: ParticleSystem(
        config: ParticleConfig.sparks.copyWith(
          colors: const [Color(0xFFFF4444), Color(0xFFFF6666), Color(0xFFFF8888)],
          maxParticles: 10,
          emissionRate: 5,
          minSpeed: 20,
          maxSpeed: 40,
          particleLifetime: const Duration(milliseconds: 800),
        ),
        isActive: true,
        emissionPoint: const Offset(30, 30),
      ),
    );
  }

  static Widget redTargetExplosion(Offset position) {
    return Positioned(
      left: position.dx - 60,
      top: position.dy - 60,
      child: ParticleSystem(
        config: ParticleConfig.explosion.copyWith(
          colors: const [Color(0xFFFF0000), Color(0xFFFF4444), Color(0xFFFF8888), Color(0xFFFFAAAA)],
          maxParticles: 20,
          emissionRate: 12,
          minSpeed: 30,
          maxSpeed: 80,
          minSize: 3,
          maxSize: 8,
          particleLifetime: const Duration(milliseconds: 1200),
        ),
        isActive: true,
        emissionPoint: const Offset(60, 60),
      ),
    );
  }
}

extension ParticleConfigExtension on ParticleConfig {
  ParticleConfig copyWith({
    ParticleType? particleType,
    int? maxParticles,
    int? emissionRate,
    double? emissionRadius,
    double? minSize,
    double? maxSize,
    double? minSpeed,
    double? maxSpeed,
    Duration? particleLifetime,
    List<Color>? colors,
    double? gravity,
    double? friction,
  }) {
    return ParticleConfig(
      particleType: particleType ?? this.particleType,
      maxParticles: maxParticles ?? this.maxParticles,
      emissionRate: emissionRate ?? this.emissionRate,
      emissionRadius: emissionRadius ?? this.emissionRadius,
      minSize: minSize ?? this.minSize,
      maxSize: maxSize ?? this.maxSize,
      minSpeed: minSpeed ?? this.minSpeed,
      maxSpeed: maxSpeed ?? this.maxSpeed,
      particleLifetime: particleLifetime ?? this.particleLifetime,
      colors: colors ?? this.colors,
      gravity: gravity ?? this.gravity,
      friction: friction ?? this.friction,
    );
  }
}
