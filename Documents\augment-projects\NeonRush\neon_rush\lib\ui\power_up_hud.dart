import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../game/power_up_manager.dart';
import '../models/neon_theme.dart';
import '../models/power_up.dart';
import '../ui/neon_text.dart';
import '../ui/neon_container.dart';
import '../ui/neon_effects.dart';

/// HUD widget to display active power-ups during gameplay
class PowerUpHUD extends StatelessWidget {
  const PowerUpHUD({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<PowerUpManager>(
      builder: (context, powerUpManager, child) {
        final activePowerUps = powerUpManager.activePowerUps.values.toList();
        
        if (activePowerUps.isEmpty) {
          return const SizedBox.shrink();
        }

        return Positioned(
          top: 60,
          right: 20,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: activePowerUps.map((activePowerUp) {
              return Padding(
                padding: const EdgeInsets.only(bottom: 8),
                child: _PowerUpIndicator(activePowerUp: activePowerUp),
              );
            }).toList(),
          ),
        );
      },
    );
  }
}

/// Individual power-up indicator widget
class _PowerUpIndicator extends StatefulWidget {
  final ActivePowerUp activePowerUp;

  const _PowerUpIndicator({required this.activePowerUp});

  @override
  State<_PowerUpIndicator> createState() => _PowerUpIndicatorState();
}

class _PowerUpIndicatorState extends State<_PowerUpIndicator>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _pulseAnimation = Tween<double>(
      begin: 0.8,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));

    // Start pulsing if about to expire
    if (widget.activePowerUp.isAboutToExpire) {
      _pulseController.repeat(reverse: true);
    }
  }

  @override
  void didUpdateWidget(_PowerUpIndicator oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    // Update pulsing based on expiration status
    if (widget.activePowerUp.isAboutToExpire && !_pulseController.isAnimating) {
      _pulseController.repeat(reverse: true);
    } else if (!widget.activePowerUp.isAboutToExpire && _pulseController.isAnimating) {
      _pulseController.stop();
      _pulseController.reset();
    }
  }

  @override
  void dispose() {
    _pulseController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final powerUp = widget.activePowerUp.powerUp;
    final progress = widget.activePowerUp.progress;
    final remainingSeconds = widget.activePowerUp.remainingDuration.inSeconds;

    return AnimatedBuilder(
      animation: _pulseAnimation,
      builder: (context, child) {
        final glowIntensity = widget.activePowerUp.isAboutToExpire 
            ? _pulseAnimation.value 
            : 1.0;

        return NeonContainer.card(
          glowColor: powerUp.color,
          glowIntensity: glowIntensity,
          child: Container(
            width: 80,
            height: 80,
            padding: const EdgeInsets.all(8),
            child: Stack(
              children: [
                // Background progress circle
                Positioned.fill(
                  child: CircularProgressIndicator(
                    value: 1.0 - progress,
                    strokeWidth: 3,
                    backgroundColor: powerUp.color.withValues(alpha: 0.2),
                    valueColor: AlwaysStoppedAnimation<Color>(powerUp.color),
                  ),
                ),
                
                // Power-up icon
                Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        powerUp.icon,
                        color: powerUp.color,
                        size: 24,
                        shadows: NeonEffects.createGlow(
                          color: powerUp.color,
                          intensity: glowIntensity,
                        ).map((shadow) => Shadow(
                          color: shadow.color,
                          blurRadius: shadow.blurRadius,
                          offset: shadow.offset,
                        )).toList(),
                      ),
                      
                      const SizedBox(height: 2),
                      
                      // Remaining time
                      NeonText.body(
                        '${remainingSeconds}s',
                        glowColor: powerUp.color,
                        fontSize: 10,
                        fontWeight: FontWeight.bold,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}

/// Power-up selection widget for pre-game
class PowerUpSelector extends StatelessWidget {
  final List<PowerUp> availablePowerUps;
  final List<String> selectedPowerUps;
  final Function(String) onPowerUpToggled;
  final int maxSelections;

  const PowerUpSelector({
    super.key,
    required this.availablePowerUps,
    required this.selectedPowerUps,
    required this.onPowerUpToggled,
    this.maxSelections = 3,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        NeonText.title(
          'SELECT POWER-UPS',
          glowColor: NeonThemes.cyberBlue.primary,
          fontSize: 18,
          fontWeight: FontWeight.bold,
        ),
        
        const SizedBox(height: 12),
        
        NeonText.body(
          'Choose up to $maxSelections power-ups for this level',
          glowColor: Colors.grey,
          fontSize: 12,
        ),
        
        const SizedBox(height: 16),
        
        Wrap(
          spacing: 12,
          runSpacing: 12,
          children: availablePowerUps.map((powerUp) {
            final isSelected = selectedPowerUps.contains(powerUp.id);
            final canSelect = isSelected || selectedPowerUps.length < maxSelections;
            
            return GestureDetector(
              onTap: canSelect ? () => onPowerUpToggled(powerUp.id) : null,
              child: AnimatedContainer(
                duration: const Duration(milliseconds: 200),
                width: 80,
                height: 100,
                decoration: BoxDecoration(
                  color: isSelected 
                      ? powerUp.color.withValues(alpha: 0.2)
                      : Colors.black.withValues(alpha: 0.3),
                  border: Border.all(
                    color: isSelected 
                        ? powerUp.color 
                        : (canSelect ? Colors.grey : Colors.grey.withValues(alpha: 0.3)),
                    width: isSelected ? 2 : 1,
                  ),
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: isSelected 
                      ? NeonEffects.createGlow(color: powerUp.color, intensity: 0.8)
                      : null,
                ),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      powerUp.icon,
                      color: isSelected 
                          ? powerUp.color 
                          : (canSelect ? Colors.grey : Colors.grey.withValues(alpha: 0.5)),
                      size: 28,
                    ),
                    
                    const SizedBox(height: 4),
                    
                    NeonText.body(
                      powerUp.name,
                      glowColor: isSelected ? powerUp.color : Colors.grey,
                      fontSize: 10,
                      textAlign: TextAlign.center,
                      fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                    ),
                  ],
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }
}
