import 'package:flutter/services.dart';
import 'package:flutter/foundation.dart';

/// Enhanced haptic feedback manager for NeonRush
class NeonHaptics {
  static bool _isEnabled = true;
  static bool _isSupported = true;

  /// Initialize haptic feedback system
  static Future<void> initialize() async {
    try {
      // Test if haptic feedback is supported
      await HapticFeedback.lightImpact();
      _isSupported = true;
    } catch (e) {
      _isSupported = false;
      if (kDebugMode) {
        print('Haptic feedback not supported: $e');
      }
    }
  }

  /// Enable or disable haptic feedback
  static void setEnabled(bool enabled) {
    _isEnabled = enabled;
  }

  /// Check if haptic feedback is enabled and supported
  static bool get isAvailable => _isEnabled && _isSupported;

  /// Light impact for UI interactions
  static Future<void> lightImpact() async {
    if (!isAvailable) return;
    try {
      await HapticFeedback.lightImpact();
    } catch (e) {
      if (kDebugMode) {
        print('Haptic feedback error: $e');
      }
    }
  }

  /// Medium impact for game actions
  static Future<void> mediumImpact() async {
    if (!isAvailable) return;
    try {
      await HapticFeedback.mediumImpact();
    } catch (e) {
      if (kDebugMode) {
        print('Haptic feedback error: $e');
      }
    }
  }

  /// Heavy impact for important events
  static Future<void> heavyImpact() async {
    if (!isAvailable) return;
    try {
      await HapticFeedback.heavyImpact();
    } catch (e) {
      if (kDebugMode) {
        print('Haptic feedback error: $e');
      }
    }
  }

  /// Selection click for menu navigation
  static Future<void> selectionClick() async {
    if (!isAvailable) return;
    try {
      await HapticFeedback.selectionClick();
    } catch (e) {
      if (kDebugMode) {
        print('Haptic feedback error: $e');
      }
    }
  }

  /// Vibrate for notifications (Android only)
  static Future<void> vibrate() async {
    if (!isAvailable) return;
    try {
      await HapticFeedback.vibrate();
    } catch (e) {
      if (kDebugMode) {
        print('Haptic feedback error: $e');
      }
    }
  }

  // Game-specific haptic patterns

  /// Feedback for successful target hit
  static Future<void> targetHit() async {
    await lightImpact();
  }

  /// Feedback for combo achievement
  static Future<void> comboAchieved() async {
    await mediumImpact();
  }

  /// Feedback for level completion
  static Future<void> levelComplete() async {
    await heavyImpact();
  }

  /// Feedback for game over
  static Future<void> gameOver() async {
    await heavyImpact();
  }

  /// Feedback for power-up activation
  static Future<void> powerUpActivated() async {
    await mediumImpact();
  }

  /// Feedback for collision/damage
  static Future<void> collision() async {
    await heavyImpact();
  }

  /// Feedback for button press
  static Future<void> buttonPress() async {
    await selectionClick();
  }

  /// Feedback for successful dodge
  static Future<void> successfulDodge() async {
    await lightImpact();
  }

  /// Feedback for near miss
  static Future<void> nearMiss() async {
    await lightImpact();
  }

  /// Feedback for streak achievement
  static Future<void> streakAchieved() async {
    await mediumImpact();
  }

  /// Custom pattern for special events
  static Future<void> customPattern({
    required List<HapticFeedbackType> pattern,
    Duration delay = const Duration(milliseconds: 100),
  }) async {
    if (!isAvailable) return;

    for (int i = 0; i < pattern.length; i++) {
      if (i > 0) {
        await Future.delayed(delay);
      }

      switch (pattern[i]) {
        case HapticFeedbackType.light:
          await lightImpact();
          break;
        case HapticFeedbackType.medium:
          await mediumImpact();
          break;
        case HapticFeedbackType.heavy:
          await heavyImpact();
          break;
        case HapticFeedbackType.selection:
          await selectionClick();
          break;
        case HapticFeedbackType.vibrate:
          await vibrate();
          break;
      }
    }
  }

  /// Feedback for bonus game unlock
  static Future<void> bonusUnlocked() async {
    await customPattern(
      pattern: [
        HapticFeedbackType.light,
        HapticFeedbackType.medium,
        HapticFeedbackType.heavy,
      ],
      delay: const Duration(milliseconds: 150),
    );
  }

  /// Feedback for crate opening
  static Future<void> crateOpening() async {
    await customPattern(
      pattern: [
        HapticFeedbackType.medium,
        HapticFeedbackType.light,
        HapticFeedbackType.medium,
      ],
      delay: const Duration(milliseconds: 100),
    );
  }

  /// Feedback for rare item obtained
  static Future<void> rareItemObtained() async {
    await customPattern(
      pattern: [
        HapticFeedbackType.heavy,
        HapticFeedbackType.medium,
        HapticFeedbackType.heavy,
        HapticFeedbackType.medium,
        HapticFeedbackType.heavy,
      ],
      delay: const Duration(milliseconds: 80),
    );
  }
}

/// Enum for haptic feedback types
enum HapticFeedbackType {
  light,
  medium,
  heavy,
  selection,
  vibrate,
}

/// Haptic feedback configuration
class HapticConfig {
  final bool enableGameplay;
  final bool enableUI;
  final bool enableNotifications;
  final double intensity; // 0.0 to 1.0

  const HapticConfig({
    this.enableGameplay = true,
    this.enableUI = true,
    this.enableNotifications = true,
    this.intensity = 1.0,
  });

  /// Create config with all haptics disabled
  static const HapticConfig disabled = HapticConfig(
    enableGameplay: false,
    enableUI: false,
    enableNotifications: false,
    intensity: 0.0,
  );

  /// Create config with only UI haptics enabled
  static const HapticConfig uiOnly = HapticConfig(
    enableGameplay: false,
    enableUI: true,
    enableNotifications: false,
    intensity: 0.5,
  );

  /// Create config with reduced intensity
  static const HapticConfig reduced = HapticConfig(
    enableGameplay: true,
    enableUI: true,
    enableNotifications: true,
    intensity: 0.5,
  );

  /// Copy with modifications
  HapticConfig copyWith({
    bool? enableGameplay,
    bool? enableUI,
    bool? enableNotifications,
    double? intensity,
  }) {
    return HapticConfig(
      enableGameplay: enableGameplay ?? this.enableGameplay,
      enableUI: enableUI ?? this.enableUI,
      enableNotifications: enableNotifications ?? this.enableNotifications,
      intensity: intensity ?? this.intensity,
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'enableGameplay': enableGameplay,
      'enableUI': enableUI,
      'enableNotifications': enableNotifications,
      'intensity': intensity,
    };
  }

  /// Create from JSON
  factory HapticConfig.fromJson(Map<String, dynamic> json) {
    return HapticConfig(
      enableGameplay: json['enableGameplay'] ?? true,
      enableUI: json['enableUI'] ?? true,
      enableNotifications: json['enableNotifications'] ?? true,
      intensity: (json['intensity'] ?? 1.0).toDouble(),
    );
  }
}
