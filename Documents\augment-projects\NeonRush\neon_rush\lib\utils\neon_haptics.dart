import 'package:flutter/services.dart';

/// Haptic feedback utility for TapVerse
class NeonHaptics {
  /// Light haptic feedback for button taps
  static Future<void> lightImpact() async {
    await HapticFeedback.lightImpact();
  }

  /// Medium haptic feedback for game actions
  static Future<void> mediumImpact() async {
    await HapticFeedback.mediumImpact();
  }

  /// Heavy haptic feedback for important events
  static Future<void> heavyImpact() async {
    await HapticFeedback.heavyImpact();
  }

  /// Selection haptic feedback for UI interactions
  static Future<void> selectionClick() async {
    await HapticFeedback.selectionClick();
  }

  /// Vibrate for game events
  static Future<void> vibrate() async {
    await HapticFeedback.vibrate();
  }

  /// Haptic feedback for target hits
  static Future<void> targetHit() async {
    await HapticFeedback.lightImpact();
  }

  /// Haptic feedback for game over
  static Future<void> gameOver() async {
    await HapticFeedback.heavyImpact();
  }

  /// Haptic feedback for level complete
  static Future<void> levelComplete() async {
    await HapticFeedback.mediumImpact();
  }

  /// Haptic feedback for combo achieved
  static Future<void> comboAchieved() async {
    await HapticFeedback.mediumImpact();
  }
}
