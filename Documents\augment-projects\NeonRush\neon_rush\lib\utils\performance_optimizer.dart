import 'dart:collection';
import 'package:flutter/material.dart';

/// Object pool for reusing game objects to reduce garbage collection
class ObjectPool<T> {
  final Queue<T> _pool = Queue<T>();
  final T Function() _factory;
  final void Function(T)? _reset;
  final int maxSize;

  ObjectPool({
    required T Function() factory,
    void Function(T)? reset,
    this.maxSize = 50,
  }) : _factory = factory, _reset = reset;

  /// Get an object from the pool or create a new one
  T acquire() {
    if (_pool.isNotEmpty) {
      final obj = _pool.removeFirst();
      _reset?.call(obj);
      return obj;
    }
    return _factory();
  }

  /// Return an object to the pool
  void release(T obj) {
    if (_pool.length < maxSize) {
      _pool.add(obj);
    }
  }

  /// Clear the pool
  void clear() {
    _pool.clear();
  }

  /// Get current pool size
  int get size => _pool.length;
}

/// Optimized collision detection utilities
class CollisionDetection {
  /// Fast circle-circle collision detection
  static bool circleCollision(
    Offset pos1, double radius1,
    Offset pos2, double radius2,
  ) {
    final dx = pos1.dx - pos2.dx;
    final dy = pos1.dy - pos2.dy;
    final distanceSquared = dx * dx + dy * dy;
    final radiusSum = radius1 + radius2;
    return distanceSquared <= radiusSum * radiusSum;
  }

  /// Fast rectangle-rectangle collision detection
  static bool rectCollision(Rect rect1, Rect rect2) {
    return rect1.left < rect2.right &&
           rect1.right > rect2.left &&
           rect1.top < rect2.bottom &&
           rect1.bottom > rect2.top;
  }

  /// Point-in-circle collision detection
  static bool pointInCircle(Offset point, Offset center, double radius) {
    final dx = point.dx - center.dx;
    final dy = point.dy - center.dy;
    return (dx * dx + dy * dy) <= radius * radius;
  }

  /// Polygon collision detection using separating axis theorem
  static bool polygonCollision(List<Offset> poly1, List<Offset> poly2) {
    // Simplified SAT implementation for convex polygons
    return _checkSeparation(poly1, poly2) && _checkSeparation(poly2, poly1);
  }

  static bool _checkSeparation(List<Offset> poly1, List<Offset> poly2) {
    for (int i = 0; i < poly1.length; i++) {
      final edge = poly1[(i + 1) % poly1.length] - poly1[i];
      final normal = Offset(-edge.dy, edge.dx); // Perpendicular vector

      final proj1 = _projectPolygon(poly1, normal);
      final proj2 = _projectPolygon(poly2, normal);

      if (proj1.max < proj2.min || proj2.max < proj1.min) {
        return false; // Separation found
      }
    }
    return true;
  }

  static ({double min, double max}) _projectPolygon(List<Offset> polygon, Offset axis) {
    double min = double.infinity;
    double max = double.negativeInfinity;

    for (final vertex in polygon) {
      final dot = vertex.dx * axis.dx + vertex.dy * axis.dy;
      if (dot < min) min = dot;
      if (dot > max) max = dot;
    }

    return (min: min, max: max);
  }
}

/// Spatial partitioning for efficient collision detection
class SpatialGrid<T> {
  final double cellSize;
  final Map<String, List<T>> _grid = {};
  final Map<T, Set<String>> _objectCells = {};

  SpatialGrid({this.cellSize = 100.0});

  /// Add an object to the grid
  void add(T object, Rect bounds) {
    final cells = _getCells(bounds);
    _objectCells[object] = cells;
    
    for (final cell in cells) {
      _grid.putIfAbsent(cell, () => []).add(object);
    }
  }

  /// Remove an object from the grid
  void remove(T object) {
    final cells = _objectCells[object];
    if (cells != null) {
      for (final cell in cells) {
        _grid[cell]?.remove(object);
        if (_grid[cell]?.isEmpty == true) {
          _grid.remove(cell);
        }
      }
      _objectCells.remove(object);
    }
  }

  /// Update an object's position in the grid
  void update(T object, Rect bounds) {
    remove(object);
    add(object, bounds);
  }

  /// Get nearby objects for collision testing
  List<T> getNearby(Rect bounds) {
    final cells = _getCells(bounds);
    final nearby = <T>{};
    
    for (final cell in cells) {
      final objects = _grid[cell];
      if (objects != null) {
        nearby.addAll(objects);
      }
    }
    
    return nearby.toList();
  }

  /// Clear the grid
  void clear() {
    _grid.clear();
    _objectCells.clear();
  }

  Set<String> _getCells(Rect bounds) {
    final cells = <String>{};
    final startX = (bounds.left / cellSize).floor();
    final endX = (bounds.right / cellSize).floor();
    final startY = (bounds.top / cellSize).floor();
    final endY = (bounds.bottom / cellSize).floor();

    for (int x = startX; x <= endX; x++) {
      for (int y = startY; y <= endY; y++) {
        cells.add('$x,$y');
      }
    }

    return cells;
  }
}

/// Performance monitoring utilities
class PerformanceMonitor {
  static final Map<String, List<int>> _frameTimes = {};
  static final Map<String, int> _lastFrameTime = {};

  /// Start timing a frame
  static void startFrame(String name) {
    _lastFrameTime[name] = DateTime.now().millisecondsSinceEpoch;
  }

  /// End timing a frame and record the duration
  static void endFrame(String name) {
    final startTime = _lastFrameTime[name];
    if (startTime != null) {
      final duration = DateTime.now().millisecondsSinceEpoch - startTime;
      _frameTimes.putIfAbsent(name, () => []).add(duration);
      
      // Keep only last 60 frames
      final times = _frameTimes[name]!;
      if (times.length > 60) {
        times.removeAt(0);
      }
    }
  }

  /// Get average frame time for a named operation
  static double getAverageFrameTime(String name) {
    final times = _frameTimes[name];
    if (times == null || times.isEmpty) return 0.0;
    
    return times.reduce((a, b) => a + b) / times.length;
  }

  /// Get FPS for a named operation
  static double getFPS(String name) {
    final avgTime = getAverageFrameTime(name);
    return avgTime > 0 ? 1000.0 / avgTime : 0.0;
  }

  /// Check if performance is below threshold
  static bool isPerformancePoor(String name, {double minFPS = 30.0}) {
    return getFPS(name) < minFPS;
  }

  /// Clear performance data
  static void clear() {
    _frameTimes.clear();
    _lastFrameTime.clear();
  }
}

/// Widget optimization utilities
class WidgetOptimizer {
  /// Create a RepaintBoundary wrapper for expensive widgets
  static Widget repaintBoundary({
    required Widget child,
    String? debugLabel,
  }) {
    return RepaintBoundary(
      child: child,
    );
  }

  /// Create a cached widget that rebuilds only when key changes
  static Widget cached({
    required Widget child,
    required Object cacheKey,
  }) {
    return KeyedSubtree(
      key: ValueKey(cacheKey),
      child: child,
    );
  }

  /// Optimize list rendering with viewport awareness
  static Widget optimizedList({
    required int itemCount,
    required Widget Function(BuildContext, int) itemBuilder,
    double? itemExtent,
    ScrollController? controller,
  }) {
    return ListView.builder(
      itemCount: itemCount,
      itemBuilder: (context, index) {
        return RepaintBoundary(
          child: itemBuilder(context, index),
        );
      },
      itemExtent: itemExtent,
      controller: controller,
      cacheExtent: 200.0, // Cache 200 pixels beyond viewport
    );
  }
}

/// Memory management utilities
class MemoryManager {
  static final Map<String, dynamic> _cache = {};
  static int _maxCacheSize = 100;

  /// Cache a value with automatic cleanup
  static void cache(String key, dynamic value) {
    if (_cache.length >= _maxCacheSize) {
      // Remove oldest entry (simple FIFO)
      final firstKey = _cache.keys.first;
      _cache.remove(firstKey);
    }
    _cache[key] = value;
  }

  /// Get cached value
  static T? getCached<T>(String key) {
    return _cache[key] as T?;
  }

  /// Clear cache
  static void clearCache() {
    _cache.clear();
  }

  /// Set maximum cache size
  static void setMaxCacheSize(int size) {
    _maxCacheSize = size;
    while (_cache.length > _maxCacheSize) {
      final firstKey = _cache.keys.first;
      _cache.remove(firstKey);
    }
  }
}
