import 'dart:math';
import 'package:flutter/material.dart';

import '../models/boss.dart';

/// Widget that renders a boss with animations and effects
class BossWidget extends StatefulWidget {
  final Boss boss;
  final VoidCallback? onTap;
  final bool showHealthBar;
  final bool enableAnimations;

  const BossWidget({
    super.key,
    required this.boss,
    this.onTap,
    this.showHealthBar = true,
    this.enableAnimations = true,
  });

  @override
  State<BossWidget> createState() => _BossWidgetState();
}

class _BossWidgetState extends State<BossWidget>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late AnimationController _rotationController;
  late AnimationController _attackController;
  
  late Animation<double> _pulseAnimation;
  late Animation<double> _rotationAnimation;
  late Animation<double> _attackAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    if (widget.enableAnimations) {
      _startAnimations();
    }
  }

  void _initializeAnimations() {
    // Pulse animation for boss presence
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    _pulseAnimation = Tween<double>(
      begin: 0.9,
      end: 1.1,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));

    // Rotation animation for visual effect
    _rotationController = AnimationController(
      duration: const Duration(milliseconds: 3000),
      vsync: this,
    );
    _rotationAnimation = Tween<double>(
      begin: 0.0,
      end: 2 * pi,
    ).animate(CurvedAnimation(
      parent: _rotationController,
      curve: Curves.linear,
    ));

    // Attack animation for state changes
    _attackController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _attackAnimation = Tween<double>(
      begin: 1.0,
      end: 1.3,
    ).animate(CurvedAnimation(
      parent: _attackController,
      curve: Curves.elasticOut,
    ));
  }

  void _startAnimations() {
    _pulseController.repeat(reverse: true);
    _rotationController.repeat();
    
    // Trigger attack animation based on boss state
    if (widget.boss.state == BossState.attacking) {
      _attackController.forward().then((_) => _attackController.reverse());
    }
  }

  @override
  void didUpdateWidget(BossWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    // Update animations based on boss state changes
    if (oldWidget.boss.state != widget.boss.state) {
      if (widget.boss.state == BossState.attacking) {
        _attackController.forward().then((_) => _attackController.reverse());
      }
    }
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _rotationController.dispose();
    _attackController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final bossSize = widget.boss.getCurrentSize();
    
    return Positioned(
      left: widget.boss.position.dx - bossSize / 2,
      top: widget.boss.position.dy - bossSize / 2,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Health bar
          if (widget.showHealthBar) _buildHealthBar(),
          
          // Boss sprite
          GestureDetector(
            onTap: widget.onTap,
            child: AnimatedBuilder(
              animation: Listenable.merge([
                _pulseController,
                _rotationController,
                _attackController,
              ]),
              builder: (context, child) {
                return Transform.scale(
                  scale: widget.boss.scale * 
                         (widget.enableAnimations ? _pulseAnimation.value : 1.0) *
                         (widget.boss.state == BossState.attacking ? _attackAnimation.value : 1.0),
                  child: Transform.rotate(
                    angle: widget.boss.rotation + 
                           (widget.enableAnimations ? _rotationAnimation.value * 0.1 : 0.0),
                    child: Opacity(
                      opacity: widget.boss.opacity,
                      child: _buildBossSprite(),
                    ),
                  ),
                );
              },
            ),
          ),
          
          // Boss name and level
          _buildBossInfo(),
        ],
      ),
    );
  }

  Widget _buildHealthBar() {
    final healthPercentage = widget.boss.healthPercentage;
    final barWidth = widget.boss.getCurrentSize() * 1.2;
    
    return Container(
      width: barWidth,
      height: 8,
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(4),
        border: Border.all(color: Colors.white, width: 1),
        color: Colors.black.withValues(alpha: 0.5),
      ),
      child: Stack(
        children: [
          // Health fill
          Container(
            width: barWidth * healthPercentage,
            height: 8,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(4),
              gradient: LinearGradient(
                colors: [
                  healthPercentage > 0.6 ? Colors.green : 
                  healthPercentage > 0.3 ? Colors.orange : Colors.red,
                  healthPercentage > 0.6 ? Colors.lightGreen : 
                  healthPercentage > 0.3 ? Colors.deepOrange : Colors.redAccent,
                ],
              ),
              boxShadow: [
                BoxShadow(
                  color: (healthPercentage > 0.6 ? Colors.green : 
                         healthPercentage > 0.3 ? Colors.orange : Colors.red)
                         .withValues(alpha: 0.6),
                  blurRadius: 4,
                  spreadRadius: 1,
                ),
              ],
            ),
          ),
          
          // Health text
          Center(
            child: Text(
              '${widget.boss.currentHealth}/${widget.boss.maxHealth}',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 10,
                fontWeight: FontWeight.bold,
                shadows: [
                  Shadow(
                    color: Colors.black,
                    blurRadius: 2,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBossSprite() {
    final displayColor = widget.boss.getDisplayColor();
    final bossSize = widget.boss.getCurrentSize();
    
    return Container(
      width: bossSize,
      height: bossSize,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        gradient: RadialGradient(
          colors: [
            displayColor,
            displayColor.withValues(alpha: 0.8),
            widget.boss.secondaryColor.withValues(alpha: 0.6),
          ],
          stops: const [0.0, 0.7, 1.0],
        ),
        border: Border.all(
          color: Colors.white,
          width: 3.0,
        ),
        boxShadow: [
          BoxShadow(
            color: displayColor.withValues(alpha: 0.8 * widget.boss.glowIntensity),
            blurRadius: 20.0 * widget.boss.glowIntensity,
            spreadRadius: 5.0 * widget.boss.glowIntensity,
          ),
          // Additional glow for special states
          if (widget.boss.state == BossState.vulnerable)
            BoxShadow(
              color: Colors.yellow.withValues(alpha: 0.8),
              blurRadius: 30.0,
              spreadRadius: 8.0,
            ),
          if (widget.boss.state == BossState.attacking)
            BoxShadow(
              color: Colors.red.withValues(alpha: 0.8),
              blurRadius: 25.0,
              spreadRadius: 6.0,
            ),
        ],
      ),
      child: Stack(
        children: [
          // Boss icon
          Center(
            child: Icon(
              widget.boss.icon,
              size: bossSize * 0.4,
              color: Colors.white,
              shadows: const [
                Shadow(
                  color: Colors.black,
                  blurRadius: 4,
                ),
              ],
            ),
          ),
          
          // State indicator
          if (widget.boss.state == BossState.vulnerable)
            Positioned(
              top: 8,
              right: 8,
              child: Container(
                width: 16,
                height: 16,
                decoration: const BoxDecoration(
                  shape: BoxShape.circle,
                  color: Colors.yellow,
                ),
                child: const Icon(
                  Icons.gps_fixed,
                  size: 12,
                  color: Colors.black,
                ),
              ),
            ),
            
          if (widget.boss.state == BossState.charging)
            Positioned(
              top: 8,
              left: 8,
              child: Container(
                width: 16,
                height: 16,
                decoration: const BoxDecoration(
                  shape: BoxShape.circle,
                  color: Colors.orange,
                ),
                child: const Icon(
                  Icons.warning,
                  size: 12,
                  color: Colors.white,
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildBossInfo() {
    return Container(
      margin: const EdgeInsets.only(top: 8),
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.7),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: widget.boss.primaryColor, width: 1),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            widget.boss.name,
            style: TextStyle(
              color: widget.boss.primaryColor,
              fontSize: 12,
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            'Level ${widget.boss.level}',
            style: const TextStyle(
              color: Colors.white70,
              fontSize: 10,
            ),
          ),
          // State indicator text
          Text(
            _getStateText(),
            style: TextStyle(
              color: _getStateColor(),
              fontSize: 10,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  String _getStateText() {
    switch (widget.boss.state) {
      case BossState.idle:
        return 'Ready';
      case BossState.attacking:
        return 'Attacking!';
      case BossState.vulnerable:
        return 'Vulnerable!';
      case BossState.charging:
        return 'Charging...';
      case BossState.defeated:
        return 'Defeated';
    }
  }

  Color _getStateColor() {
    switch (widget.boss.state) {
      case BossState.idle:
        return Colors.white;
      case BossState.attacking:
        return Colors.red;
      case BossState.vulnerable:
        return Colors.yellow;
      case BossState.charging:
        return Colors.orange;
      case BossState.defeated:
        return Colors.green;
    }
  }
}
