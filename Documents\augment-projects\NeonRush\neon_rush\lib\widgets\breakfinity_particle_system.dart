import 'package:flutter/material.dart';
import 'dart:math';
import 'dart:ui' as ui;

/// Enhanced particle system for Breakfinity breaking effects
class BreakfinityParticle {
  Offset position;
  Offset velocity;
  double life;
  double maxLife;
  Color color;
  double size;
  double rotation;
  double rotationSpeed;
  String type;
  Map<String, dynamic> properties;

  BreakfinityParticle({
    required this.position,
    required this.velocity,
    required this.life,
    required this.maxLife,
    required this.color,
    required this.size,
    this.rotation = 0.0,
    this.rotationSpeed = 0.0,
    this.type = 'default',
    this.properties = const {},
  });

  /// Update particle physics
  void update(double deltaTime) {
    // Update position
    position += velocity * deltaTime;
    
    // Update rotation
    rotation += rotationSpeed * deltaTime;
    
    // Apply gravity for certain particle types
    if (type == 'fragment' || type == 'debris') {
      velocity = velocity + const Offset(0, 200) * deltaTime; // Gravity
    }
    
    // Apply air resistance
    velocity *= 0.98;
    
    // Decrease life
    life -= deltaTime;
    
    // Update size based on life for some types
    if (type == 'explosion' || type == 'glow') {
      final lifeRatio = life / maxLife;
      size = properties['originalSize'] * (0.5 + lifeRatio * 0.5);
    }
  }

  /// Check if particle is still alive
  bool get isAlive => life > 0;

  /// Get current alpha based on life
  double get alpha {
    final lifeRatio = life / maxLife;
    switch (type) {
      case 'explosion':
        return (lifeRatio * 0.8).clamp(0.0, 1.0);
      case 'glow':
        return (lifeRatio * 0.6).clamp(0.0, 1.0);
      case 'spark':
        return lifeRatio.clamp(0.0, 1.0);
      default:
        return (lifeRatio * 0.9).clamp(0.0, 1.0);
    }
  }
}

/// Particle system for managing breaking effects
class BreakfinityParticleSystem extends StatefulWidget {
  final Size size;
  final Function(Offset, String, Color)? onParticleRequest;

  const BreakfinityParticleSystem({
    super.key,
    required this.size,
    this.onParticleRequest,
  });

  @override
  State<BreakfinityParticleSystem> createState() => _BreakfinityParticleSystemState();
}

class _BreakfinityParticleSystemState extends State<BreakfinityParticleSystem>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  final List<BreakfinityParticle> _particles = [];
  final Random _random = Random();

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(seconds: 1),
      vsync: this,
    )..repeat();
    
    _animationController.addListener(_updateParticles);
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _updateParticles() {
    setState(() {
      final deltaTime = 1.0 / 60.0; // Assume 60 FPS
      
      // Update existing particles
      for (int i = _particles.length - 1; i >= 0; i--) {
        _particles[i].update(deltaTime);
        if (!_particles[i].isAlive) {
          _particles.removeAt(i);
        }
      }
    });
  }

  /// Create breaking effect at position
  void createBreakingEffect(Offset position, Color color, String effectType) {
    switch (effectType) {
      case 'tap':
        _createTapEffect(position, color);
        break;
      case 'break':
        _createBreakEffect(position, color);
        break;
      case 'explosion':
        _createExplosionEffect(position, color);
        break;
      case 'crack':
        _createCrackEffect(position, color);
        break;
    }
  }

  void _createTapEffect(Offset position, Color color) {
    // Central glow
    _particles.add(BreakfinityParticle(
      position: position,
      velocity: Offset.zero,
      life: 0.3,
      maxLife: 0.3,
      color: color,
      size: 30.0,
      type: 'glow',
      properties: {'originalSize': 30.0},
    ));

    // Ripple rings
    for (int i = 0; i < 3; i++) {
      _particles.add(BreakfinityParticle(
        position: position,
        velocity: Offset.zero,
        life: 0.5 + i * 0.1,
        maxLife: 0.5 + i * 0.1,
        color: color.withValues(alpha: 0.3),
        size: 20.0 + i * 15.0,
        type: 'ripple',
        properties: {'originalSize': 20.0 + i * 15.0},
      ));
    }

    // Sparks
    for (int i = 0; i < 8; i++) {
      final angle = (i / 8.0) * 2 * pi;
      final speed = 100.0 + _random.nextDouble() * 50.0;
      _particles.add(BreakfinityParticle(
        position: position,
        velocity: Offset(cos(angle) * speed, sin(angle) * speed),
        life: 0.2 + _random.nextDouble() * 0.2,
        maxLife: 0.4,
        color: color,
        size: 3.0 + _random.nextDouble() * 2.0,
        type: 'spark',
      ));
    }
  }

  void _createBreakEffect(Offset position, Color color) {
    // Large explosion
    _particles.add(BreakfinityParticle(
      position: position,
      velocity: Offset.zero,
      life: 0.8,
      maxLife: 0.8,
      color: color,
      size: 60.0,
      type: 'explosion',
      properties: {'originalSize': 60.0},
    ));

    // Fragments
    for (int i = 0; i < 12; i++) {
      final angle = (i / 12.0) * 2 * pi + _random.nextDouble() * 0.5;
      final speed = 150.0 + _random.nextDouble() * 100.0;
      _particles.add(BreakfinityParticle(
        position: position + Offset(_random.nextDouble() * 20 - 10, _random.nextDouble() * 20 - 10),
        velocity: Offset(cos(angle) * speed, sin(angle) * speed),
        life: 1.0 + _random.nextDouble() * 0.5,
        maxLife: 1.5,
        color: color,
        size: 4.0 + _random.nextDouble() * 3.0,
        rotation: _random.nextDouble() * 2 * pi,
        rotationSpeed: (_random.nextDouble() - 0.5) * 10.0,
        type: 'fragment',
      ));
    }

    // Energy burst
    for (int i = 0; i < 20; i++) {
      final angle = _random.nextDouble() * 2 * pi;
      final speed = 200.0 + _random.nextDouble() * 150.0;
      _particles.add(BreakfinityParticle(
        position: position,
        velocity: Offset(cos(angle) * speed, sin(angle) * speed),
        life: 0.3 + _random.nextDouble() * 0.3,
        maxLife: 0.6,
        color: color.withValues(alpha: 0.8),
        size: 2.0 + _random.nextDouble() * 1.5,
        type: 'energy',
      ));
    }
  }

  void _createExplosionEffect(Offset position, Color color) {
    // Massive central explosion
    _particles.add(BreakfinityParticle(
      position: position,
      velocity: Offset.zero,
      life: 1.2,
      maxLife: 1.2,
      color: color,
      size: 100.0,
      type: 'explosion',
      properties: {'originalSize': 100.0},
    ));

    // Shockwave
    _particles.add(BreakfinityParticle(
      position: position,
      velocity: Offset.zero,
      life: 0.8,
      maxLife: 0.8,
      color: color.withValues(alpha: 0.2),
      size: 150.0,
      type: 'shockwave',
      properties: {'originalSize': 150.0},
    ));

    // Many fragments
    for (int i = 0; i < 25; i++) {
      final angle = _random.nextDouble() * 2 * pi;
      final speed = 200.0 + _random.nextDouble() * 200.0;
      _particles.add(BreakfinityParticle(
        position: position,
        velocity: Offset(cos(angle) * speed, sin(angle) * speed),
        life: 1.5 + _random.nextDouble() * 1.0,
        maxLife: 2.5,
        color: color,
        size: 3.0 + _random.nextDouble() * 4.0,
        rotation: _random.nextDouble() * 2 * pi,
        rotationSpeed: (_random.nextDouble() - 0.5) * 15.0,
        type: 'fragment',
      ));
    }
  }

  void _createCrackEffect(Offset position, Color color) {
    // Crack lines (simulated with small particles)
    for (int i = 0; i < 6; i++) {
      final angle = (i / 6.0) * 2 * pi;
      final length = 30.0 + _random.nextDouble() * 20.0;
      
      for (int j = 0; j < 8; j++) {
        final distance = (j / 8.0) * length;
        final particlePos = position + Offset(cos(angle) * distance, sin(angle) * distance);
        
        _particles.add(BreakfinityParticle(
          position: particlePos,
          velocity: Offset.zero,
          life: 0.5 + j * 0.05,
          maxLife: 0.5 + j * 0.05,
          color: color.withValues(alpha: 0.6),
          size: 2.0,
          type: 'crack',
        ));
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return CustomPaint(
      size: widget.size,
      painter: _ParticleSystemPainter(_particles),
    );
  }
}

/// Custom painter for rendering particles
class _ParticleSystemPainter extends CustomPainter {
  final List<BreakfinityParticle> particles;

  _ParticleSystemPainter(this.particles);

  @override
  void paint(Canvas canvas, Size size) {
    for (final particle in particles) {
      _paintParticle(canvas, particle);
    }
  }

  void _paintParticle(Canvas canvas, BreakfinityParticle particle) {
    final paint = Paint()
      ..color = particle.color.withValues(alpha: particle.alpha)
      ..style = PaintingStyle.fill;

    switch (particle.type) {
      case 'glow':
      case 'explosion':
        // Radial gradient for glow effect
        paint.shader = ui.Gradient.radial(
          particle.position,
          particle.size / 2,
          [
            particle.color.withValues(alpha: particle.alpha),
            particle.color.withValues(alpha: 0.0),
          ],
        );
        canvas.drawCircle(particle.position, particle.size / 2, paint);
        break;

      case 'ripple':
      case 'shockwave':
        paint.style = PaintingStyle.stroke;
        paint.strokeWidth = 2.0;
        canvas.drawCircle(particle.position, particle.size / 2, paint);
        break;

      case 'fragment':
        canvas.save();
        canvas.translate(particle.position.dx, particle.position.dy);
        canvas.rotate(particle.rotation);
        canvas.drawRect(
          Rect.fromCenter(center: Offset.zero, width: particle.size, height: particle.size),
          paint,
        );
        canvas.restore();
        break;

      default:
        canvas.drawCircle(particle.position, particle.size / 2, paint);
        break;
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
