import 'package:flutter/material.dart';
import 'dart:math';
import 'dart:ui' as ui;

import '../models/breakfinity_section.dart';
import '../models/breakfinity_layer_theme.dart';

/// Enhanced renderer for Breakfinity sections with theme support
class BreakfinitySectionRenderer extends StatefulWidget {
  final BreakfinitySection section;
  final VoidCallback? onTap;
  final bool showCracks;
  final bool showGlow;
  final double animationProgress;

  const BreakfinitySectionRenderer({
    super.key,
    required this.section,
    this.onTap,
    this.showCracks = true,
    this.showGlow = true,
    this.animationProgress = 0.0,
  });

  @override
  State<BreakfinitySectionRenderer> createState() => _BreakfinitySectionRendererState();
}

class _BreakfinitySectionRendererState extends State<BreakfinitySectionRenderer>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late AnimationController _glowController;
  late Animation<double> _pulseAnimation;
  late Animation<double> _glowAnimation;

  @override
  void initState() {
    super.initState();
    
    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );
    
    _glowController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    );

    _pulseAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));

    _glowAnimation = Tween<double>(
      begin: 0.5,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _glowController,
      curve: Curves.easeInOut,
    ));

    // Start animations for special sections
    if (widget.section.hasSpecialEffects) {
      _pulseController.repeat(reverse: true);
      _glowController.repeat(reverse: true);
    }
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _glowController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (widget.section.isDestroyed) {
      return _buildDestroyedSection();
    }

    return GestureDetector(
      onTap: widget.onTap,
      child: AnimatedBuilder(
        animation: Listenable.merge([_pulseAnimation, _glowAnimation]),
        builder: (context, child) {
          return CustomPaint(
            size: widget.section.size,
            painter: _SectionPainter(
              section: widget.section,
              pulseValue: _pulseAnimation.value,
              glowValue: _glowAnimation.value,
              showCracks: widget.showCracks,
              showGlow: widget.showGlow,
              animationProgress: widget.animationProgress,
            ),
          );
        },
      ),
    );
  }

  Widget _buildDestroyedSection() {
    return CustomPaint(
      size: widget.section.size,
      painter: _DestroyedSectionPainter(
        section: widget.section,
        animationProgress: widget.animationProgress,
      ),
    );
  }
}

/// Custom painter for rendering intact sections
class _SectionPainter extends CustomPainter {
  final BreakfinitySection section;
  final double pulseValue;
  final double glowValue;
  final bool showCracks;
  final bool showGlow;
  final double animationProgress;

  _SectionPainter({
    required this.section,
    required this.pulseValue,
    required this.glowValue,
    required this.showCracks,
    required this.showGlow,
    required this.animationProgress,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final rect = Rect.fromLTWH(0, 0, size.width, size.height);
    final center = Offset(size.width / 2, size.height / 2);
    
    // Draw glow effect
    if (showGlow && !section.isDestroyed) {
      _drawGlowEffect(canvas, rect, center);
    }
    
    // Draw main section
    _drawMainSection(canvas, rect);
    
    // Draw special effects based on type
    _drawTypeSpecificEffects(canvas, rect, center);
    
    // Draw cracks
    if (showCracks && section.crackProgress > 0) {
      _drawCracks(canvas, size);
    }
    
    // Draw health indicator
    _drawHealthIndicator(canvas, rect);
  }

  void _drawGlowEffect(Canvas canvas, Rect rect, Offset center) {
    final glowColor = section.glowColor;
    final glowRadius = (rect.width / 2) * (1.0 + glowValue * 0.3);
    
    final glowPaint = Paint()
      ..shader = ui.Gradient.radial(
        center,
        glowRadius,
        [
          glowColor.withValues(alpha: glowColor.a * glowValue),
          glowColor.withValues(alpha: 0.0),
        ],
      );
    
    canvas.drawCircle(center, glowRadius, glowPaint);
  }

  void _drawMainSection(Canvas canvas, Rect rect) {
    final paint = Paint()
      ..color = section.themedColor
      ..style = PaintingStyle.fill;
    
    // Apply pulse effect for special sections
    if (section.hasSpecialEffects) {
      final scale = pulseValue;
      canvas.save();
      canvas.scale(scale, scale);
      canvas.translate(rect.width * (1 - scale) / 2, rect.height * (1 - scale) / 2);
    }
    
    // Draw rounded rectangle
    final borderRadius = min(rect.width, rect.height) * 0.1;
    final rrect = RRect.fromRectAndRadius(rect, Radius.circular(borderRadius));
    canvas.drawRRect(rrect, paint);
    
    // Draw border
    final borderPaint = Paint()
      ..color = section.layerTheme.accentColor
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2.0;
    canvas.drawRRect(rrect, borderPaint);
    
    if (section.hasSpecialEffects) {
      canvas.restore();
    }
  }

  void _drawTypeSpecificEffects(Canvas canvas, Rect rect, Offset center) {
    switch (section.type) {
      case SectionType.crystal:
        _drawCrystalEffect(canvas, rect, center);
        break;
      case SectionType.mystery:
        _drawMysteryEffect(canvas, rect, center);
        break;
      case SectionType.reinforced:
        _drawReinforcedEffect(canvas, rect);
        break;
      default:
        break;
    }
  }

  void _drawCrystalEffect(Canvas canvas, Rect rect, Offset center) {
    final paint = Paint()
      ..color = section.layerTheme.accentColor.withValues(alpha: 0.6 * glowValue)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.5;
    
    // Draw crystal facets
    final path = Path();
    final radius = min(rect.width, rect.height) * 0.3;
    
    for (int i = 0; i < 6; i++) {
      final angle = (i / 6.0) * 2 * pi;
      final point = center + Offset(cos(angle) * radius, sin(angle) * radius);
      
      if (i == 0) {
        path.moveTo(center.dx, center.dy);
        path.lineTo(point.dx, point.dy);
      } else {
        path.moveTo(center.dx, center.dy);
        path.lineTo(point.dx, point.dy);
      }
    }
    
    canvas.drawPath(path, paint);
  }

  void _drawMysteryEffect(Canvas canvas, Rect rect, Offset center) {
    final paint = Paint()
      ..color = section.layerTheme.accentColor.withValues(alpha: 0.8 * glowValue)
      ..style = PaintingStyle.fill;
    
    // Draw question mark
    final textPainter = TextPainter(
      text: TextSpan(
        text: '?',
        style: TextStyle(
          color: paint.color,
          fontSize: min(rect.width, rect.height) * 0.4,
          fontWeight: FontWeight.bold,
        ),
      ),
      textDirection: TextDirection.ltr,
    );
    
    textPainter.layout();
    textPainter.paint(
      canvas,
      center - Offset(textPainter.width / 2, textPainter.height / 2),
    );
  }

  void _drawReinforcedEffect(Canvas canvas, Rect rect) {
    final paint = Paint()
      ..color = section.layerTheme.secondaryColor.withValues(alpha: 0.5)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 3.0;
    
    // Draw reinforcement lines
    final spacing = rect.width / 4;
    for (int i = 1; i < 4; i++) {
      final x = i * spacing;
      canvas.drawLine(
        Offset(x, 0),
        Offset(x, rect.height),
        paint,
      );
    }
  }

  void _drawCracks(Canvas canvas, Size size) {
    final crackLines = section.generateCrackLines();
    if (crackLines.isEmpty) return;
    
    final paint = Paint()
      ..color = Colors.black.withValues(alpha: 0.7)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.5
      ..strokeCap = StrokeCap.round;
    
    for (int i = 0; i < crackLines.length; i += 2) {
      if (i + 1 < crackLines.length) {
        canvas.drawLine(crackLines[i], crackLines[i + 1], paint);
      }
    }
  }

  void _drawHealthIndicator(Canvas canvas, Rect rect) {
    if (section.currentHealth >= section.maxHealth) return;
    
    final healthRatio = section.currentHealth / section.maxHealth;
    final barWidth = rect.width * 0.8;
    final barHeight = 4.0;
    final barRect = Rect.fromLTWH(
      (rect.width - barWidth) / 2,
      rect.height - barHeight - 4,
      barWidth,
      barHeight,
    );
    
    // Background
    final bgPaint = Paint()
      ..color = Colors.black.withValues(alpha: 0.5)
      ..style = PaintingStyle.fill;
    canvas.drawRect(barRect, bgPaint);
    
    // Health bar
    final healthRect = Rect.fromLTWH(
      barRect.left,
      barRect.top,
      barRect.width * healthRatio,
      barRect.height,
    );
    
    final healthColor = Color.lerp(
      Colors.red,
      Colors.green,
      healthRatio,
    )!;
    
    final healthPaint = Paint()
      ..color = healthColor
      ..style = PaintingStyle.fill;
    canvas.drawRect(healthRect, healthPaint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

/// Custom painter for destroyed sections showing next layer preview
class _DestroyedSectionPainter extends CustomPainter {
  final BreakfinitySection section;
  final double animationProgress;

  _DestroyedSectionPainter({
    required this.section,
    required this.animationProgress,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final rect = Rect.fromLTWH(0, 0, size.width, size.height);
    
    // Show next layer preview
    final nextLayerTheme = BreakfinityLayerThemes.getThemeForLayer(section.layer + 1);
    final previewColor = nextLayerTheme.primaryColor.withValues(alpha: 0.3);
    
    final paint = Paint()
      ..color = previewColor
      ..style = PaintingStyle.fill;
    
    final borderRadius = min(rect.width, rect.height) * 0.1;
    final rrect = RRect.fromRectAndRadius(rect, Radius.circular(borderRadius));
    canvas.drawRRect(rrect, paint);
    
    // Draw preview border
    final borderPaint = Paint()
      ..color = nextLayerTheme.accentColor.withValues(alpha: 0.5)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.0
      ..strokeCap = StrokeCap.round;
    
    canvas.drawRRect(rrect, borderPaint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
