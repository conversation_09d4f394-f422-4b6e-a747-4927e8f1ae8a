import 'dart:math';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';

import '../constants.dart';
import '../game/input_system.dart';

/// Visual feedback widget for input gestures
class InputFeedback extends StatefulWidget {
  final InputSystem inputSystem;
  final Size screenSize;

  const InputFeedback({
    super.key,
    required this.inputSystem,
    required this.screenSize,
  });

  @override
  State<InputFeedback> createState() => _InputFeedbackState();
}

class _InputFeedbackState extends State<InputFeedback>
    with TickerProviderStateMixin {
  final List<FeedbackEffect> _effects = [];
  late AnimationController _holdController;
  late Animation<double> _holdAnimation;

  @override
  void initState() {
    super.initState();
    
    _holdController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    
    _holdAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _holdController,
      curve: Curves.easeInOut,
    ));
    
    _setupInputCallbacks();
  }

  void _setupInputCallbacks() {
    // Register callbacks for different input types
    widget.inputSystem.registerCallback(InputType.tap, _onTap);
    widget.inputSystem.registerCallback(InputType.swipe, _onSwipe);
    widget.inputSystem.registerCallback(InputType.hold, _onHold);
    widget.inputSystem.registerCallback(InputType.drag, _onDrag);
  }

  void _onTap(InputEvent event) {
    _addEffect(TapEffect(
      position: event.position,
      color: NeonColors.primaryAccent,
    ));
  }

  void _onSwipe(InputEvent event) {
    if (event.startPosition != null && event.endPosition != null) {
      _addEffect(SwipeEffect(
        startPosition: event.startPosition!,
        endPosition: event.endPosition!,
        direction: event.swipeDirection ?? SwipeDirection.right,
        color: NeonColors.secondaryAccent,
      ));
    }
  }

  void _onHold(InputEvent event) {
    _addEffect(HoldEffect(
      position: event.position,
      color: NeonColors.warning,
    ));
  }

  void _onDrag(InputEvent event) {
    _addEffect(DragEffect(
      position: event.position,
      color: NeonColors.neonGreen,
    ));
  }

  void _addEffect(FeedbackEffect effect) {
    setState(() {
      _effects.add(effect);
    });
    
    // Remove effect after animation completes
    Future.delayed(effect.duration, () {
      if (mounted) {
        setState(() {
          _effects.remove(effect);
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        // Render all active effects
        ..._effects.map((effect) => _buildEffect(effect)),
        
        // Hold progress indicator
        if (widget.inputSystem.isHolding && widget.inputSystem.holdPosition != null)
          _buildHoldProgress(),
      ],
    );
  }

  Widget _buildEffect(FeedbackEffect effect) {
    switch (effect.runtimeType) {
      case TapEffect:
        return _buildTapEffect(effect as TapEffect);
      case SwipeEffect:
        return _buildSwipeEffect(effect as SwipeEffect);
      case HoldEffect:
        return _buildHoldEffect(effect as HoldEffect);
      case DragEffect:
        return _buildDragEffect(effect as DragEffect);
      default:
        return const SizedBox.shrink();
    }
  }

  Widget _buildTapEffect(TapEffect effect) {
    return Positioned(
      left: effect.position.dx - 25,
      top: effect.position.dy - 25,
      child: Container(
        width: 50,
        height: 50,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: effect.color.withValues(alpha: 0.3),
          border: Border.all(
            color: effect.color,
            width: 2,
          ),
        ),
      )
          .animate()
          .scale(
            begin: const Offset(0.5, 0.5),
            end: const Offset(1.5, 1.5),
            duration: const Duration(milliseconds: 300),
          )
          .fadeOut(
            duration: const Duration(milliseconds: 300),
          ),
    );
  }

  Widget _buildSwipeEffect(SwipeEffect effect) {
    final delta = effect.endPosition - effect.startPosition;
    final angle = atan2(delta.dy, delta.dx);
    final length = delta.distance;

    return Positioned(
      left: effect.startPosition.dx,
      top: effect.startPosition.dy,
      child: Transform.rotate(
        angle: angle,
        child: Container(
          width: length,
          height: 4,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                effect.color.withValues(alpha: 0.8),
                effect.color.withValues(alpha: 0.2),
              ],
            ),
            borderRadius: BorderRadius.circular(2),
          ),
        ),
      )
          .animate()
          .slideX(
            begin: 0,
            end: 1,
            duration: const Duration(milliseconds: 400),
          )
          .fadeOut(
            delay: const Duration(milliseconds: 200),
            duration: const Duration(milliseconds: 200),
          ),
    );
  }

  Widget _buildHoldEffect(HoldEffect effect) {
    return Positioned(
      left: effect.position.dx - 30,
      top: effect.position.dy - 30,
      child: Container(
        width: 60,
        height: 60,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          border: Border.all(
            color: effect.color,
            width: 3,
          ),
        ),
        child: Center(
          child: Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: effect.color.withValues(alpha: 0.3),
            ),
          ),
        ),
      )
          .animate(onPlay: (controller) => controller.repeat())
          .scale(
            begin: const Offset(0.8, 0.8),
            end: const Offset(1.2, 1.2),
            duration: const Duration(milliseconds: 800),
          ),
    );
  }

  Widget _buildDragEffect(DragEffect effect) {
    return Positioned(
      left: effect.position.dx - 15,
      top: effect.position.dy - 15,
      child: Container(
        width: 30,
        height: 30,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: effect.color.withValues(alpha: 0.6),
          boxShadow: [
            BoxShadow(
              color: effect.color.withValues(alpha: 0.4),
              blurRadius: 10,
              spreadRadius: 2,
            ),
          ],
        ),
      )
          .animate()
          .fadeOut(
            duration: const Duration(milliseconds: 200),
          ),
    );
  }

  Widget _buildHoldProgress() {
    final position = widget.inputSystem.holdPosition!;
    final progress = widget.inputSystem.getHoldProgress();

    return Positioned(
      left: position.dx - 40,
      top: position.dy - 40,
      child: SizedBox(
        width: 80,
        height: 80,
        child: CircularProgressIndicator(
          value: progress,
          strokeWidth: 4,
          valueColor: AlwaysStoppedAnimation<Color>(NeonColors.warning),
          backgroundColor: NeonColors.warning.withValues(alpha: 0.3),
        ),
      ),
    );
  }

  @override
  void dispose() {
    _holdController.dispose();
    super.dispose();
  }
}

/// Base class for feedback effects
abstract class FeedbackEffect {
  final Offset position;
  final Color color;
  final Duration duration;

  FeedbackEffect({
    required this.position,
    required this.color,
    this.duration = const Duration(milliseconds: 500),
  });
}

/// Tap feedback effect
class TapEffect extends FeedbackEffect {
  TapEffect({
    required super.position,
    required super.color,
  }) : super(duration: const Duration(milliseconds: 300));
}

/// Swipe feedback effect
class SwipeEffect extends FeedbackEffect {
  final Offset startPosition;
  final Offset endPosition;
  final SwipeDirection direction;

  SwipeEffect({
    required this.startPosition,
    required this.endPosition,
    required this.direction,
    required super.color,
  }) : super(
          position: startPosition,
          duration: const Duration(milliseconds: 400),
        );
}

/// Hold feedback effect
class HoldEffect extends FeedbackEffect {
  HoldEffect({
    required super.position,
    required super.color,
  }) : super(duration: const Duration(milliseconds: 1000));
}

/// Drag feedback effect
class DragEffect extends FeedbackEffect {
  DragEffect({
    required super.position,
    required super.color,
  }) : super(duration: const Duration(milliseconds: 200));
}

/// Input instruction overlay
class InputInstructions extends StatelessWidget {
  final String instruction;
  final InputType expectedInput;
  final bool isVisible;

  const InputInstructions({
    super.key,
    required this.instruction,
    required this.expectedInput,
    this.isVisible = true,
  });

  @override
  Widget build(BuildContext context) {
    if (!isVisible) return const SizedBox.shrink();

    return Positioned(
      bottom: 100,
      left: 20,
      right: 20,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: NeonColors.surface.withValues(alpha: 0.9),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: NeonColors.primaryAccent.withValues(alpha: 0.5),
            width: 1,
          ),
        ),
        child: Row(
          children: [
            _buildInputIcon(),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                instruction,
                style: TextStyle(
                  color: NeonColors.textPrimary,
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
      )
          .animate()
          .slideY(
            begin: 1,
            end: 0,
            duration: const Duration(milliseconds: 300),
          )
          .fadeIn(
            duration: const Duration(milliseconds: 300),
          ),
    );
  }

  Widget _buildInputIcon() {
    IconData iconData;
    Color iconColor;

    switch (expectedInput) {
      case InputType.tap:
        iconData = Icons.touch_app;
        iconColor = NeonColors.primaryAccent;
        break;
      case InputType.swipe:
        iconData = Icons.swipe;
        iconColor = NeonColors.secondaryAccent;
        break;
      case InputType.hold:
        iconData = Icons.radio_button_checked;
        iconColor = NeonColors.warning;
        break;
      case InputType.drag:
        iconData = Icons.drag_indicator;
        iconColor = NeonColors.neonGreen;
        break;
      default:
        iconData = Icons.touch_app;
        iconColor = NeonColors.primaryAccent;
    }

    return Container(
      width: 40,
      height: 40,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: iconColor.withValues(alpha: 0.2),
        border: Border.all(
          color: iconColor,
          width: 2,
        ),
      ),
      child: Icon(
        iconData,
        color: iconColor,
        size: 20,
      ),
    );
  }
}
