import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';

import '../constants.dart';
import '../core/sound_manager.dart';
import '../game/level_progression.dart';
import '../providers/game_provider.dart';
import '../services/particle_service.dart';
import '../models/token_transaction.dart';
import '../models/power_up.dart';
import 'box_selection_widget.dart';

/// Popup shown when a level is completed
class LevelCompletionPopup extends StatefulWidget {
  final int level;
  final int finalScore;
  final int tokensEarned;
  final int xpEarned;
  final Map<String, dynamic> stats;
  final List<LevelObjective> completedObjectives;
  final List<String> achievementsUnlocked;
  final VoidCallback onReplay;
  final VoidCallback onNextLevel;
  final VoidCallback onHome;

  const LevelCompletionPopup({
    super.key,
    required this.level,
    required this.finalScore,
    required this.tokensEarned,
    required this.xpEarned,
    required this.stats,
    required this.completedObjectives,
    required this.achievementsUnlocked,
    required this.onReplay,
    required this.onNextLevel,
    required this.onHome,
  });

  @override
  State<LevelCompletionPopup> createState() => _LevelCompletionPopupState();
}

class _LevelCompletionPopupState extends State<LevelCompletionPopup>
    with TickerProviderStateMixin {
  late AnimationController _slideController;
  late AnimationController _scoreController;
  late AnimationController _tokenController;
  final ParticleService _particleService = ParticleService();

  bool _boxSelectionUsed = false;

  @override
  void initState() {
    super.initState();
    
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    
    _scoreController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    
    _tokenController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    // Initialize particle service
    _particleService.initialize();

    // Start animations
    _slideController.forward();
    Future.delayed(const Duration(milliseconds: 300), () {
      _scoreController.forward();
    });
    Future.delayed(const Duration(milliseconds: 600), () {
      _tokenController.forward();
      _particleService.playConfetti('level_complete');
      // Play reward sound
      SoundManager().playReward();
    });
  }

  @override
  void dispose() {
    _slideController.dispose();
    _scoreController.dispose();
    _tokenController.dispose();
    _particleService.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.black54,
      child: Stack(
        children: [
          // Confetti
          _particleService.buildLevelCompleteConfetti(),
          
          // Main popup
          Center(
            child: AnimatedBuilder(
              animation: _slideController,
              builder: (context, child) {
                return Transform.scale(
                  scale: _slideController.value,
                  child: LayoutBuilder(
                    builder: (context, constraints) {
                      final screenWidth = MediaQuery.of(context).size.width;
                      final isSmallScreen = screenWidth < 400;
                      final popupWidth = isSmallScreen
                          ? screenWidth * 0.95
                          : screenWidth * 0.9;
                      final maxWidth = isSmallScreen ? 350.0 : 400.0;
                      final padding = isSmallScreen ? 16.0 : 24.0;
                      final spacing = isSmallScreen ? 12.0 : 16.0;

                      return Container(
                        width: popupWidth,
                        constraints: BoxConstraints(maxWidth: maxWidth),
                        decoration: BoxDecoration(
                          color: NeonColors.surface,
                          borderRadius: BorderRadius.circular(20),
                          border: Border.all(
                            color: NeonColors.primaryAccent,
                            width: 2,
                          ),
                          boxShadow: [
                            BoxShadow(
                              color: NeonColors.primaryAccent.withValues(alpha: 0.3),
                              blurRadius: 20,
                              spreadRadius: 5,
                            ),
                          ],
                        ),
                        child: SingleChildScrollView(
                          child: Padding(
                            padding: EdgeInsets.all(padding),
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                _buildHeader(),
                                SizedBox(height: spacing),
                                _buildPerformanceRating(),
                                SizedBox(height: spacing + 4),
                                _buildScoreSection(),
                                SizedBox(height: spacing),
                                _buildXpSection(),
                                SizedBox(height: spacing),
                                _buildStatsSection(),
                                SizedBox(height: spacing),
                                _buildObjectivesSection(),
                                SizedBox(height: spacing),
                                _buildAchievementsSection(),
                                SizedBox(height: spacing + 4),
                                _buildTokensSection(),
                                SizedBox(height: spacing + 8),
                                _buildButtons(),
                              ],
                            ),
                          ),
                        ),
                      );
                    },
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Column(
      children: [
        Text(
          'LEVEL ${widget.level}',
          style: GoogleFonts.orbitron(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: NeonColors.primaryAccent,
            letterSpacing: 2,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          'COMPLETED!',
          style: GoogleFonts.orbitron(
            fontSize: 28,
            fontWeight: FontWeight.bold,
            color: NeonColors.success,
            letterSpacing: 3,
          ),
        ).animate().shimmer(
          duration: const Duration(seconds: 2),
          color: NeonColors.success.withValues(alpha: 0.5),
        ),
      ],
    );
  }

  Widget _buildPerformanceRating() {
    int stars = _calculateStars();
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
      decoration: BoxDecoration(
        color: NeonColors.background,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: NeonColors.highlights.withValues(alpha: 0.3),
        ),
      ),
      child: Column(
        children: [
          Text(
            'PERFORMANCE',
            style: GoogleFonts.orbitron(
              fontSize: 12,
              color: NeonColors.textSecondary,
              letterSpacing: 1,
            ),
          ),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: List.generate(3, (index) {
              return Padding(
                padding: const EdgeInsets.symmetric(horizontal: 2),
                child: Icon(
                  Icons.star,
                  color: index < stars ? NeonColors.highlights : NeonColors.textSecondary.withValues(alpha: 0.3),
                  size: 24,
                ).animate(delay: Duration(milliseconds: 200 * index))
                 .scale(duration: const Duration(milliseconds: 300)),
              );
            }),
          ),
        ],
      ),
    );
  }

  int _calculateStars() {
    // Calculate stars based on performance
    final accuracy = widget.stats['accuracy'] ?? 0.0;
    final timeLeft = widget.stats['timeLeft'] ?? 0;

    if (accuracy >= 90 && timeLeft > 10) return 3;
    if (accuracy >= 70 && timeLeft > 5) return 2;
    return 1;
  }

  Widget _buildXpSection() {
    return AnimatedBuilder(
      animation: _tokenController,
      builder: (context, child) {
        final animatedXp = (widget.xpEarned * _tokenController.value).round();

        return Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                NeonColors.secondaryAccent.withValues(alpha: 0.1),
                NeonColors.highlights.withValues(alpha: 0.1),
              ],
            ),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: NeonColors.secondaryAccent.withValues(alpha: 0.3),
            ),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.trending_up,
                color: NeonColors.secondaryAccent,
                size: 24,
              ),
              const SizedBox(width: 12),
              Text(
                '+$animatedXp XP EARNED',
                style: GoogleFonts.orbitron(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: NeonColors.secondaryAccent,
                  letterSpacing: 1,
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildAchievementsSection() {
    if (widget.achievementsUnlocked.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'ACHIEVEMENTS UNLOCKED',
          style: GoogleFonts.orbitron(
            fontSize: 12,
            color: NeonColors.textSecondary,
            letterSpacing: 1,
          ),
        ),
        const SizedBox(height: 8),
        ...widget.achievementsUnlocked.map((achievement) =>
          Container(
            margin: const EdgeInsets.only(bottom: 4),
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              color: NeonColors.highlights.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: NeonColors.highlights.withValues(alpha: 0.3),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.emoji_events,
                  color: NeonColors.highlights,
                  size: 16,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    achievement,
                    style: GoogleFonts.orbitron(
                      fontSize: 12,
                      color: NeonColors.highlights,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
          ).animate().slideX(duration: const Duration(milliseconds: 300)),
        ),
      ],
    );
  }

  Widget _buildScoreSection() {
    return AnimatedBuilder(
      animation: _scoreController,
      builder: (context, child) {
        final animatedScore = (widget.finalScore * _scoreController.value).round();
        
        return Container(
          padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 24),
          decoration: BoxDecoration(
            color: NeonColors.background,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: NeonColors.secondaryAccent.withValues(alpha: 0.5),
            ),
          ),
          child: Column(
            children: [
              Text(
                'FINAL SCORE',
                style: GoogleFonts.orbitron(
                  fontSize: 14,
                  color: NeonColors.textSecondary,
                  letterSpacing: 1,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                animatedScore.toString().padLeft(6, '0'),
                style: GoogleFonts.orbitron(
                  fontSize: 32,
                  fontWeight: FontWeight.bold,
                  color: NeonColors.primaryAccent,
                  letterSpacing: 2,
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildStatsSection() {
    final accuracy = widget.stats['accuracy'] ?? 0;
    final completionTime = widget.stats['completionTime'] ?? 0;
    final maxCombo = widget.stats['maxCombo'] ?? 0;
    
    return Row(
      children: [
        Expanded(child: _buildStatItem('ACCURACY', '$accuracy%')),
        Expanded(child: _buildStatItem('TIME', '${completionTime}s')),
        Expanded(child: _buildStatItem('COMBO', '$maxCombo')),
      ],
    );
  }

  Widget _buildStatItem(String label, String value) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 12),
      child: Column(
        children: [
          Text(
            label,
            style: GoogleFonts.orbitron(
              fontSize: 10,
              color: NeonColors.textSecondary,
              letterSpacing: 1,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: GoogleFonts.orbitron(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: NeonColors.textPrimary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildObjectivesSection() {
    if (widget.completedObjectives.isEmpty) {
      return const SizedBox.shrink();
    }
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'BONUS OBJECTIVES',
          style: GoogleFonts.orbitron(
            fontSize: 12,
            color: NeonColors.textSecondary,
            letterSpacing: 1,
          ),
        ),
        const SizedBox(height: 8),
        ...widget.completedObjectives.map((objective) => 
          _buildObjectiveItem(objective)
        ),
      ],
    );
  }

  Widget _buildObjectiveItem(LevelObjective objective) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          Icon(
            Icons.check_circle,
            color: NeonColors.success,
            size: 16,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              objective.name,
              style: GoogleFonts.orbitron(
                fontSize: 12,
                color: NeonColors.textPrimary,
              ),
            ),
          ),
          Text(
            '+${objective.bonusPoints}',
            style: GoogleFonts.orbitron(
              fontSize: 12,
              fontWeight: FontWeight.bold,
              color: NeonColors.success,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTokensSection() {
    return AnimatedBuilder(
      animation: _tokenController,
      builder: (context, child) {
        final animatedTokens = (widget.tokensEarned * _tokenController.value).round();
        
        return Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                NeonColors.primaryAccent.withValues(alpha: 0.1),
                NeonColors.secondaryAccent.withValues(alpha: 0.1),
              ],
            ),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: NeonColors.primaryAccent.withValues(alpha: 0.3),
            ),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.toll,
                color: NeonColors.primaryAccent,
                size: 24,
              ),
              const SizedBox(width: 12),
              Text(
                '+$animatedTokens TOKENS EARNED',
                style: GoogleFonts.orbitron(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: NeonColors.primaryAccent,
                  letterSpacing: 1,
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildButtons() {
    // Always show mystery box selection first if not used yet
    if (!_boxSelectionUsed) {
      return Column(
        children: [
          // Mystery box selection title
          Text(
            'PICK YOUR REWARD',
            style: GoogleFonts.orbitron(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: NeonColors.highlights,
              letterSpacing: 1,
            ),
          ),
          const SizedBox(height: 16),
          // Box selection
          BoxSelectionWidget(
            onBoxSelected: _handleBoxSelection,
            enabled: true,
          ),
        ],
      );
    }

    // Show navigation buttons after mystery box is used
    return Row(
      children: [
        Expanded(
          child: _buildButton(
            'REPLAY',
            Icons.replay,
            NeonColors.textSecondary,
            widget.onReplay,
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: _buildButton(
            'HOME',
            Icons.home,
            NeonColors.textSecondary,
            widget.onHome,
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          flex: 2,
          child: _buildButton(
            'NEXT LEVEL',
            Icons.arrow_forward,
            NeonColors.primaryAccent,
            widget.onNextLevel,
          ),
        ),
      ],
    );
  }

  Widget _buildButton(String text, IconData icon, Color color, VoidCallback onPressed) {
    return ElevatedButton(
      onPressed: onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor: color == NeonColors.primaryAccent 
            ? NeonColors.primaryAccent.withValues(alpha: 0.2)
            : Colors.transparent,
        foregroundColor: color,
        side: BorderSide(color: color),
        padding: const EdgeInsets.symmetric(vertical: 12),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, size: 16),
          const SizedBox(width: 8),
          Text(
            text,
            style: GoogleFonts.orbitron(
              fontSize: 12,
              fontWeight: FontWeight.bold,
              letterSpacing: 1,
            ),
          ),
        ],
      ),
    );
  }

  void _handleBoxSelection(MysteryBoxReward reward) async {
    setState(() => _boxSelectionUsed = true);

    final gameProvider = context.read<GameProvider>();

    // For now, only handle token rewards (power-up inventory system to be implemented later)
    int tokensWon = 0;
    if (reward.type == RewardType.tokens) {
      tokensWon = reward.tokens!;
      await gameProvider.earnTokens(
        tokensWon,
        TokenTransactionType.levelCompletion,
        'Level completion bonus box',
      );
    } else {
      // Convert power-up to token equivalent for now
      tokensWon = 25; // Default token value for power-ups
      await gameProvider.earnTokens(
        tokensWon,
        TokenTransactionType.levelCompletion,
        'Level completion bonus box (power-up converted)',
      );
    }

    // Show result dialog
    if (mounted) {
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          backgroundColor: NeonColors.surface,
          title: Text(
            'Bonus Reward!',
            style: GoogleFonts.orbitron(
              color: NeonColors.highlights,
              fontWeight: FontWeight.bold,
            ),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                reward.type == RewardType.tokens ? Icons.toll : Icons.card_giftcard,
                color: reward.type == RewardType.tokens ? NeonColors.primaryAccent : NeonColors.highlights,
                size: 48,
              ),
              const SizedBox(height: 16),
              Text(
                reward.type == RewardType.tokens
                    ? 'You earned ${reward.tokens} bonus tokens!'
                    : 'You earned $tokensWon tokens (power-up reward)!',
                style: GoogleFonts.orbitron(
                  color: NeonColors.textPrimary,
                  fontSize: 18,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: Text(
                'Awesome!',
                style: GoogleFonts.orbitron(color: NeonColors.primaryAccent),
              ),
            ),
          ],
        ),
      );
    }
  }
}
