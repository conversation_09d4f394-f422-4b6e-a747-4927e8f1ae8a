import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../constants.dart';
import '../models/mode_model.dart';
import 'token_balance.dart';

/// Card widget for displaying game modes with unlock status
class ModeCard extends StatelessWidget {
  final Mode mode;
  final VoidCallback? onTap;
  final VoidCallback? onUnlock;
  final VoidCallback? onDemo;

  const ModeCard({
    super.key,
    required this.mode,
    this.onTap,
    this.onUnlock,
    this.onDemo,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: mode.unlocked ? onTap : null,
      child: Container(
        margin: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: NeonColors.background,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: mode.unlocked 
                ? NeonColors.primaryAccent 
                : NeonColors.textSecondary.withValues(alpha: 0.5),
            width: 2,
          ),
          boxShadow: mode.unlocked
              ? [
                  BoxShadow(
                    color: NeonColors.primaryAccent.withValues(alpha: 0.3),
                    blurRadius: 12,
                    spreadRadius: 2,
                  ),
                ]
              : null,
        ),
        child: Stack(
          children: [
            // Background gradient overlay
            if (mode.unlocked)
              Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(14),
                  gradient: LinearGradient(
                    colors: [
                      NeonColors.primaryAccent.withValues(alpha: 0.1),
                      NeonColors.secondaryAccent.withValues(alpha: 0.05),
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                ),
              ),
            
            // Content
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Header with icon and lock status
                  Row(
                    children: [
                      Container(
                        width: 48,
                        height: 48,
                        decoration: BoxDecoration(
                          color: mode.unlocked 
                              ? NeonColors.primaryAccent.withValues(alpha: 0.2)
                              : NeonColors.textSecondary.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                            color: mode.unlocked 
                                ? NeonColors.primaryAccent 
                                : NeonColors.textSecondary,
                            width: 1,
                          ),
                        ),
                        child: Icon(
                          mode.unlocked ? Icons.play_arrow : Icons.lock,
                          color: mode.unlocked 
                              ? NeonColors.primaryAccent 
                              : NeonColors.textSecondary,
                          size: 24,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              mode.name,
                              style: TextStyle(
                                color: mode.unlocked 
                                    ? NeonColors.textPrimary 
                                    : NeonColors.textSecondary,
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                shadows: mode.unlocked
                                    ? [
                                        Shadow(
                                          color: NeonColors.primaryAccent.withValues(alpha: 0.5),
                                          blurRadius: 4,
                                        ),
                                      ]
                                    : null,
                              ),
                            ),
                            if (!mode.unlocked)
                              Row(
                                children: [
                                  Icon(
                                    Icons.stars,
                                    color: NeonColors.highlights,
                                    size: 16,
                                  ),
                                  const SizedBox(width: 4),
                                  Text(
                                    mode.cost.toString(),
                                    style: TextStyle(
                                      color: NeonColors.highlights,
                                      fontSize: 14,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ],
                              ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  
                  const SizedBox(height: 12),
                  
                  // Description
                  Text(
                    mode.description,
                    style: TextStyle(
                      color: mode.unlocked 
                          ? NeonColors.textSecondary 
                          : NeonColors.textSecondary.withValues(alpha: 0.7),
                      fontSize: 14,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  
                  const SizedBox(height: 12),
                  
                  // Features
                  if (mode.features.isNotEmpty)
                    Wrap(
                      spacing: 6,
                      runSpacing: 4,
                      children: mode.features.take(2).map((feature) {
                        return Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 4,
                          ),
                          decoration: BoxDecoration(
                            color: mode.unlocked 
                                ? NeonColors.primaryAccent.withValues(alpha: 0.2)
                                : NeonColors.textSecondary.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(
                              color: mode.unlocked 
                                  ? NeonColors.primaryAccent.withValues(alpha: 0.5)
                                  : NeonColors.textSecondary.withValues(alpha: 0.3),
                              width: 1,
                            ),
                          ),
                          child: Text(
                            feature,
                            style: TextStyle(
                              color: mode.unlocked 
                                  ? NeonColors.primaryAccent 
                                  : NeonColors.textSecondary,
                              fontSize: 10,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        );
                      }).toList(),
                    ),
                  
                  const Spacer(),
                  
                  // Action buttons
                  if (mode.unlocked)
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: onTap,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.transparent,
                          foregroundColor: NeonColors.primaryAccent,
                          side: BorderSide(
                            color: NeonColors.primaryAccent,
                            width: 2,
                          ),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        child: const Text('PLAY'),
                      ),
                    )
                  else
                    Row(
                      children: [
                        Expanded(
                          child: TokenPurchaseButton(
                            label: 'UNLOCK',
                            cost: mode.cost,
                            onPressed: onUnlock,
                            icon: Icons.lock_open,
                          ),
                        ),
                        const SizedBox(width: 8),
                        OutlinedButton(
                          onPressed: onDemo,
                          style: OutlinedButton.styleFrom(
                            foregroundColor: NeonColors.textSecondary,
                            side: BorderSide(
                              color: NeonColors.textSecondary,
                              width: 1,
                            ),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                          child: const Text('DEMO'),
                        ),
                      ],
                    ),
                ],
              ),
            ),
            
            // Locked overlay
            if (!mode.unlocked)
              Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(14),
                  color: NeonColors.background.withValues(alpha: 0.7),
                ),
                child: const Center(
                  child: Icon(
                    Icons.lock,
                    color: NeonColors.textSecondary,
                    size: 48,
                  ),
                ),
              ),
          ],
        ),
      ),
    )
        .animate()
        .fadeIn(duration: const Duration(milliseconds: 300))
        .slideY(
          begin: 0.2,
          end: 0,
          duration: const Duration(milliseconds: 400),
          curve: Curves.easeOut,
        );
  }
}

/// Compact mode card for horizontal lists
class CompactModeCard extends StatelessWidget {
  final Mode mode;
  final VoidCallback? onTap;

  const CompactModeCard({
    super.key,
    required this.mode,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: mode.unlocked ? onTap : null,
      child: Container(
        width: 120,
        margin: const EdgeInsets.only(right: 12),
        decoration: BoxDecoration(
          color: NeonColors.background,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: mode.unlocked 
                ? NeonColors.primaryAccent 
                : NeonColors.textSecondary.withValues(alpha: 0.5),
            width: 2,
          ),
          boxShadow: mode.unlocked
              ? [
                  BoxShadow(
                    color: NeonColors.primaryAccent.withValues(alpha: 0.3),
                    blurRadius: 8,
                    spreadRadius: 1,
                  ),
                ]
              : null,
        ),
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                mode.unlocked ? Icons.play_arrow : Icons.lock,
                color: mode.unlocked 
                    ? NeonColors.primaryAccent 
                    : NeonColors.textSecondary,
                size: 32,
              ),
              const SizedBox(height: 8),
              Text(
                mode.name,
                style: TextStyle(
                  color: mode.unlocked 
                      ? NeonColors.textPrimary 
                      : NeonColors.textSecondary,
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              if (!mode.unlocked) ...[
                const SizedBox(height: 4),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.stars,
                      color: NeonColors.highlights,
                      size: 12,
                    ),
                    const SizedBox(width: 2),
                    Text(
                      mode.cost.toString(),
                      style: TextStyle(
                        color: NeonColors.highlights,
                        fontSize: 10,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
}
