import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

import '../constants.dart';
import '../services/spin_wheel_service.dart';

/// Interactive spin wheel widget for bonus rewards
class SpinWheelWidget extends StatefulWidget {
  final Function(SpinResult) onSpinComplete;
  final bool enabled;

  const SpinWheelWidget({
    super.key,
    required this.onSpinComplete,
    this.enabled = true,
  });

  @override
  State<SpinWheelWidget> createState() => _SpinWheelWidgetState();
}

class _SpinWheelWidgetState extends State<SpinWheelWidget>
    with TickerProviderStateMixin {
  late AnimationController _rotationController;
  late AnimationController _glowController;
  late Animation<double> _rotationAnimation;
  late Animation<double> _glowAnimation;
  
  bool _isSpinning = false;
  double _finalRotation = 0;

  final List<WheelSegment> _segments = [
    WheelSegment(tokens: 10, color: NeonColors.neonGreen, label: '10'),
    WheelSegment(tokens: 25, color: NeonColors.electricBlue, label: '25'),
    WheelSegment(tokens: 5, color: NeonColors.textSecondary, label: '5'),
    WheelSegment(tokens: 50, color: NeonColors.highlights, label: '50'),
    WheelSegment(tokens: 15, color: NeonColors.hotPink, label: '15'),
    WheelSegment(tokens: 100, color: NeonColors.secondaryAccent, label: '100'),
    WheelSegment(tokens: 20, color: NeonColors.purpleGlow, label: '20'),
    WheelSegment(tokens: 75, color: NeonColors.orangeFlame, label: '75'),
  ];

  @override
  void initState() {
    super.initState();
    
    _rotationController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    );
    
    _glowController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );
    
    _rotationAnimation = Tween<double>(
      begin: 0,
      end: 1,
    ).animate(CurvedAnimation(
      parent: _rotationController,
      curve: Curves.easeOut,
    ));
    
    _glowAnimation = Tween<double>(
      begin: 0.5,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _glowController,
      curve: Curves.easeInOut,
    ));
    
    _glowController.repeat(reverse: true);
  }

  @override
  void dispose() {
    _rotationController.dispose();
    _glowController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Spin wheel
          Stack(
            alignment: Alignment.center,
            children: [
              // Wheel
              AnimatedBuilder(
                animation: _rotationAnimation,
                builder: (context, child) {
                  return Transform.rotate(
                    angle: _rotationAnimation.value * _finalRotation,
                    child: Container(
                      width: 250,
                      height: 250,
                      child: CustomPaint(
                        painter: SpinWheelPainter(_segments, _glowAnimation),
                        size: const Size(250, 250),
                      ),
                    ),
                  );
                },
              ),
              
              // Center button
              GestureDetector(
                onTap: widget.enabled && !_isSpinning ? _spin : null,
                child: AnimatedBuilder(
                  animation: _glowAnimation,
                  builder: (context, child) {
                    return Container(
                      width: 60,
                      height: 60,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: NeonColors.primaryAccent,
                        border: Border.all(
                          color: NeonColors.primaryAccent.withValues(alpha: _glowAnimation.value),
                          width: 3,
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: NeonColors.primaryAccent.withValues(alpha: _glowAnimation.value * 0.5),
                            blurRadius: 15,
                            spreadRadius: 3,
                          ),
                        ],
                      ),
                      child: Icon(
                        _isSpinning ? Icons.hourglass_empty : Icons.play_arrow,
                        color: NeonColors.background,
                        size: 30,
                      ),
                    );
                  },
                ),
              ),
              
              // Pointer
              Positioned(
                top: 10,
                child: Container(
                  width: 0,
                  height: 0,
                  decoration: const BoxDecoration(
                    border: Border(
                      left: BorderSide(width: 10, color: Colors.transparent),
                      right: BorderSide(width: 10, color: Colors.transparent),
                      bottom: BorderSide(width: 20, color: NeonColors.textPrimary),
                    ),
                  ),
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 24),
          
          // Instructions
          Text(
            _isSpinning ? 'Spinning...' : 'Tap to spin!',
            style: GoogleFonts.orbitron(
              color: NeonColors.textPrimary,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  void _spin() async {
    if (_isSpinning) return;
    
    setState(() => _isSpinning = true);
    
    // Generate random result
    final random = math.Random();
    final segmentIndex = random.nextInt(_segments.length);
    final selectedSegment = _segments[segmentIndex];
    
    // Calculate rotation needed to land on selected segment
    final segmentAngle = (2 * math.pi) / _segments.length;
    final targetAngle = (segmentIndex * segmentAngle) + (segmentAngle / 2);
    
    // Add multiple full rotations for effect
    final fullRotations = 3 + random.nextDouble() * 2; // 3-5 rotations
    _finalRotation = (fullRotations * 2 * math.pi) + (2 * math.pi - targetAngle);
    
    // Start spinning animation
    await _rotationController.forward();
    
    // Create result
    final result = SpinResult(
      isWin: true,
      tokensWon: selectedSegment.tokens,
      bonusGameKeysWon: 0,
      label: selectedSegment.label,
      segmentIndex: segmentIndex,
      type: SpinResultType.tokens,
      wasFree: true, // This widget is used for demo purposes
    );
    
    setState(() => _isSpinning = false);
    
    // Notify parent
    widget.onSpinComplete(result);
    
    // Reset for next spin
    _rotationController.reset();
  }
}

/// Custom painter for the spin wheel
class SpinWheelPainter extends CustomPainter {
  final List<WheelSegment> segments;
  final Animation<double> glowAnimation;

  SpinWheelPainter(this.segments, this.glowAnimation) : super(repaint: glowAnimation);

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width / 2;
    final segmentAngle = (2 * math.pi) / segments.length;

    for (int i = 0; i < segments.length; i++) {
      final segment = segments[i];
      final startAngle = i * segmentAngle - (math.pi / 2);
      
      // Draw segment
      final paint = Paint()
        ..color = segment.color.withValues(alpha: 0.8)
        ..style = PaintingStyle.fill;
      
      canvas.drawArc(
        Rect.fromCircle(center: center, radius: radius),
        startAngle,
        segmentAngle,
        true,
        paint,
      );
      
      // Draw border
      final borderPaint = Paint()
        ..color = NeonColors.textPrimary.withValues(alpha: 0.3)
        ..style = PaintingStyle.stroke
        ..strokeWidth = 2;
      
      canvas.drawArc(
        Rect.fromCircle(center: center, radius: radius),
        startAngle,
        segmentAngle,
        true,
        borderPaint,
      );
      
      // Draw text
      final textAngle = startAngle + (segmentAngle / 2);
      final textRadius = radius * 0.7;
      final textX = center.dx + textRadius * math.cos(textAngle);
      final textY = center.dy + textRadius * math.sin(textAngle);
      
      final textPainter = TextPainter(
        text: TextSpan(
          text: segment.label,
          style: TextStyle(
            color: NeonColors.background,
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        textDirection: TextDirection.ltr,
      );
      
      textPainter.layout();
      textPainter.paint(
        canvas,
        Offset(
          textX - textPainter.width / 2,
          textY - textPainter.height / 2,
        ),
      );
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

/// Represents a segment of the spin wheel
class WheelSegment {
  final int tokens;
  final Color color;
  final String label;

  const WheelSegment({
    required this.tokens,
    required this.color,
    required this.label,
  });
}
