import 'dart:math';
import 'package:flutter/material.dart';

import '../models/target.dart';

/// Widget that renders a v2.0 game target with animations and effects
class TargetWidget extends StatefulWidget {
  final GameTarget target;
  final VoidCallback? onTap;
  final bool enableAnimations;

  const TargetWidget({
    super.key,
    required this.target,
    this.onTap,
    this.enableAnimations = true,
  });

  @override
  State<TargetWidget> createState() => _TargetWidgetState();
}

class _TargetWidgetState extends State<TargetWidget>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late AnimationController _rotationController;
  late AnimationController _scaleController;
  
  late Animation<double> _pulseAnimation;
  late Animation<double> _rotationAnimation;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    if (widget.enableAnimations) {
      _startAnimations();
    }
  }

  void _initializeAnimations() {
    // Pulse animation for basic targets
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _pulseAnimation = Tween<double>(
      begin: 0.9,
      end: 1.1,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));

    // Rotation animation for special targets
    _rotationController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );
    _rotationAnimation = Tween<double>(
      begin: 0.0,
      end: 2 * pi,
    ).animate(CurvedAnimation(
      parent: _rotationController,
      curve: Curves.linear,
    ));

    // Scale animation for dynamic targets
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: Curves.easeInOut,
    ));
  }

  void _startAnimations() {
    switch (widget.target.type) {
      case TargetType.easyBall:
        _pulseController.repeat(reverse: true);
        break;
      case TargetType.mediumBall:
        _pulseController.repeat(reverse: true);
        break;
      case TargetType.hardBall:
        _pulseController.repeat(reverse: true);
        _rotationController.repeat(); // Hard balls rotate
        break;
      case TargetType.redEnemy:
        _pulseController.repeat(reverse: true);
        _scaleController.repeat(reverse: true); // Red enemies pulse scale
        break;
      case TargetType.yellowEnemy:
        _pulseController.repeat(reverse: true);
        break;
    }
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _rotationController.dispose();
    _scaleController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final targetSize = widget.target.getCurrentSize();
    return Positioned(
      left: widget.target.position.dx - targetSize / 2,
      top: widget.target.position.dy - targetSize / 2,
      child: GestureDetector(
        onTap: widget.onTap,
        child: AnimatedBuilder(
          animation: Listenable.merge([
            _pulseController,
            _rotationController,
            _scaleController,
          ]),
          builder: (context, child) {
            return Transform.scale(
              scale: _scaleAnimation.value * 
                     (widget.enableAnimations ? _pulseAnimation.value : 1.0),
              child: Transform.rotate(
                angle: widget.target.rotation + 
                       (widget.enableAnimations ? _rotationAnimation.value : 0.0),
                child: Opacity(
                  opacity: widget.target.opacity,
                  child: _buildTargetContent(),
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildTargetContent() {
    switch (widget.target.type) {
      case TargetType.easyBall:
        return _buildEasyBall();
      case TargetType.mediumBall:
        return _buildMediumBall();
      case TargetType.hardBall:
        return _buildHardBall();
      case TargetType.redEnemy:
        return _buildRedEnemy();
      case TargetType.yellowEnemy:
        return _buildYellowEnemy();
    }
  }

  Widget _buildEasyBall() {
    final targetSize = widget.target.getCurrentSize();
    final displayColor = widget.target.getDisplayColor();
    
    return Container(
      width: targetSize,
      height: targetSize,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: displayColor,
        border: Border.all(
          color: Colors.white,
          width: 2.0,
        ),
        boxShadow: [
          BoxShadow(
            color: displayColor.withValues(alpha: 0.6 * widget.target.glowIntensity),
            blurRadius: 10.0 * widget.target.glowIntensity,
            spreadRadius: 2.0 * widget.target.glowIntensity,
          ),
        ],
      ),
      child: Center(
        child: Text(
          '${widget.target.getScore()}',
          style: const TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
            fontSize: 16,
          ),
        ),
      ),
    );
  }

  Widget _buildMediumBall() {
    final targetSize = widget.target.getCurrentSize();
    final displayColor = widget.target.getDisplayColor();
    
    return Container(
      width: targetSize,
      height: targetSize,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: displayColor,
        border: Border.all(
          color: Colors.white,
          width: 3.0,
        ),
        boxShadow: [
          BoxShadow(
            color: displayColor.withValues(alpha: 0.8 * widget.target.glowIntensity),
            blurRadius: 15.0 * widget.target.glowIntensity,
            spreadRadius: 3.0 * widget.target.glowIntensity,
          ),
        ],
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.arrow_downward,
              color: Colors.white,
              size: 20,
            ),
            Text(
              '${widget.target.getScore()}',
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                fontSize: 14,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHardBall() {
    final targetSize = widget.target.getCurrentSize();
    final displayColor = widget.target.getDisplayColor();
    
    return Container(
      width: targetSize,
      height: targetSize,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: displayColor,
        border: Border.all(
          color: Colors.white,
          width: 4.0,
        ),
        boxShadow: [
          BoxShadow(
            color: displayColor.withValues(alpha: 1.0 * widget.target.glowIntensity),
            blurRadius: 20.0 * widget.target.glowIntensity,
            spreadRadius: 4.0 * widget.target.glowIntensity,
          ),
        ],
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.scatter_plot,
              color: Colors.white,
              size: 24,
            ),
            Text(
              '${widget.target.getScore()}',
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                fontSize: 12,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRedEnemy() {
    final targetSize = widget.target.getCurrentSize();
    final displayColor = widget.target.getDisplayColor();
    
    return Container(
      width: targetSize,
      height: targetSize,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: displayColor,
        border: Border.all(
          color: Colors.orange,
          width: 3.0,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.red.withValues(alpha: 0.9 * widget.target.glowIntensity),
            blurRadius: 25.0 * widget.target.glowIntensity,
            spreadRadius: 5.0 * widget.target.glowIntensity,
          ),
        ],
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              widget.target.icon ?? Icons.warning,
              color: Colors.white,
              size: 28,
            ),
            Text(
              '${widget.target.penalty}',
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                fontSize: 12,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildYellowEnemy() {
    final targetSize = widget.target.getCurrentSize();
    final displayColor = widget.target.getDisplayColor();
    
    return Container(
      width: targetSize,
      height: targetSize,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: displayColor,
        border: Border.all(
          color: Colors.orange,
          width: 3.0,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.yellow.withValues(alpha: 0.8 * widget.target.glowIntensity),
            blurRadius: 20.0 * widget.target.glowIntensity,
            spreadRadius: 4.0 * widget.target.glowIntensity,
          ),
        ],
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              widget.target.icon ?? Icons.timer,
              color: Colors.black,
              size: 24,
            ),
            Text(
              '${widget.target.penalty}s',
              style: const TextStyle(
                color: Colors.black,
                fontWeight: FontWeight.bold,
                fontSize: 10,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
