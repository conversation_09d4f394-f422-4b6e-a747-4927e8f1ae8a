import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../constants.dart';
import '../providers/game_provider.dart';

/// Widget that displays the current token balance with neon styling
class TokenBalance extends StatelessWidget {
  final bool showLabel;
  final double fontSize;
  final bool animated;

  const TokenBalance({
    super.key,
    this.showLabel = true,
    this.fontSize = 18,
    this.animated = true,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<GameProvider>(
      builder: (context, gameProvider, child) {
        final tokens = gameProvider.tokens;
        
        Widget tokenWidget = Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: BoxDecoration(
            color: NeonColors.background.withValues(alpha: 0.8),
            border: Border.all(
              color: NeonColors.highlights,
              width: 2,
            ),
            borderRadius: BorderRadius.circular(20),
            boxShadow: [
              BoxShadow(
                color: NeonColors.highlights.withValues(alpha: 0.3),
                blurRadius: 8,
                spreadRadius: 1,
              ),
            ],
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.stars,
                color: NeonColors.highlights,
                size: fontSize + 2,
                shadows: [
                  Shadow(
                    color: NeonColors.highlights.withValues(alpha: 0.8),
                    blurRadius: 4,
                  ),
                ],
              ),
              const SizedBox(width: 6),
              Text(
                tokens.toString(),
                style: TextStyle(
                  color: NeonColors.textPrimary,
                  fontSize: fontSize,
                  fontWeight: FontWeight.bold,
                  shadows: [
                    Shadow(
                      color: NeonColors.highlights.withValues(alpha: 0.6),
                      blurRadius: 6,
                    ),
                  ],
                ),
              ),
              if (showLabel) ...[
                const SizedBox(width: 4),
                Text(
                  'Tokens',
                  style: TextStyle(
                    color: NeonColors.textSecondary,
                    fontSize: fontSize - 2,
                  ),
                ),
              ],
            ],
          ),
        );

        if (animated) {
          return tokenWidget
              .animate(key: ValueKey(tokens))
              .scale(
                duration: const Duration(milliseconds: 200),
                begin: const Offset(0.8, 0.8),
                end: const Offset(1.0, 1.0),
                curve: Curves.elasticOut,
              )
              .shimmer(
                duration: const Duration(milliseconds: 500),
                color: NeonColors.highlights.withValues(alpha: 0.3),
              );
        }

        return tokenWidget;
      },
    );
  }
}

/// Animated token counter for earning/spending feedback
class AnimatedTokenCounter extends StatefulWidget {
  final int startValue;
  final int endValue;
  final Duration duration;
  final VoidCallback? onComplete;

  const AnimatedTokenCounter({
    super.key,
    required this.startValue,
    required this.endValue,
    this.duration = const Duration(milliseconds: 1000),
    this.onComplete,
  });

  @override
  State<AnimatedTokenCounter> createState() => _AnimatedTokenCounterState();
}

class _AnimatedTokenCounterState extends State<AnimatedTokenCounter>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<int> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(duration: widget.duration, vsync: this);
    _animation = IntTween(
      begin: widget.startValue,
      end: widget.endValue,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeOutCubic,
    ));

    _controller.forward().then((_) {
      widget.onComplete?.call();
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Text(
          _animation.value.toString(),
          style: TextStyle(
            color: NeonColors.highlights,
            fontSize: 24,
            fontWeight: FontWeight.bold,
            shadows: [
              Shadow(
                color: NeonColors.highlights.withValues(alpha: 0.8),
                blurRadius: 8,
              ),
            ],
          ),
        );
      },
    );
  }
}

/// Token change indicator (shows +/- tokens)
class TokenChangeIndicator extends StatelessWidget {
  final int amount;
  final bool isPositive;
  final String? label;

  const TokenChangeIndicator({
    super.key,
    required this.amount,
    required this.isPositive,
    this.label,
  });

  @override
  Widget build(BuildContext context) {
    final color = isPositive ? NeonColors.neonGreen : NeonColors.hotPink;
    final sign = isPositive ? '+' : '-';

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.2),
        border: Border.all(color: color, width: 1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            isPositive ? Icons.add : Icons.remove,
            color: color,
            size: 16,
          ),
          const SizedBox(width: 2),
          Text(
            '$sign$amount',
            style: TextStyle(
              color: color,
              fontSize: 14,
              fontWeight: FontWeight.bold,
            ),
          ),
          if (label != null) ...[
            const SizedBox(width: 4),
            Text(
              label!,
              style: TextStyle(
                color: color.withValues(alpha: 0.8),
                fontSize: 12,
              ),
            ),
          ],
        ],
      ),
    )
        .animate()
        .slideY(
          begin: 1,
          end: 0,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        )
        .fadeIn(duration: const Duration(milliseconds: 200))
        .then(delay: const Duration(milliseconds: 2000))
        .slideY(
          begin: 0,
          end: -1,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeIn,
        )
        .fadeOut(duration: const Duration(milliseconds: 200));
  }
}

/// Token purchase button with cost display
class TokenPurchaseButton extends StatelessWidget {
  final String label;
  final int cost;
  final VoidCallback? onPressed;
  final bool enabled;
  final IconData? icon;

  const TokenPurchaseButton({
    super.key,
    required this.label,
    required this.cost,
    this.onPressed,
    this.enabled = true,
    this.icon,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<GameProvider>(
      builder: (context, gameProvider, child) {
        final canAfford = gameProvider.tokens >= cost;
        final isEnabled = enabled && canAfford;

        return ElevatedButton(
          onPressed: isEnabled ? onPressed : null,
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.transparent,
            foregroundColor: isEnabled 
                ? NeonColors.primaryAccent 
                : NeonColors.textSecondary,
            side: BorderSide(
              color: isEnabled 
                  ? NeonColors.primaryAccent 
                  : NeonColors.textSecondary,
              width: 2,
            ),
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              if (icon != null) ...[
                Icon(icon, size: 20),
                const SizedBox(width: 8),
              ],
              Text(label),
              const SizedBox(width: 12),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: NeonColors.highlights.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.stars,
                      color: NeonColors.highlights,
                      size: 14,
                    ),
                    const SizedBox(width: 2),
                    Text(
                      cost.toString(),
                      style: TextStyle(
                        color: NeonColors.highlights,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
