import 'package:flutter_test/flutter_test.dart';
import 'package:flutter/material.dart';
import 'package:neon_rush/models/breakfinity_progress.dart';
import 'package:neon_rush/models/breakfinity_section.dart';

void main() {
  group('Breakfinity Damage Tests', () {
    test('BreakfinityProgress should have correct default base damage', () {
      final progress = BreakfinityProgress(lastPlayedAt: DateTime.now());
      
      expect(progress.baseDamage, equals(5));
      expect(progress.getEffectiveDamage(), equals(5));
    });

    test('Section health should be reasonable for early layers', () {
      // Test layer 1 sections
      final normalSection = SectionFactory.generateSection(
        layer: 1,
        sectionIndex: 0,
        position: const Offset(0, 0),
        size: const Size(50, 50),
      );

      final weakSection = SectionFactory.generateSection(
        layer: 1,
        sectionIndex: 1,
        position: const Offset(50, 0),
        size: const Size(50, 50),
      );
      
      // Layer 1 base health should be 10 + (1 * 2) = 12
      // Section types are randomly generated, so just check they have reasonable health
      expect(normalSection.maxHealth, greaterThan(0));
      expect(weakSection.maxHealth, greaterThan(0));
      expect(normalSection.maxHealth, lessThanOrEqualTo(24)); // Max for reinforced: 12 * 2
      expect(weakSection.maxHealth, lessThanOrEqualTo(24));
      
      print('Normal section health: ${normalSection.maxHealth}');
      print('Weak section health: ${weakSection.maxHealth}');
    });

    test('Damage calculation should break sections in reasonable time', () {
      final progress = BreakfinityProgress(lastPlayedAt: DateTime.now());
      final section = SectionFactory.generateSection(
        layer: 1,
        sectionIndex: 0,
        position: const Offset(0, 0),
        size: const Size(50, 50),
      );
      
      final baseDamage = progress.getEffectiveDamage();
      final sectionDamage = section.calculateDamage(baseDamage, progress.powerUps);
      final tapsToBreak = (section.maxHealth / sectionDamage).ceil();
      
      print('Base damage: $baseDamage');
      print('Section damage: $sectionDamage');
      print('Section health: ${section.maxHealth}');
      print('Taps to break: $tapsToBreak');
      
      // Should take no more than 5 taps to break any section on layer 1 with base damage 5
      expect(tapsToBreak, lessThanOrEqualTo(5));
    });

    test('Damage multipliers work correctly', () {
      final progress = BreakfinityProgress(
        lastPlayedAt: DateTime.now(),
        baseDamage: 5,
        tapMultiplier: 2.0,
      );
      
      expect(progress.getEffectiveDamage(), equals(10));
    });

    test('Power-up effects work correctly', () {
      final progress = BreakfinityProgress(
        lastPlayedAt: DateTime.now(),
        baseDamage: 5,
        powerUps: {'mega_tap': 1},
      );
      
      // Mega tap should multiply by 5
      expect(progress.getEffectiveDamage(), equals(25));
    });
  });
}
